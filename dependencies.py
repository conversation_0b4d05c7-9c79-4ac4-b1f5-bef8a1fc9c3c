from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Depends, WebSocket
from services import N1ProcessPipeline  # Adjust the import path as necessary
from ws_service import ConnectionManager  # Adjust the import path as necessary
from config import N1_API_KEY, N1_API_HEADER_NAME, USERNAME, PASSWORD
from fastapi.security import <PERSON><PERSON>PBasic, HTTPBasicCredentials, APIKeyHeader
import secrets

# Singleton instance of N1ProcessPipeline
CONNECTION_MANAGER = ConnectionManager()
PIPELINE_INSTANCE: N1ProcessPipeline = N1ProcessPipeline(CONNECTION_MANAGER)

# Set up HTTP Basic Auth
security = HTTPBasic()

# Set up API Key Auth
api_key_header = APIKeyHeader(name=N1_API_HEADER_NAME, auto_error=True, scheme_name="ApiKeyAuth", description="API key authentication")


def verify_credentials(credentials: HTTPBasicCredentials = Depends(security)):
    """
    Verify the username and password provided in the HTTP Basic Auth.

    Args:
        credentials: The HTTP Basic Auth credentials

    Raises:
        HTTPException: If the credentials are invalid

    Returns:
        str: The username if credentials are valid
    """
    correct_username = secrets.compare_digest(credentials.username, USERNAME)
    correct_password = secrets.compare_digest(credentials.password, PASSWORD)

    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username


async def verify_api_key(api_key: str = Depends(api_key_header)):
    """
    Dependency function to verify the API key provided in the header.

    Args:
        api_key: The API key provided in the N1-Api-Key header

    Raises:
        HTTPException: If the API key is invalid

    Returns:
        str: The API key if valid
    """
    if api_key != N1_API_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "ApiKey"},
        )
    return api_key


async def verify_api_key_websocket(websocket: WebSocket):
    """
    Dependency function to verify the API key for WebSocket connections.

    Args:
        websocket: The WebSocket connection

    Raises:
        WebSocketException: If the API key is invalid

    Returns:
        str: The API key if valid
    """
    query_params = dict(websocket.query_params)
    api_key = query_params.get('api_key')
    if api_key != N1_API_KEY:
        await websocket.close(code=1008, reason="Invalid API key")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    return api_key


def get_pipeline() -> N1ProcessPipeline:
    """
    Returns a singleton instance of N1ProcessPipeline.

    Returns:
        N1ProcessPipeline: The singleton instance of the pipeline
    """
    return PIPELINE_INSTANCE

def get_connection_manager() -> ConnectionManager:
    """
    Returns the singleton instance of ConnectionManager.

    Returns:
        ConnectionManager: The singleton instance of the connection manager
    """
    return CONNECTION_MANAGER