import config
import os
import aioboto3
import logging
from botocore.exceptions import ClientError


logger = logging.getLogger(__name__)

async def launch_data_processing_job_aws(user_id: str, record_ids: list, bucket_name: str, parser:dict):
    """
    Asynchronously launches multiple AWS Batch jobs for report generation,
    each job for a single record ID.

    Args:
        user_id: The USER_ID value to override.
        record_ids: List of record IDs to process.
        bucket_name: The name of the user's bucket.
        parser: dict with parser job details
        parser_model: The AI model to use for parsing (optional, uses default if not provided)
    """

    job_queue = parser["job_queue"]
    job_definition = parser["job_definition"]

    for record_id in record_ids:
        job_name = f"data-processing-sequential--{user_id}--{record_id}"

        env_overrides = {
            "USER_ID": user_id,
            "BUCKET_NAME": bucket_name,
            "DB_HOST": config.DB_HOST_PUBLIC,
            "N1_API_HEADER": config.N1_API_HEADER_NAME,
            "N1_API_KEY": config.N1_API_KEY,
            "N1_API_RECORD_STATUS_URL": f"{config.N1_API_BASE_URL}/records/status",
            "N1_API_SYNC_BIOMARKERS_URL": f"{config.N1_API_BASE_URL}/records/sync-biomarkers",
            "OPENAI_API_KEY": config.OPENAI_API_KEY,
            "OPENAI_BASE_URL": config.OPENAI_BASE_URL,
            "DB_USERNAME": config.DB_USERNAME,
            "DB_PASSWORD": config.DB_PASSWORD,
            "DATABASE_NAME": config.DATABASE_NAME,
            "BIOMARKERS_TABLE_NAME": config.BIOMARKERS_TABLE_NAME,
            "MODEL_NAME": parser.get("model") or config.MODEL,
            "RECORDS_TABLE_NAME": config.RECORDS_TABLE_NAME,
            "FILE_NAME_COLUMN": config.FILE_NAME_COLUMN,
        }

        # Convert the env_overrides dict into the list format required by AWS Batch
        environment = [{"name": key, "value": str(value)} for key, value in env_overrides.items()]

        # Override the container command to include the record_id argument.
        # This makes the command run as: python process_record.py <record_id>
        command_override = ["python", "process_record_aws.py", record_id]

        # Create an aioboto3 session using the AWS_REGION from your environment
        session = aioboto3.Session(aws_access_key_id=config.AWS_ACCESS_KEY, aws_secret_access_key=config.AWS_SECRET_ACCESS_KEY, region_name=parser["region"])

        try:
            async with session.client("batch") as batch_client:
                response = await batch_client.submit_job(
                    jobName=job_name,
                    jobQueue=job_queue,
                    jobDefinition=job_definition,
                    containerOverrides={
                        "environment": environment,
                        "command": command_override
                    },
                    parameters={"record_id": record_id},  # Optional: also pass record_id as a parameter
                )
                job_id = response["jobId"]
                logger.info(f"Launched AWS Batch job {job_name} with job ID {job_id}")
                logger.info(f"Request variables: USER_ID={user_id}, BUCKET_NAME={bucket_name}, record_id={record_id}")
        except ClientError as e:
            logger.error(f"Failed to launch AWS Batch job: {str(e)}")
            raise RuntimeError(f"Failed to launch job: {str(e)}")
