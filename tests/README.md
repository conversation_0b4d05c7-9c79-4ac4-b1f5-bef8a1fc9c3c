# N1 API Testing Suite

This directory contains the testing suite for the N1 API. The tests are organized into different categories and use pytest as the testing framework.

## Testing Strategy

The testing strategy follows these principles:

1. **API/Integration Tests**: Using FastAPI's TestClient to test the API endpoints
2. **Extensive Pytest Fixtures**: Reusable fixtures for database, authentication, and mocking
3. **Mocked Storage**: The `storage.py` module is mocked to avoid using actual cloud services
4. **SQLite for Testing**: Using SQLite in-memory database for testing instead of the production database
5. **Isolated Tests**: Each test runs in isolation with its own database state
6. **Patched Environment**: Environment variables and configuration are patched for testing

## Directory Structure

```
tests/
├── conftest.py           # Shared pytest fixtures
├── run_tests.py          # Test runner script
├── integration/          # Integration tests
│   ├── test_main.py      # Tests for the main application
│   ├── test_records_routes.py  # Tests for the records routes
│   ├── test_reports_routes.py  # Tests for the reports routes
│   └── test_users_routes.py    # Tests for the users routes
└── unit/                 # Unit tests
    ├── test_dependencies.py    # Tests for the dependencies module
    ├── test_services.py        # Tests for the services module
    ├── test_storage.py         # Tests for the storage module
    └── test_storage_mock.py    # Tests for the storage service mock
```

## Test Categories

### Unit Tests

Unit tests focus on testing individual components in isolation. They use mocks to avoid external dependencies.

- **Storage Tests**: Test the `FileStorageService` class with mocked Google Cloud Storage
- **Storage Mock Tests**: Verify the storage service mock is properly configured
- **Services Tests**: Test the `N1ProcessPipeline` class with mocked storage and database
- **Dependencies Tests**: Test the authentication and dependency injection functions

### Integration Tests

Integration tests focus on testing the API endpoints and how components work together.

- **Main Tests**: Test the main application, including authentication middleware
- **Routes Tests**: Test the API routes for users, records, and reports
- **Database Integration**: Test how the application interacts with the database

## Running Tests

You can run the tests using the provided `run_tests.py` script:

```bash
# Run all tests
python tests/run_tests.py

# Run only unit tests
python tests/run_tests.py --unit

# Run only integration tests
python tests/run_tests.py --integration

# Run with coverage report
python tests/run_tests.py --coverage

# Run a specific test file
python tests/run_tests.py --file tests/unit/test_storage.py
```

Or you can use pytest directly:

```bash
# Run all tests
pytest

# Run only unit tests
pytest -m unit

# Run only integration tests
pytest -m integration

# Run with coverage report
pytest --cov=. --cov-report=term --cov-report=html
```

## Key Fixtures

The testing suite provides several key fixtures:

- **engine**: SQLAlchemy engine for the test database
- **db_session**: Session for the test database
- **client**: FastAPI TestClient for making requests
- **auth_client**: TestClient with authentication headers
- **mock_storage_service**: Mock for the FileStorageService
- **mock_n1_process_pipeline**: Mock for the N1ProcessPipeline with real instance but mocked methods
- **mock_user**: Mock user in the database
- **mock_record**: Mock record in the database
- **mock_report**: Mock report in the database
- **mock_clinical_data**: Mock clinical data in the database
- **api_key_header**: Header with a valid API key for testing

## Mocking Strategy

The testing suite uses the following mocking strategy:

1. **Storage Service**: The `FileStorageService` is mocked to avoid actual cloud operations
   - `mock_storage_service` fixture provides a pre-configured mock with common methods
   - `test_storage_mock.py` verifies the mock is properly configured
2. **Background Tasks**: FastAPI background tasks are mocked to run synchronously
3. **External APIs**: External API calls (like Bubble API) are mocked
4. **Database**: The database is replaced with an in-memory SQLite database
5. **N1ProcessPipeline**: A real instance is created but methods that interact with external systems are mocked
6. **Async Methods**: AsyncMock is used for mocking asynchronous methods

## SQLite Configuration

The test suite uses SQLite in-memory database instead of PostgreSQL for testing. This is configured in conftest.py:

1. **Environment Variables**: Sets the DATABASE_URL environment variable to use SQLite
2. **Config Override**: Directly overrides config.DATABASE_URL to use SQLite
3. **Engine Override**: Replaces the database engine with a SQLite engine
4. **SessionLocal Override**: Updates the SessionLocal to use the SQLite engine
5. **Test Fixtures**: Configures engine and db_session fixtures to use SQLite
6. **Custom Dialect**: Uses a custom SQLite dialect (`sqlite_dialect.py`) to handle PostgreSQL-specific syntax

This approach ensures that tests run in isolation without requiring a PostgreSQL database, and without modifying the actual application code.

## Troubleshooting

If you encounter database connection issues:

1. Make sure the DATABASE_URL environment variable is set to "sqlite:///:memory:" when running tests
2. Check that the conftest.py file is properly overriding the database engine
3. Ensure that the test fixtures are being used correctly in the tests
4. Try running a single test file to isolate the issue: `python tests/run_tests.py --file tests/unit/test_dependencies.py`

## Best Practices

When writing tests, follow these best practices:

1. **Use fixtures**: Reuse fixtures to avoid duplicating setup code
2. **Clean up**: Ensure tests clean up after themselves
3. **Isolation**: Tests should not depend on each other
4. **Mocking**: Mock external dependencies to avoid side effects
5. **Assertions**: Make specific assertions about the expected behavior
6. **Coverage**: Aim for high test coverage, especially for critical paths
