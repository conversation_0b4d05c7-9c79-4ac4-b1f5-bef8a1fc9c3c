# How to test lamgroid with n1 api:

1.  Run the local `n1-api` server by running:

    ```bash
    uv run uvicorn main:app --reload --port 8080
    ```

    _(ensure to source `env.sh` before running)_

On the langroid repo, run the following command:

2.  Set up the postgres db as mentioned in `Readme.md` on langroid repo (see point 5 in README.md).
    - Difference being: set `SYNC_WITH_CLOUD=True`.

3.  Ensure your `record_request` table has the filename that you want to test with and that the file also exists in the cloud bucket: `n1-testing-files`.

4.  Ensure the `qdrant` cluster is up and running [`qdrant`](https://qdrant.tech/).

5.  Ensure you clean up the `clinical_data` table.\
    The test queries all rows with a specific `userid` and `recordid`, which will result in duplicates if the table is not cleaned up beforehand.

6.  Set up your `.env` file as follows:

    ```env
    OPENAI_API_KEY=<KEY>
    OPENAI_BASE_URL="https://litellm.n1-research.com/v1"
    QDRANT_API_KEY=<API_KEY_QDRANT>
    QDRANT_API_URL=<URL_QDRANT>
    MODEL="claude-3.7-sonnet"
    DATABASE_URL="postgresql+psycopg2://postgres:postgres@localhost:5432/clinical_data"
    TABLE_NAME="clinical_data"
    USER_ID="1309e4db-d803-4f7d-bf5f-224b516b4994"
    BUCKET_NAME="n1-testing-files"
    GCP_SERVICE_JSON=<Path to json>
    RECORDS_TABLE_NAME="record_requests"
    BIOMARKERS_TABLE_NAME="clinical_data"
    PROCEDURES_TABLE_NAME="user_procedures"
    DIAGNOSES_TABLE_NAME="user_diagnoses"
    DB_USERNAME="postgres"
    DB_PASSWORD="postgres"
    DB_HOST="localhost"
    DATABASE_NAME="postgres"
    N1_API_HEADER="N1-Api-Key"
    N1_API_KEY="n1-devkey-7897cdv"
    SYNC_WITH_CLOUD="True"
    N1_API_RECORD_STATUS_URL="http://127.0.0.1:8080/records/status"
    N1_API_SYNC_BIOMARKERS_URL="http://127.0.0.1:8080/records/sync-biomarkers"
    CLOUD_RUN_TASK_INDEX=0
    ENRICHMENT_MCP_SERVER_URL="https://enrichment-server/health-areas"
    ENRICHMENT_VIA_MCP=False
    ENRICHMENT_BATCH_SIZE=10
    ```

7.  Then run the test script:
    ```bash
    uv run scripts/main.py mr-f75b7305-ed3f-4cb9-a227-4b0de8ee0f9c
    ```
