import pytest
import uuid
from fastapi import status
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from models import UserProcedureORM  # Assuming this ORM model exists
from schemas import UserProcedureCreate, UserProcedureBase, PaginatedProcedureResponse


@pytest.mark.integration
class TestProceduresRoutes:

    @pytest.fixture
    def mock_procedure(self, db_session, mock_user):
        """Create a mock procedure in the database."""
        procedure_id = uuid.uuid4()
        # Assuming record_id in UserProcedureORM is also UUID based on UserProcedureBase schema
        # If record_id is expected as string in ORM, adjust accordingly.
        # For now, let's assume it's a string to align with how record_id was handled in diagnoses (e.g. "record123")
        # but the schema UserProcedureBase has record_id: UUID. This might be an inconsistency.
        # Let's use a string for now for the mock ORM, and a UUID for the schema.
        procedure = UserProcedureORM(
            id=procedure_id,
            user_id=mock_user.id,
            record_id=f"mr-{str(uuid.uuid4())}", # Matching UserProcedureBase schema
            name="Test Procedure",
            cpt_code="99213",
            date_performed=datetime.now(timezone.utc),
            outcome="Successful",
            explanation="Test explanation for procedure",
            page_number=1,
        )
        db_session.add(procedure)
        db_session.commit()
        db_session.refresh(procedure)
        return procedure

    @pytest.mark.asyncio
    async def test_create_user_procedure(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating a new procedure."""
        # Prepare test data
        record_id_uuid = str(uuid.uuid4())
        procedure_data = {
            "user_id": str(mock_user.id),
            "record_id": str(record_id_uuid), # Send as string, will be validated as UUID by Pydantic
            "name": "New Procedure",
            "cpt_code": "99214",
            "date_performed": datetime.now(timezone.utc).isoformat(),
            "outcome": "Pending",
            "explanation": "New procedure explanation",
            "page_number": 2,
        }

        # Mock the create_procedure method
        new_procedure_id = uuid.uuid4()
        new_procedure_mock_orm = MagicMock()
        new_procedure_mock_orm.id = new_procedure_id
        new_procedure_mock_orm.user_id = mock_user.id
        new_procedure_mock_orm.record_id = record_id_uuid # Store as UUID
        new_procedure_mock_orm.name = procedure_data["name"]
        new_procedure_mock_orm.cpt_code = procedure_data["cpt_code"]
        new_procedure_mock_orm.date_performed = datetime.fromisoformat(procedure_data["date_performed"])
        new_procedure_mock_orm.outcome = procedure_data["outcome"]
        new_procedure_mock_orm.explanation = procedure_data["explanation"]
        new_procedure_mock_orm.page_number = procedure_data["page_number"]
        new_procedure_mock_orm.additional_data = None # Add this line
        new_procedure_mock_orm.created_at = datetime.now(timezone.utc)
        new_procedure_mock_orm.updated_at = datetime.now(timezone.utc)

        mock_create = AsyncMock(return_value=new_procedure_mock_orm)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_procedure", mock_create)

        # Make the request
        response = auth_client.post("/procedures/", json=procedure_data)

        # Verify the response
        assert response.status_code == status.HTTP_201_CREATED, response.text
        data = response.json()
        assert data["name"] == procedure_data["name"]
        assert data["cpt_code"] == procedure_data["cpt_code"]
        assert data["outcome"] == procedure_data["outcome"]
        assert data["explanation"] == procedure_data["explanation"]
        assert data["page_number"] == procedure_data["page_number"]
        assert data["record_id"] == record_id_uuid
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

        # Verify the service method was called with correct arguments
        mock_create.assert_called_once()
        call_args = mock_create.call_args[0][0] # The UserProcedureCreate object
        assert isinstance(call_args, UserProcedureCreate)
        assert call_args.user_id == mock_user.id # mock_user.id is already UUID
        assert call_args.record_id == record_id_uuid
        assert call_args.name == procedure_data["name"]
        assert call_args.cpt_code == procedure_data["cpt_code"]
        assert call_args.date_performed == datetime.fromisoformat(procedure_data["date_performed"])
        assert call_args.outcome == procedure_data["outcome"]
        assert call_args.explanation == procedure_data["explanation"]
        assert call_args.page_number == procedure_data["page_number"]

    @pytest.mark.asyncio
    async def test_get_user_procedures(self, auth_client, mock_user, mock_procedure, mock_n1_process_pipeline, monkeypatch):
        """Test getting all procedures for a user."""
        # Mock the get_user_procedures method
        mock_procedures = [mock_procedure]
        mock_get = AsyncMock(return_value=mock_procedures)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_procedures", mock_get)

        # Make the request
        response = auth_client.get(f"/procedures/?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        procedure_resp = data[0]
        assert procedure_resp["name"] == mock_procedure.name
        assert procedure_resp["cpt_code"] == mock_procedure.cpt_code
        assert procedure_resp["outcome"] == mock_procedure.outcome
        assert procedure_resp["explanation"] == mock_procedure.explanation
        assert procedure_resp["page_number"] == mock_procedure.page_number
        assert procedure_resp["record_id"] == mock_procedure.record_id
        assert str(mock_procedure.id) == procedure_resp["id"] # id is already string in response

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id)

    @pytest.mark.asyncio
    async def test_get_user_procedures_paginated(self, auth_client, mock_user, mock_procedure, mock_n1_process_pipeline, monkeypatch):
        """Test getting all procedures for a user with pagination."""
        # Mock paginated response
        mock_paginated_response = PaginatedProcedureResponse(
            status="success",
            user_id=mock_user.id,
            total_count=1,
            page=1,
            page_size=20,
            total_pages=1,
            data=[mock_procedure]
        )
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_procedures_paginated", mock_get)

        # Make the request
        response = auth_client.get(f"/procedures/paginated?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 1
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert data["total_pages"] == 1
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params_obj = call_args[0][0]  # The first argument is the query params object
        assert str(query_params_obj.user_id) == str(mock_user.id)  # user_id

        # Verify the query_params object (ProcedureQueryParams)
        assert query_params_obj.page == 1
        assert query_params_obj.page_size == 20
        assert query_params_obj.sort_by == "created_at"
        assert query_params_obj.is_descending == False
        assert query_params_obj.record_id is None
        assert query_params_obj.name is None
        assert query_params_obj.cpt_code is None
        assert query_params_obj.outcome is None
        assert query_params_obj.date_performed_from is None
        assert query_params_obj.date_performed_to is None

    @pytest.mark.asyncio
    async def test_get_user_procedure(self, auth_client, mock_user, mock_procedure, mock_n1_process_pipeline, monkeypatch):
        """Test getting a specific procedure."""
        # Mock the get_procedure method
        mock_get = AsyncMock(return_value=mock_procedure)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_procedure", mock_get)

        # Make the request
        response = auth_client.get(f"/procedures/{mock_procedure.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["name"] == mock_procedure.name
        assert data["cpt_code"] == mock_procedure.cpt_code
        assert data["outcome"] == mock_procedure.outcome
        assert data["explanation"] == mock_procedure.explanation
        assert data["page_number"] == mock_procedure.page_number
        assert data["record_id"] == mock_procedure.record_id
        assert str(mock_procedure.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, mock_procedure.id)

    @pytest.mark.asyncio
    async def test_update_user_procedure(self, auth_client, mock_user, mock_procedure, mock_n1_process_pipeline, monkeypatch):
        """Test updating a procedure."""
        # Prepare update data
        # record_id is not part of UserProcedureBase, so it's not in the update payload
        update_data = {
            "name": "Updated Procedure",
            "cpt_code": mock_procedure.cpt_code,
            "date_performed": mock_procedure.date_performed.isoformat(),
            "outcome": "Completed with complications",
            "explanation": "Updated explanation for procedure",
            "page_number": 3
        }

        # Mock the update_procedure method
        updated_procedure_mock_orm = MagicMock()
        updated_procedure_mock_orm.id = mock_procedure.id
        updated_procedure_mock_orm.user_id = mock_user.id
        updated_procedure_mock_orm.record_id = mock_procedure.record_id
        updated_procedure_mock_orm.name = update_data["name"]
        updated_procedure_mock_orm.cpt_code = mock_procedure.cpt_code # Unchanged
        updated_procedure_mock_orm.date_performed = mock_procedure.date_performed # Unchanged
        updated_procedure_mock_orm.outcome = update_data["outcome"]
        updated_procedure_mock_orm.explanation = update_data["explanation"]
        updated_procedure_mock_orm.page_number = update_data["page_number"]
        updated_procedure_mock_orm.additional_data = None
        updated_procedure_mock_orm.created_at = mock_procedure.created_at
        updated_procedure_mock_orm.updated_at = datetime.now(timezone.utc)

        mock_update = AsyncMock(return_value=updated_procedure_mock_orm)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_procedure", mock_update)

        # Make the request
        response = auth_client.patch(f"/procedures/{mock_procedure.id}?user_id={mock_user.id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["outcome"] == update_data["outcome"]
        assert data["explanation"] == update_data["explanation"]
        assert data["page_number"] == update_data["page_number"]
        assert data["cpt_code"] == mock_procedure.cpt_code  # Unchanged field
        assert str(mock_procedure.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_update.assert_called_once()
        call_args = mock_update.call_args
        assert call_args[0][0] == mock_user.id
        assert call_args[0][1] == mock_procedure.id
        # The third argument is the Pydantic model for update
        update_payload_model = call_args[0][2]
        assert isinstance(update_payload_model, UserProcedureBase) # Or a specific update schema if one exists
        assert update_payload_model.name == update_data["name"]
        assert update_payload_model.outcome == update_data["outcome"]
        assert update_payload_model.explanation == update_data["explanation"]
        assert update_payload_model.page_number == update_data["page_number"]
        # Ensure other fields are passed correctly
        assert update_payload_model.cpt_code == update_data["cpt_code"]
        assert update_payload_model.date_performed == datetime.fromisoformat(update_data["date_performed"])


    @pytest.mark.asyncio
    async def test_delete_user_procedure(self, auth_client, mock_user, mock_procedure, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a procedure."""
        # Mock the delete_procedure method
        mock_delete_response = {
            "status": "success",
            "message": f"Procedure {mock_procedure.id} deleted successfully"
        }
        mock_delete = AsyncMock(return_value=mock_delete_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_procedure", mock_delete)

        # Make the request
        response = auth_client.delete(f"/procedures/{mock_procedure.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["status"] == "success"
        assert "deleted successfully" in data["message"]

        # Verify the service method was called with correct arguments
        mock_delete.assert_called_once_with(mock_user.id, mock_procedure.id)

    @pytest.mark.asyncio
    async def test_create_user_procedure_validation_error(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating a procedure with invalid data."""
        # Mock the create_procedure method to raise HTTPException for user not found
        from fastapi import HTTPException
        mock_create = AsyncMock(side_effect=HTTPException(status_code=404, detail="User not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "create_procedure", mock_create)

        # Prepare invalid data (missing optional fields like name, date_performed, record_id)
        invalid_data = {
            "user_id": str(mock_user.id),
            "cpt_code": "12345"
            # Missing: record_id, name, date_performed (all optional in schema)
        }

        # Make the request
        response = auth_client.post("/procedures/", json=invalid_data)

        # Since record_id is optional in the schema, the request will reach the service
        # and fail with user not found error (500 -> 404 in this case)
        assert response.status_code == status.HTTP_404_NOT_FOUND, response.text
        data = response.json()
        assert "detail" in data
        assert "User not found" in data["detail"]


    @pytest.mark.asyncio
    async def test_get_user_procedure_not_found(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting a non-existent procedure."""
        # Generate a random UUID for a non-existent procedure
        non_existent_id = uuid.uuid4()

        # Mock the get_procedure method to raise HTTPException
        from fastapi import HTTPException
        mock_get = AsyncMock(side_effect=HTTPException(status_code=404, detail="Procedure not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_procedure", mock_get)

        # Make the request
        response = auth_client.get(f"/procedures/{non_existent_id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_404_NOT_FOUND, response.text
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, non_existent_id)

    @pytest.mark.asyncio
    async def test_create_user_procedures_bulk(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating multiple procedures in bulk."""
        record_id = f"mr-{uuid.uuid4()}"
        bulk_data = {
            "user_id": str(mock_user.id),
            "record_id": record_id,
            "procedures": [
                {
                    "name": "Bulk Procedure 1",
                    "cpt_code": "99215",
                    "date_performed": datetime.now(timezone.utc).isoformat(),
                    "outcome": "Stable",
                    "explanation": "First bulk procedure",
                    "page_number": 1,
                },
                {
                    "name": "Bulk Procedure 2",
                    "cpt_code": "99216",
                    "date_performed": datetime.now(timezone.utc).isoformat(),
                    "outcome": "Improving",
                    "explanation": "Second bulk procedure",
                    "page_number": 2,
                },
            ],
        }

        mock_created_procedures = [
            UserProcedureORM(
                id=uuid.uuid4(),
                user_id=mock_user.id,
                record_id=record_id,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                **proc,
            )
            for proc in bulk_data["procedures"]
        ]

        mock_bulk_create = AsyncMock(return_value=mock_created_procedures)
        monkeypatch.setattr(
            mock_n1_process_pipeline, "create_procedures_bulk", mock_bulk_create
        )

        response = auth_client.post("/procedures/bulk", json=bulk_data)

        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 2
        assert data[0]["name"] == "Bulk Procedure 1"
        assert data[0]["record_id"] == record_id
        assert data[1]["name"] == "Bulk Procedure 2"
        assert data[1]["record_id"] == record_id

        mock_bulk_create.assert_called_once()
        call_args = mock_bulk_create.call_args[0][0]
        assert str(call_args.user_id) == str(mock_user.id)
        assert call_args.record_id == record_id
        assert len(call_args.procedures) == 2
