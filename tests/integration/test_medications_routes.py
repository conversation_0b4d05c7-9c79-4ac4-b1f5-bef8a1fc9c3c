import pytest
import uuid
from fastapi import status
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from models import UserMedicationORM
from schemas import UserMedicationCreate, MedicationUpdate, PaginatedMedicationResponse


@pytest.mark.integration
class TestMedicationsRoutes:

    @pytest.fixture
    def mock_medication(self, db_session, mock_user):
        """Create a mock medication in the database."""
        medication_id = uuid.uuid4()
        medication = UserMedicationORM(
            id=medication_id,
            user_id=mock_user.id,
            name="Test Medication",
            brand_name="Test Brand",
            url="https://example.com/medication",
            dosage=10.0,
            unit="mg",
            type="pill",
            started_from=datetime.now(timezone.utc),
            frequency="daily",
            reason="Test reason"
        )
        db_session.add(medication)
        db_session.commit()
        db_session.refresh(medication)
        return medication

    @pytest.mark.asyncio
    async def test_create_medication(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating a new medication."""
        # Prepare test data
        medication_data = {
            "user_id": str(mock_user.id),
            "name": "New Medication",
            "brand_name": "New Brand",
            "url": "https://example.com/new-medication",
            "dosage": 20.0,
            "unit": "mg",
            "type": "liquid",
            "started_from": datetime.now(timezone.utc).isoformat(),
            "frequency": "twice daily",
            "reason": "New reason"
        }

        # Mock the create_medication method
        new_medication = MagicMock()
        new_medication.id = uuid.uuid4()
        new_medication.user_id = mock_user.id
        new_medication.name = medication_data["name"]
        new_medication.brand_name = medication_data["brand_name"]
        new_medication.url = medication_data["url"]
        new_medication.dosage = medication_data["dosage"]
        new_medication.unit = medication_data["unit"]
        new_medication.type = medication_data["type"]
        new_medication.started_from = datetime.fromisoformat(medication_data["started_from"])
        new_medication.frequency = medication_data["frequency"]
        new_medication.reason = medication_data["reason"]
        new_medication.additional_data = None
        new_medication.created_at = datetime.now(timezone.utc)
        new_medication.updated_at = datetime.now(timezone.utc)

        mock_create = AsyncMock(return_value=new_medication)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_medication", mock_create)

        # Make the request
        response = auth_client.post("/medications", json=medication_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == medication_data["name"]
        assert data["brand_name"] == medication_data["brand_name"]
        assert data["dosage"] == medication_data["dosage"]
        assert data["unit"] == medication_data["unit"]
        assert data["type"] == medication_data["type"]
        assert data["frequency"] == medication_data["frequency"]
        assert data["reason"] == medication_data["reason"]
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

        # Verify the service method was called with correct arguments
        mock_create.assert_called_once()
        call_args = mock_create.call_args[0][0]
        assert call_args.user_id == mock_user.id
        assert call_args.name == medication_data["name"]
        assert call_args.brand_name == medication_data["brand_name"]
        assert call_args.dosage == medication_data["dosage"]
        assert call_args.unit == medication_data["unit"]
        assert call_args.type == medication_data["type"]
        assert call_args.frequency == medication_data["frequency"]
        assert call_args.reason == medication_data["reason"]

    @pytest.mark.asyncio
    async def test_get_user_medications(self, auth_client, mock_user, mock_medication, mock_n1_process_pipeline, monkeypatch):
        """Test getting all medications for a user."""
        # Mock the get_user_medications method
        mock_medications = [mock_medication]
        mock_get = AsyncMock(return_value=mock_medications)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_medications", mock_get)

        # Make the request
        response = auth_client.get(f"/medications?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        assert data[0]["name"] == mock_medication.name
        assert data[0]["brand_name"] == mock_medication.brand_name
        assert data[0]["dosage"] == mock_medication.dosage
        assert data[0]["unit"] == mock_medication.unit
        assert data[0]["type"] == mock_medication.type
        assert data[0]["frequency"] == mock_medication.frequency
        assert data[0]["reason"] == mock_medication.reason
        assert str(mock_medication.id) in data[0]["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(str(mock_user.id))

    @pytest.mark.asyncio
    async def test_get_user_medications_paginated(self, auth_client, mock_user, mock_medication, mock_n1_process_pipeline, monkeypatch):
        """Test getting all medications for a user with pagination."""
        # Mock paginated response
        mock_paginated_response = PaginatedMedicationResponse(
            status="success",
            user_id=str(mock_user.id),
            total_count=1,
            page=1,
            page_size=20,
            total_pages=1,
            data=[mock_medication]
        )
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_medications_paginated", mock_get)

        # Make the request
        response = auth_client.get(f"/medications/paginated?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 1
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert data["total_pages"] == 1
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params_obj = call_args[0][0]  # The first argument is the query params object
        assert str(query_params_obj.user_id) == str(mock_user.id)  # user_id

        # Verify the query_params object (MedicationQueryParams)
        assert query_params_obj.page == 1
        assert query_params_obj.page_size == 20
        assert query_params_obj.sort_by == "created_at"
        assert query_params_obj.is_descending == False
        assert query_params_obj.name is None
        assert query_params_obj.brand_name is None
        assert query_params_obj.type is None
        assert query_params_obj.unit is None
        assert query_params_obj.frequency is None
        assert query_params_obj.reason is None
        assert query_params_obj.started_from_date is None
        assert query_params_obj.started_to_date is None
        assert query_params_obj.stopped_from_date is None
        assert query_params_obj.stopped_to_date is None

    @pytest.mark.asyncio
    async def test_get_medication(self, auth_client, mock_user, mock_medication, mock_n1_process_pipeline, monkeypatch):
        """Test getting a specific medication."""
        # Mock the get_medication method
        mock_get = AsyncMock(return_value=mock_medication)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_medication", mock_get)

        # Make the request
        response = auth_client.get(f"/medications/{mock_medication.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == mock_medication.name
        assert data["brand_name"] == mock_medication.brand_name
        assert data["dosage"] == mock_medication.dosage
        assert data["unit"] == mock_medication.unit
        assert data["type"] == mock_medication.type
        assert data["frequency"] == mock_medication.frequency
        assert data["reason"] == mock_medication.reason
        assert str(mock_medication.id) in data["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, str(mock_medication.id))

    @pytest.mark.asyncio
    async def test_update_medication(self, auth_client, mock_user, mock_medication, mock_n1_process_pipeline, monkeypatch):
        """Test updating a medication."""
        # Prepare update data
        update_data = {
            "name": "Updated Medication",
            "dosage": 15.0,
            "unit": "ml",
            "frequency": "three times daily"
        }

        # Mock the update_medication method
        updated_medication = MagicMock()
        updated_medication.id = mock_medication.id
        updated_medication.user_id = mock_user.id
        updated_medication.name = update_data["name"]
        updated_medication.brand_name = mock_medication.brand_name
        updated_medication.url = mock_medication.url
        updated_medication.dosage = update_data["dosage"]
        updated_medication.unit = update_data["unit"]
        updated_medication.type = mock_medication.type
        updated_medication.started_from = mock_medication.started_from
        updated_medication.stopped_on = mock_medication.stopped_on
        updated_medication.frequency = update_data["frequency"]
        updated_medication.reason = mock_medication.reason
        updated_medication.additional_data = None
        updated_medication.created_at = mock_medication.created_at
        updated_medication.updated_at = datetime.now(timezone.utc)

        mock_update = AsyncMock(return_value=updated_medication)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_medication", mock_update)

        # Make the request
        response = auth_client.patch(f"/medications/{mock_medication.id}?user_id={mock_user.id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["dosage"] == update_data["dosage"]
        assert data["unit"] == update_data["unit"]
        assert data["frequency"] == update_data["frequency"]
        assert data["brand_name"] == mock_medication.brand_name  # Unchanged field
        assert str(mock_medication.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_update.assert_called_once()
        call_args = mock_update.call_args
        assert call_args[0][0] == mock_user.id
        assert call_args[0][1] == mock_medication.id
        assert call_args[0][2].name == update_data["name"]
        assert call_args[0][2].dosage == update_data["dosage"]
        assert call_args[0][2].unit == update_data["unit"]
        assert call_args[0][2].frequency == update_data["frequency"]

    @pytest.mark.asyncio
    async def test_delete_medication(self, auth_client, mock_user, mock_medication, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a medication."""
        # Mock the delete_medication method
        mock_delete_response = {
            "status": "success",
            "message": f"Medication {mock_medication.id} deleted successfully"
        }
        mock_delete = AsyncMock(return_value=mock_delete_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_medication", mock_delete)

        # Make the request
        response = auth_client.delete(f"/medications/{mock_medication.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert "deleted successfully" in data["message"]

        # Verify the service method was called with correct arguments
        mock_delete.assert_called_once_with(mock_user.id, mock_medication.id)

    @pytest.mark.asyncio
    async def test_create_medication_validation_error(self, auth_client, mock_user):
        """Test creating a medication with invalid data."""
        # Prepare invalid data (missing required fields)
        invalid_data = {
            "user_id": str(mock_user.id),
            "name": "Invalid Medication"
            # Missing required fields: dosage, unit, type, started_from, frequency
        }

        # Make the request
        response = auth_client.post("/medications", json=invalid_data)

        # Verify the response indicates validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
        # Check that the error message mentions the missing fields
        error_fields = [error["loc"][-1] for error in data["detail"]]
        assert "dosage" in error_fields
        assert "unit" in error_fields
        assert "type" in error_fields
        assert "started_from" in error_fields
        assert "frequency" in error_fields

    @pytest.mark.asyncio
    async def test_get_medication_not_found(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting a non-existent medication."""
        # Generate a random UUID for a non-existent medication
        non_existent_id = str(uuid.uuid4())

        # Mock the get_medication method to raise HTTPException
        from fastapi import HTTPException
        mock_get = AsyncMock(side_effect=HTTPException(status_code=404, detail="Medication not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_medication", mock_get)

        # Make the request
        response = auth_client.get(f"/medications/{non_existent_id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, non_existent_id)
