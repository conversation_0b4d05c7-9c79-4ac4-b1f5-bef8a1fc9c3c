import pytest
import uuid
from fastapi import status
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from models import CanonicalRecordORM, UserBiomarkerORM
from schemas import PaginatedCanonicalBiomarkerResponse


@pytest.mark.integration
class TestCanonicalBiomarkersRoutes:

    @pytest.fixture
    def mock_canonical_biomarker(self, db_session):
        """Create a mock canonical biomarker in the database."""
        canonical_id = uuid.uuid4()
        canonical_biomarker = CanonicalRecordORM(
            id=canonical_id,
            user_id=uuid.uuid4(),  # Added user_id
            canonical_name="Test Glucose",
            standard_unit="mg/dL",
            reference_range_min=70.0,
            reference_range_max=100.0,
            health_areas=["metabolic", "diabetes"],
            group_name="Blood Chemistry",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        db_session.add(canonical_biomarker)
        db_session.commit()
        db_session.refresh(canonical_biomarker)
        return canonical_biomarker

    @pytest.fixture
    def mock_user_biomarker_with_canonical(self, db_session, mock_user, mock_canonical_biomarker):
        """Create a user biomarker that references a canonical biomarker."""
        user_biomarker = UserBiomarkerORM(
            id=uuid.uuid4(),
            user_id=mock_user.id,
            record_id="record123",
            test_name="Glucose Test",
            result="85",
            reference_range="70-100",
            unit="mg/dL",
            canonical_id=mock_canonical_biomarker.id,
            excluded=False,
            test_date=datetime.now(),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        db_session.add(user_biomarker)
        db_session.commit()
        db_session.refresh(user_biomarker)
        return user_biomarker

    # CREATE Tests
    @pytest.mark.asyncio
    async def test_create_canonical_biomarker(self, auth_client, mock_n1_process_pipeline, monkeypatch):
        """Test creating a new canonical biomarker."""
        # Prepare test data
        canonical_data = {
            "user_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "canonical_name": "New Canonical Biomarker",
            "standard_unit": "mmol/L",
            "reference_range_min": 3.9,
            "reference_range_max": 5.5,
            "health_areas": ["cardiovascular", "metabolic"],
            "group_name": "Lipid Panel"
        }

        # Mock the create_canonical_biomarker method
        new_canonical = MagicMock()
        new_canonical.id = uuid.uuid4()
        new_canonical.user_id = uuid.UUID(canonical_data["user_id"])  # Ensure UUID type
        new_canonical.canonical_name = canonical_data["canonical_name"]
        new_canonical.standard_unit = canonical_data["standard_unit"]
        new_canonical.reference_range_min = canonical_data["reference_range_min"]
        new_canonical.reference_range_max = canonical_data["reference_range_max"]
        new_canonical.health_areas = canonical_data["health_areas"]
        new_canonical.group_name = canonical_data["group_name"]
        new_canonical.created_at = datetime.now(timezone.utc)
        new_canonical.updated_at = datetime.now(timezone.utc)

        mock_create = AsyncMock(return_value=new_canonical)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_canonical_biomarker", mock_create)

        # Make the request
        response = auth_client.post("/canonical_biomarkers/", json=canonical_data)

        # Verify the response
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert uuid.UUID(data["user_id"]) == uuid.UUID(canonical_data["user_id"])  # Fixed UUID comparison
        assert data["canonical_name"] == canonical_data["canonical_name"]
        assert data["standard_unit"] == canonical_data["standard_unit"]
        assert data["reference_range_min"] == canonical_data["reference_range_min"]
        assert data["reference_range_max"] == canonical_data["reference_range_max"]
        assert data["health_areas"] == canonical_data["health_areas"]
        assert data["group_name"] == canonical_data["group_name"]
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

        # Verify the service method was called with correct arguments
        mock_create.assert_called_once()
        call_args = mock_create.call_args[0][0]
        assert call_args.user_id == uuid.UUID(canonical_data["user_id"])  # Fixed UUID comparison
        assert call_args.canonical_name == canonical_data["canonical_name"]
        assert call_args.standard_unit == canonical_data["standard_unit"]
        assert call_args.reference_range_min == canonical_data["reference_range_min"]
        assert call_args.reference_range_max == canonical_data["reference_range_max"]
        assert call_args.health_areas == canonical_data["health_areas"]
        assert call_args.group_name == canonical_data["group_name"]

    @pytest.mark.asyncio
    async def test_create_canonical_biomarker_duplicate_name(self, auth_client, mock_n1_process_pipeline, monkeypatch):
        """Test creating a canonical biomarker with duplicate name."""
        canonical_data = {
            "user_id": str(uuid.uuid4()),
            "canonical_name": "Duplicate Name",
            "standard_unit": "mg/dL"
        }

        # Mock the create_canonical_biomarker method to raise HTTPException for duplicate
        from fastapi import HTTPException
        mock_create = AsyncMock(side_effect=HTTPException(
            status_code=409,
            detail="Canonical biomarker with name 'Duplicate Name' already exists"
        ))
        monkeypatch.setattr(mock_n1_process_pipeline, "create_canonical_biomarker", mock_create)

        # Make the request
        response = auth_client.post("/canonical_biomarkers/", json=canonical_data)

        # Verify the response
        assert response.status_code == status.HTTP_409_CONFLICT
        data = response.json()
        assert "detail" in data
        assert "already exists" in data["detail"]

    @pytest.mark.asyncio
    async def test_get_canonical_biomarkers_paginated(self, auth_client, mock_canonical_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting canonical biomarkers with pagination."""
        # Mock paginated response
        user_id=uuid.uuid4()
        mock_canonical_biomarker.user_id = user_id
        mock_canonical_biomarker.member_count = 3
        mock_paginated_response = PaginatedCanonicalBiomarkerResponse(
            data=[mock_canonical_biomarker],
            status="success",
            user_id=user_id,
            page=1,
            page_size=20,
            total_pages=1,
            total_count=1
        )
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_canonical_biomarkers_paginated", mock_get)

        # Make the request
        response = auth_client.get("/canonical_biomarkers/paginated", params={"user_id": user_id, "page": 1, "page_size": 20})

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["total_count"] == 1
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert data["total_pages"] == 1
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1

        # Verify that member_count is included in the response
        canonical_biomarker = data["data"][0]
        assert "member_count" in canonical_biomarker
        assert isinstance(canonical_biomarker["member_count"], int)
        assert canonical_biomarker["member_count"] == 3

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params_obj = call_args[0][0]  # The first argument is the query params object

        # Verify the query_params object (CanonicalBiomarkerQueryParams)
        assert query_params_obj.page == 1
        assert query_params_obj.page_size == 20
        assert query_params_obj.sort_by == "created_at"
        assert query_params_obj.is_descending == False
        assert query_params_obj.canonical_name is None
        assert query_params_obj.standard_unit is None
        assert query_params_obj.group_name is None
        assert query_params_obj.health_areas is None
        assert query_params_obj.created_from is None
        assert query_params_obj.created_to is None

    @pytest.mark.asyncio
    async def test_get_canonical_biomarkers_paginated_with_filters(self, auth_client, mock_n1_process_pipeline, monkeypatch):
        """Test getting canonical biomarkers with pagination and filters."""
        # Mock paginated response
        user_id=uuid.uuid4()
        mock_paginated_response = PaginatedCanonicalBiomarkerResponse(
            data=[],
            total_count=0,
            status="success",
            user_id=user_id,
            page_size=10,
            page=1,
            total_pages=0
        )
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_canonical_biomarkers_paginated", mock_get)

        # Make the request with filters
        query_params = {
            "page": 1,
            "page_size": 10,
            "sort_by": "canonical_name",
            "is_descending": True,
            "canonical_name": "glucose",
            "standard_unit": "mg/dL",
            "group_name": "metabolic",
            "health_areas": "diabetes,metabolic",
            "user_id": str(user_id)
        }
        response = auth_client.get("/canonical_biomarkers/paginated", params=query_params)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total_count"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 10

        # Verify the service method was called with correct filters
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params_obj = call_args[0][0]
        assert query_params_obj.canonical_name == "glucose"
        assert query_params_obj.standard_unit == "mg/dL"
        assert query_params_obj.group_name == "metabolic"
        assert query_params_obj.health_areas == "diabetes,metabolic"
        assert query_params_obj.sort_by == "canonical_name"
        assert query_params_obj.is_descending == True

    @pytest.mark.asyncio
    async def test_get_canonical_biomarker_by_id(self, auth_client, mock_canonical_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting a specific canonical biomarker by ID."""
        # Mock the get_canonical_biomarker method
        mock_get = AsyncMock(return_value=mock_canonical_biomarker)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_canonical_biomarker", mock_get)

        # Make the request
        response = auth_client.get(f"/canonical_biomarkers/{mock_canonical_biomarker.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["canonical_name"] == mock_canonical_biomarker.canonical_name
        assert data["standard_unit"] == mock_canonical_biomarker.standard_unit
        assert data["reference_range_min"] == mock_canonical_biomarker.reference_range_min
        assert data["reference_range_max"] == mock_canonical_biomarker.reference_range_max
        assert data["health_areas"] == mock_canonical_biomarker.health_areas
        assert data["group_name"] == mock_canonical_biomarker.group_name
        assert str(mock_canonical_biomarker.id) in data["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_canonical_biomarker.id)

    @pytest.mark.asyncio
    async def test_get_canonical_biomarker_not_found(self, auth_client, mock_n1_process_pipeline, monkeypatch):
        """Test getting a non-existent canonical biomarker."""
        # Generate a random UUID for a non-existent canonical biomarker
        non_existent_id = str(uuid.uuid4())

        # Mock the get_canonical_biomarker method to raise HTTPException
        from fastapi import HTTPException
        mock_get = AsyncMock(side_effect=HTTPException(status_code=404, detail="Canonical biomarker not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_canonical_biomarker", mock_get)

        # Make the request
        response = auth_client.get(f"/canonical_biomarkers/{non_existent_id}")

        # Verify the response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(uuid.UUID(non_existent_id))

    # UPDATE Tests
    @pytest.mark.asyncio
    async def test_update_canonical_biomarker(self, auth_client, mock_canonical_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test updating a canonical biomarker."""
        # Prepare update data
        update_data = {
            "canonical_name": "Updated Canonical Name",
            "reference_range_min": 65.0,
            "reference_range_max": 105.0,
            "health_areas": ["updated", "areas"]
        }

        # Mock the update_canonical_biomarker method
        updated_canonical = MagicMock()
        updated_canonical.id = mock_canonical_biomarker.id
        updated_canonical.user_id = mock_canonical_biomarker.user_id
        updated_canonical.canonical_name = update_data["canonical_name"]
        updated_canonical.standard_unit = mock_canonical_biomarker.standard_unit
        updated_canonical.reference_range_min = update_data["reference_range_min"]
        updated_canonical.reference_range_max = update_data["reference_range_max"]
        updated_canonical.health_areas = update_data["health_areas"]
        updated_canonical.group_name = mock_canonical_biomarker.group_name
        updated_canonical.created_at = mock_canonical_biomarker.created_at
        updated_canonical.updated_at = datetime.now(timezone.utc)

        mock_update = AsyncMock(return_value=updated_canonical)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_canonical_biomarker", mock_update)

        # Make the request
        response = auth_client.patch(f"/canonical_biomarkers/{mock_canonical_biomarker.id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["canonical_name"] == update_data["canonical_name"]
        assert data["reference_range_min"] == update_data["reference_range_min"]
        assert data["reference_range_max"] == update_data["reference_range_max"]
        assert data["health_areas"] == update_data["health_areas"]
        assert data["standard_unit"] == mock_canonical_biomarker.standard_unit  # Unchanged field
        assert str(mock_canonical_biomarker.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_update.assert_called_once()
        call_args = mock_update.call_args
        assert call_args[0][0] == mock_canonical_biomarker.id
        assert call_args[0][1].canonical_name == update_data["canonical_name"]
        assert call_args[0][1].reference_range_min == update_data["reference_range_min"]
        assert call_args[0][1].reference_range_max == update_data["reference_range_max"]
        assert call_args[0][1].health_areas == update_data["health_areas"]

    @pytest.mark.asyncio
    async def test_update_canonical_biomarker_duplicate_name(self, auth_client, mock_canonical_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test updating a canonical biomarker with duplicate name."""
        update_data = {
            "canonical_name": "Existing Name"
        }

        # Mock the update_canonical_biomarker method to raise HTTPException for duplicate
        from fastapi import HTTPException
        mock_update = AsyncMock(side_effect=HTTPException(
            status_code=409,
            detail="Canonical biomarker with name 'Existing Name' already exists"
        ))
        monkeypatch.setattr(mock_n1_process_pipeline, "update_canonical_biomarker", mock_update)

        # Make the request
        response = auth_client.patch(f"/canonical_biomarkers/{mock_canonical_biomarker.id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_409_CONFLICT
        data = response.json()
        assert "detail" in data
        assert "already exists" in data["detail"]

    @pytest.mark.asyncio
    async def test_update_canonical_biomarker_not_found(self, auth_client, mock_n1_process_pipeline, monkeypatch):
        """Test updating a non-existent canonical biomarker."""
        non_existent_id = str(uuid.uuid4())
        update_data = {"canonical_name": "Updated Name"}

        # Mock the update_canonical_biomarker method to raise HTTPException
        from fastapi import HTTPException
        mock_update = AsyncMock(side_effect=HTTPException(status_code=404, detail="Canonical biomarker not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "update_canonical_biomarker", mock_update)

        # Make the request
        response = auth_client.patch(f"/canonical_biomarkers/{non_existent_id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

    # DELETE Tests
    @pytest.mark.asyncio
    async def test_delete_canonical_biomarker(self, auth_client, mock_canonical_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a canonical biomarker."""
        # Mock the delete_canonical_biomarker method
        mock_delete_response = {
            "status": "success",
            "message": f"Canonical biomarker {mock_canonical_biomarker.id} deleted successfully"
        }
        mock_delete = AsyncMock(return_value=mock_delete_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_canonical_biomarker", mock_delete)

        # Make the request
        response = auth_client.delete(f"/canonical_biomarkers/{mock_canonical_biomarker.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert "deleted successfully" in data["message"]

        # Verify the service method was called with correct arguments
        mock_delete.assert_called_once_with(mock_canonical_biomarker.id)

    @pytest.mark.asyncio
    async def test_delete_canonical_biomarker_with_references(self, auth_client, mock_canonical_biomarker, mock_user_biomarker_with_canonical, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a canonical biomarker that is referenced by user biomarkers."""
        # Note: mock_user_biomarker_with_canonical is needed to set up the reference relationship
        # Mock the delete_canonical_biomarker method to raise HTTPException for references
        from fastapi import HTTPException
        mock_delete = AsyncMock(side_effect=HTTPException(
            status_code=409,
            detail="Cannot delete canonical biomarker: it is referenced by 1 user biomarker(s)"
        ))
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_canonical_biomarker", mock_delete)

        # Make the request
        response = auth_client.delete(f"/canonical_biomarkers/{mock_canonical_biomarker.id}")

        # Verify the response
        assert response.status_code == status.HTTP_409_CONFLICT
        data = response.json()
        assert "detail" in data
        assert "referenced by" in data["detail"]
        assert "user biomarker" in data["detail"]

        # Verify the service method was called with correct arguments
        mock_delete.assert_called_once_with(mock_canonical_biomarker.id)

    @pytest.mark.asyncio
    async def test_delete_canonical_biomarker_not_found(self, auth_client, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a non-existent canonical biomarker."""
        non_existent_id = str(uuid.uuid4())

        # Mock the delete_canonical_biomarker method to raise HTTPException
        from fastapi import HTTPException
        mock_delete = AsyncMock(side_effect=HTTPException(status_code=404, detail="Canonical biomarker not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_canonical_biomarker", mock_delete)

        # Make the request
        response = auth_client.delete(f"/canonical_biomarkers/{non_existent_id}")

        # Verify the response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

        # Verify the service method was called with correct arguments
        mock_delete.assert_called_once_with(uuid.UUID(non_existent_id))

    # VALIDATION Tests
    @pytest.mark.asyncio
    async def test_create_canonical_biomarker_validation_error(self, auth_client):
        """Test creating a canonical biomarker with invalid data."""
        # Prepare invalid data (missing required canonical_name field)
        invalid_data = {
            "standard_unit": "mg/dL",
            "reference_range_min": 70.0
            # Missing required field: canonical_name
        }

        # Make the request
        response = auth_client.post("/canonical_biomarkers/", json=invalid_data)

        # Verify the response indicates validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
        # Check that the error message mentions the missing field
        error_fields = [error["loc"][-1] for error in data["detail"]]
        assert "canonical_name" in error_fields

    @pytest.mark.asyncio
    async def test_create_canonical_biomarker_invalid_data_types(self, auth_client):
        """Test creating a canonical biomarker with invalid data types."""
        invalid_data = {
            "canonical_name": "Test Biomarker",
            "reference_range_min": "not-a-number",  # Should be float
            "reference_range_max": "also-not-a-number"  # Should be float
        }

        # Make the request
        response = auth_client.post("/canonical_biomarkers/", json=invalid_data)

        # Verify the response indicates validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_get_canonical_biomarker_invalid_uuid(self, auth_client):
        """Test getting a canonical biomarker with invalid UUID format."""
        invalid_id = "invalid-uuid-format"

        # Make the request
        response = auth_client.get(f"/canonical_biomarkers/{invalid_id}")

        # Verify the response indicates validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_update_canonical_biomarker_invalid_uuid(self, auth_client):
        """Test updating a canonical biomarker with invalid UUID format."""
        invalid_id = "invalid-uuid-format"
        update_data = {"canonical_name": "Updated Name"}

        # Make the request
        response = auth_client.patch(f"/canonical_biomarkers/{invalid_id}", json=update_data)

        # Verify the response indicates validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_delete_canonical_biomarker_invalid_uuid(self, auth_client):
        """Test deleting a canonical biomarker with invalid UUID format."""
        invalid_id = "invalid-uuid-format"

        # Make the request
        response = auth_client.delete(f"/canonical_biomarkers/{invalid_id}")

        # Verify the response indicates validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data

    # PAGINATION Edge Cases
    @pytest.mark.asyncio
    async def test_get_canonical_biomarkers_paginated_invalid_page(self, auth_client):
        """Test pagination with invalid page parameters."""
        # Test with page = 0 (should be >= 1)
        response = auth_client.get("/canonical_biomarkers/paginated?page=0")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test with negative page
        response = auth_client.get("/canonical_biomarkers/paginated?page=-1")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_get_canonical_biomarkers_paginated_invalid_page_size(self, auth_client):
        """Test pagination with invalid page_size parameters."""
        # Test with page_size = 0 (should be >= 1)
        response = auth_client.get("/canonical_biomarkers/paginated?page_size=0")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test with page_size > 1000 (should be <= 1000)
        response = auth_client.get("/canonical_biomarkers/paginated?page_size=1001")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    # EDGE Cases
    @pytest.mark.asyncio
    async def test_create_canonical_biomarker_minimal_data(self, auth_client, mock_n1_process_pipeline, monkeypatch):
        """Test creating a canonical biomarker with minimal required data."""
        # Only canonical_name is required
        minimal_data = {
            "user_id": str(uuid.uuid4()),
            "canonical_name": "Minimal Biomarker"
        }

        # Mock the create_canonical_biomarker method
        new_canonical = MagicMock()
        new_canonical.id = uuid.uuid4()
        new_canonical.user_id = str(uuid.uuid4())
        new_canonical.canonical_name = minimal_data["canonical_name"]
        new_canonical.standard_unit = None
        new_canonical.reference_range_min = None
        new_canonical.reference_range_max = None
        new_canonical.health_areas = None
        new_canonical.group_name = None
        new_canonical.created_at = datetime.now(timezone.utc)
        new_canonical.updated_at = datetime.now(timezone.utc)

        mock_create = AsyncMock(return_value=new_canonical)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_canonical_biomarker", mock_create)

        # Make the request
        response = auth_client.post("/canonical_biomarkers/", json=minimal_data)

        # Verify the response
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["canonical_name"] == minimal_data["canonical_name"]
        assert data["standard_unit"] is None
        assert data["reference_range_min"] is None
        assert data["reference_range_max"] is None
        assert data["health_areas"] is None
        assert data["group_name"] is None

    @pytest.mark.asyncio
    async def test_update_canonical_biomarker_empty_update(self, auth_client, mock_canonical_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test updating a canonical biomarker with empty update data."""
        # Empty update data
        update_data = {}

        # Mock the update_canonical_biomarker method
        mock_update = AsyncMock(return_value=mock_canonical_biomarker)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_canonical_biomarker", mock_update)

        # Make the request
        response = auth_client.patch(f"/canonical_biomarkers/{mock_canonical_biomarker.id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        # Should return the original data unchanged
        assert data["canonical_name"] == mock_canonical_biomarker.canonical_name
