import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import MagicMock, AsyncMock, patch
from fastapi import status

@pytest.mark.integration
class TestCancelBatchEndpoint:

    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        from models import UserORM
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @pytest.fixture
    def mock_batch_id(self):
        """Create a mock batch ID."""
        return f"batch-{str(uuid.uuid4())[:8]}"

    @pytest.mark.asyncio
    async def test_cancel_batch(self, auth_client, mock_user, mock_batch_id, mock_n1_process_pipeline, monkeypatch):
        """Test cancelling a batch of records."""
        # Mock the cancel_batch method
        now = datetime.now(timezone.utc)
        mock_response = {
            "status": "success",
            "message": f"Batch {mock_batch_id} cancellation has been queued",
            "batch_id": mock_batch_id,
            "updated_at": now
        }
        mock_cancel_batch = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "cancel_batch", mock_cancel_batch)

        # Make the request
        response = auth_client.post(
            f"/records/cancel-batch?user_id={mock_user.id}&batch_id={mock_batch_id}"
        )

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert data["batch_id"] == mock_batch_id
        assert "updated_at" in data
        assert f"Batch {mock_batch_id} cancellation has been queued" in data["message"]

        # Verify the mocked method was called with correct arguments
        mock_cancel_batch.assert_called_once()
        args, _ = mock_cancel_batch.call_args
        assert args[0] == mock_user.id  # First arg should be user_id
        assert args[1] == mock_batch_id  # Second arg should be batch_id

    @pytest.mark.asyncio
    async def test_cancel_batch_error(self, auth_client, mock_user, mock_batch_id, mock_n1_process_pipeline, monkeypatch):
        """Test cancelling a batch of records with an error."""
        # Instead of mocking the cancel_batch method to raise an exception directly,
        # we'll patch the add_task method of BackgroundTasks to raise an exception
        from fastapi import BackgroundTasks
        original_add_task = BackgroundTasks.add_task

        def mock_add_task_error(self, func, *args, **kwargs):
            if func == mock_n1_process_pipeline.cancel_batch:
                raise Exception(f"Error cancelling batch {mock_batch_id} : Test error")
            return original_add_task(self, func, *args, **kwargs)

        # Apply the patch
        with patch.object(BackgroundTasks, 'add_task', mock_add_task_error):
            # Make the request
            response = auth_client.post(
                f"/records/cancel-batch?user_id={mock_user.id}&batch_id={mock_batch_id}"
            )

            # Verify the response
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            data = response.json()
            assert "detail" in data
            assert f"Error cancelling batch {mock_batch_id}" in data["detail"]
