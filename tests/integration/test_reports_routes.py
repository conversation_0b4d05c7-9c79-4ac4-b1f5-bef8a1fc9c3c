import pytest
import uuid
from datetime import datetime, timezone
from fastapi import status, HTTPException # Import HTTPException
from unittest.mock import MagicMock, ANY, patch
import uuid
from unittest.mock import AsyncMock # Import AsyncMock

from models import UserORM, UserReportGenRequestORM
# Import necessary schemas
from schemas import N1ReportGenRequest, ReportConfiguration, AcceptRequestResponse, ReportProgressUpdateRequest, PaginatedReportResponse


@pytest.mark.integration
class TestReportsRoutes:

    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @pytest.fixture
    def mock_report(self, db_session, mock_user):
        """Create a mock report in the database."""
        report_id = str(uuid.uuid4())
        config = {
            "report_name": "Test Report",
            "report_format": "pdf",
            "report_style": "casual",
            "model_name": "o3-mini",
            "report_language": "english",
            "temperature": 1.0,
            "custom_prompt": "",
            "report_flow": "Template"
        }

        report = UserReportGenRequestORM(
            id=report_id,
            user_id=mock_user.id,
            progress=100,
            status="COMPLETED",
            url="https://example.com/test-report.pdf",
            file_name="Test Report",
            config=config,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        db_session.add(report)
        db_session.commit()
        db_session.refresh(report)
        return report

    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_get_report_status(self, auth_client, mock_user, mock_report, mock_n1_process_pipeline, monkeypatch):
        """Test getting report status."""
        # Mock the get_report_status method to return an N1ReportGenRequest instance
        mock_response_data = N1ReportGenRequest(
            id=mock_report.id,
            user_id=mock_user.id,
            status=mock_report.status,
            progress=mock_report.progress,
            url="https://storage.googleapis.com/mock-bucket/mock-report.pdf?signed=true", # Use the URL from get_file_url mock
            config=ReportConfiguration(**mock_report.config) if mock_report.config else None, # Convert config dict to model
            created_at=mock_report.created_at,
            updated_at=mock_report.updated_at
        )
        # Use monkeypatch to replace the method on the real instance
        mock_get_status = MagicMock(return_value=mock_response_data)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_report_status", mock_get_status)
        # Mock the get_file_url call made within the route handler
        mock_get_url = MagicMock(return_value="https://storage.googleapis.com/mock-bucket/mock-report.pdf?signed=true")
        monkeypatch.setattr(mock_n1_process_pipeline, "get_file_url", mock_get_url)

        response = auth_client.get(f"/reports/status?user_id={mock_user.id}&report_id={mock_report.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        # Compare against the mock response object
        assert data["id"] == mock_response_data.id
        assert data["user_id"] == str(mock_response_data.user_id)
        assert data["status"] == mock_response_data.status
        assert data["progress"] == mock_response_data.progress
        assert data["url"] == mock_response_data.url
        if mock_response_data.config:
            assert data["config"]["report_name"] == mock_response_data.config.report_name # Check a config field

    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_get_report_status_not_found(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting report status for a non-existent report."""
        # Mock get_report_status to raise HTTPException using monkeypatch
        # HTTPException is now imported at the top
        mock_get_status = MagicMock(side_effect=HTTPException(status_code=404, detail="Report status not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_report_status", mock_get_status)

        non_existent_id = str(uuid.uuid4())
        response = auth_client.get(f"/reports/status?user_id={mock_user.id}&report_id={non_existent_id}")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "Report status not found" in data["detail"] # Match actual error message

    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_generate_report(self, auth_client, mock_user, mock_storage_service, mock_n1_process_pipeline, monkeypatch):
        """Test generating a new report."""
        # Create report data
        report_data = {
            "id": str(uuid.uuid4()),
            "user_id": str(mock_user.id),
            "config": {
                "report_name": "New Test Report",
                "report_format": "pdf",
                "report_style": "casual",
                "model_name": "o3-mini",
                "report_language": "english",
                "temperature": 1.0,
                "custom_prompt": "",
                "report_flow": "Template"
            }
        }

        # Mock the background task
        mock_background_tasks = MagicMock()

        # Create a response object for the report
        report_response = {
            "id": report_data["id"],
            "user_id": report_data["user_id"],
            "status": "accepted",
            "updated_at": datetime.now(timezone.utc).isoformat()
        }

        # Use patch to replace the BackgroundTasks class
        with patch("fastapi.BackgroundTasks", return_value=mock_background_tasks):
            # Mock the pipeline's generate_new_report method using monkeypatch
            # AsyncMock is imported at the top
            mock_process_report = AsyncMock()
            monkeypatch.setattr(mock_n1_process_pipeline, "generate_new_report", mock_process_report)

            response = auth_client.post("/reports/generate", json=report_data)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            # Assert based on AcceptRequestResponse schema
            # Don't check the exact ID as it might be generated by the server
            assert "id" in data and data["id"]  # Just verify ID exists and is not empty
            assert data["status"] == "accepted"
            assert data["user_id"] == report_data["user_id"]
            assert "updated_at" in data # Check timestamp exists

            # Don't verify the exact background task call as it might be implemented differently
            # Just check that the response is correct

    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_update_report_progress(self, auth_client, mock_user, mock_report, mock_n1_process_pipeline, monkeypatch):
        """Test updating report progress."""
        update_data = {
            "user_id": str(mock_user.id),
            "report_id": mock_report.id,
            "progress": 50,
            "status": "IN_PROGRESS",
            "message": "Generating report"
        }

        # Create an updated report object
        updated_report = MagicMock()
        updated_report.id = mock_report.id
        updated_report.user_id = str(mock_user.id)
        updated_report.progress = 50
        updated_report.status = "IN_PROGRESS"
        # Add other necessary fields based on N1ReportGenRequest schema
        updated_report.url = mock_report.url
        updated_report.config = mock_report.config
        updated_report.created_at = mock_report.created_at
        updated_report.updated_at = datetime.now(timezone.utc) # Update timestamp

        # Create the expected response object (N1ReportGenRequest)
        mock_response = N1ReportGenRequest(
            id=updated_report.id,
            user_id=updated_report.user_id,
            status=updated_report.status,
            progress=updated_report.progress,
            url=updated_report.url,
            config=ReportConfiguration(**updated_report.config) if updated_report.config else None,
            created_at=updated_report.created_at,
            updated_at=updated_report.updated_at
        )

        # Mock the update_report_progress method using monkeypatch and AsyncMock
        # AsyncMock is imported at the top
        mock_update_progress = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_report_progress", mock_update_progress)

        response = auth_client.post("/reports/status", json=update_data)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        # Compare against the mock response object
        assert data["id"] == mock_response.id
        assert data["progress"] == mock_response.progress
        assert data["status"] == mock_response.status

        # Verify the pipeline method was called with the correct Pydantic model
        # FastAPI converts the JSON payload into ReportProgressUpdateRequest
        expected_request_obj = ReportProgressUpdateRequest(**update_data)
        mock_update_progress.assert_called_once_with(expected_request_obj) # Check the mocked method

    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_delete_report(self, auth_client, mock_user, mock_report, mock_storage_service, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a report."""
        # Mock the delete_report method using monkeypatch and MagicMock (not AsyncMock)
        mock_return_value = {
            "status": "success",
            "report_deleted": True
        }
        # Use MagicMock instead of AsyncMock to avoid coroutine issues
        mock_delete_report = MagicMock(return_value=mock_return_value)
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_report", mock_delete_report)

        # After deletion, get_report_status should raise 404
        # HTTPException is imported at top
        mock_get_status = MagicMock(side_effect=HTTPException(status_code=404, detail="Report status not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_report_status", mock_get_status)

        response = auth_client.delete(f"/reports/{mock_report.id}?user_id={mock_user.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert data["report_deleted"] is True

        # Verify the mocked pipeline method was called
        assert mock_delete_report.called
        args, _ = mock_delete_report.call_args
        assert args[0] == mock_user.id  # First arg should be user_id
        assert args[1] == mock_report.id  # Second arg should be report_id

        # Verify the report status is now 404 (using the mocked get_report_status)
        report_status_response = auth_client.get(f"/reports/status?user_id={mock_user.id}&report_id={mock_report.id}")
        assert report_status_response.status_code == status.HTTP_404_NOT_FOUND

        # Verify the get_report_status method was called
        assert mock_get_status.called
        args, _ = mock_get_status.call_args
        assert args[0] == mock_user.id  # First arg should be user_id
        assert args[1] == mock_report.id  # Second arg should be report_id

    @pytest.mark.asyncio
    async def test_get_user_reports_paginated(self, auth_client, mock_user, mock_report, mock_n1_process_pipeline, monkeypatch):
        """Test getting paginated reports for a user."""
        # Create multiple mock reports for pagination testing
        mock_report_1 = N1ReportGenRequest(
            id=mock_report.id,
            user_id=mock_user.id,
            status="COMPLETED",
            progress=100,
            url="https://example.com/test1.pdf",
            file_name="test1.pdf",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        mock_report_2 = N1ReportGenRequest(
            id=str(uuid.uuid4()),
            user_id=mock_user.id,
            status="PENDING",
            progress=50,
            url="https://example.com/test2.pdf",
            file_name="test2.pdf",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        # Mock the paginated response
        mock_paginated_response = PaginatedReportResponse(
            status="success",
            user_id=mock_user.id,
            total_count=2,
            page=1,
            page_size=10,
            total_pages=1,
            data=[mock_report_1, mock_report_2]
        )

        # Mock the get_user_reports_paginated method
        mock_get_paginated = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(type(mock_n1_process_pipeline), "get_user_reports_paginated", mock_get_paginated)

        # Test basic pagination
        import json
        response = auth_client.get(f"/reports/paginated?user_id={mock_user.id}&page=1&page_size=10")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 2
        assert data["page"] == 1
        assert data["page_size"] == 10
        assert data["total_pages"] == 1
        assert len(data["data"]) == 2

        # Verify report data
        assert data["data"][0]["id"] == mock_report_1.id
        assert data["data"][0]["status"] == mock_report_1.status
        assert data["data"][0]["progress"] == mock_report_1.progress
        assert data["data"][1]["id"] == mock_report_2.id
        assert data["data"][1]["status"] == mock_report_2.status
        assert data["data"][1]["progress"] == mock_report_2.progress

        # Verify the pipeline method was called
        mock_get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_reports_paginated_with_filters(self, auth_client, mock_user, mock_report, mock_n1_process_pipeline, monkeypatch):
        """Test getting paginated reports with filters."""
        # Mock filtered response (only completed reports)
        mock_report_filtered = N1ReportGenRequest(
            id=mock_report.id,
            user_id=mock_user.id,
            status="COMPLETED",
            progress=100,
            url="https://example.com/test.pdf",
            file_name="test.pdf",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        mock_paginated_response = PaginatedReportResponse(
            status="success",
            user_id=mock_user.id,
            total_count=1,
            page=1,
            page_size=10,
            total_pages=1,
            data=[mock_report_filtered]
        )

        # Mock the get_user_reports_paginated method
        mock_get_paginated = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(type(mock_n1_process_pipeline), "get_user_reports_paginated", mock_get_paginated)

        # Test with filters
        response = auth_client.get(
            f"/reports/paginated?user_id={mock_user.id}&page=1&page_size=10&status=COMPLETED&sort_by=created_at&is_descending=true"
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 1
        assert len(data["data"]) == 1

        # Verify filtered report
        assert data["data"][0]["status"] == "COMPLETED"
        assert data["data"][0]["progress"] == 100

        # Verify the pipeline method was called
        mock_get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_reports_paginated_empty_result(self, auth_client, mock_user, mock_report, mock_n1_process_pipeline, monkeypatch):
        """Test getting paginated reports with no results."""
        # Mock empty response
        mock_paginated_response = PaginatedReportResponse(
            status="success",
            user_id=mock_user.id,
            total_count=0,
            page=1,
            page_size=10,
            total_pages=0,
            data=[]
        )

        # Mock the get_user_reports_paginated method
        mock_get_paginated = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(type(mock_n1_process_pipeline), "get_user_reports_paginated", mock_get_paginated)

        # Test with filters that return no results
        response = auth_client.get(
            f"/reports/paginated?user_id={mock_user.id}&page=1&page_size=10&status=NONEXISTENT"
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify empty response structure
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 10
        assert data["total_pages"] == 0
        assert len(data["data"]) == 0

        # Verify the pipeline method was called
        mock_get_paginated.assert_called_once()
