import pytest
from fastapi import status
from unittest.mock import MagicMock, AsyncMock
import uuid

from models import LoincRecordORM
from schemas import LoincRecord


@pytest.mark.integration
class TestLoincEndpoints:

    @pytest.fixture
    def mock_loinc_records(self, db_session):
        """Get LOINC records from the database.

        This fixture returns LOINC records that should have been loaded from the CSV file.
        If they don't exist, it creates them as a fallback.
        """
        # Check if records already exist
        existing_records = db_session.query(LoincRecordORM).all()

        if existing_records:
            print(f"Using {len(existing_records)} existing LOINC records")
            return existing_records

        # If no records exist, create a few test LOINC records as fallback
        print("No existing LOINC records found, creating test records")
        records = [
            LoincRecordORM(
                loinc_num="100000-9",
                component="Health informatics pioneer and the father of LOINC",
                property="Hx",
                time_aspct="Pt",
                system="^Patient",
                scale_typ="Nar",
                method_typ="",
                class_="H&P.HX",
                classtype=2,
                long_common_name="Health informatics pioneer and the father of LOINC",
                shortname="Health Info Pioneer+Father of LOINC",
                external_copyright_notice="",
                status="ACTIVE",
                version_first_released="2.74",
                version_last_changed="2.74"
            ),
            LoincRecordORM(
                loinc_num="10000-8",
                component="R wave duration.lead AVR",
                property="Time",
                time_aspct="Pt",
                system="Heart",
                scale_typ="Qn",
                method_typ="EKG",
                class_="EKG.MEAS",
                classtype=2,
                long_common_name="R wave duration in lead AVR",
                shortname="R wave dur L-AVR",
                external_copyright_notice="",
                status="ACTIVE",
                version_first_released="1.0i",
                version_last_changed="2.48"
            )
        ]

        # Add records to the database
        for record in records:
            db_session.add(record)

        db_session.commit()

        # Return the records for use in tests
        return records

    def test_get_loinc_record_by_code(self, auth_client, mock_loinc_records):
        """Test getting a LOINC record by its code."""
        # Use one of the records from the sample CSV
        loinc_num = "100000-9"

        response = auth_client.get(f"/records/loinc/{loinc_num}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["loinc_num"] == loinc_num
        assert "component" in data
        assert "shortname" in data
        assert "long_common_name" in data
        assert data["status"] == "ACTIVE"

    def test_get_loinc_record_not_found(self, auth_client):
        """Test getting a non-existent LOINC record."""
        non_existent_code = "99999-9"

        response = auth_client.get(f"/records/loinc/{non_existent_code}")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()