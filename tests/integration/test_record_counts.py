import pytest
import uuid
from fastapi import status,HTTPException
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from schemas import RecordCounts


@pytest.mark.integration
class TestRecordCounts:

    @pytest.mark.asyncio
    async def test_get_record_counts(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting record counts for a specific record."""
        record_id = f"mr-{str(uuid.uuid4())}"
        
        mock_counts = RecordCounts(
            record_id=record_id,
            biomarkers=5,
            diagnosis=2,
            genetics=1,
            procedures=3
        )
        mock_get_counts = AsyncMock(return_value=mock_counts)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_record_counts", mock_get_counts)

        response = auth_client.get(f"/records/counts/{record_id}")

        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["record_id"] == record_id
        assert data["biomarkers"] == 5
        assert data["diagnosis"] == 2
        assert data["genetics"] == 1
        assert data["procedures"] == 3

        mock_get_counts.assert_called_once_with(record_id)
        

    @pytest.mark.asyncio
    async def test_get_record_id_not_found(self,auth_client,mock_n1_process_pipeline,monkeypatch):
        """Requesting a record_id that doesn’t exist should yield a 404."""
        record_id = f"mr-{uuid.uuid4()}"

        mock_get_counts = AsyncMock(
            side_effect=HTTPException(
                status_code=404,
                detail=f"Record {record_id} not found"
            )
        )
        monkeypatch.setattr(
            mock_n1_process_pipeline,
            "get_record_counts",
            mock_get_counts
        )

        response = auth_client.get(f"/records/counts/{record_id}")

        assert response.status_code == status.HTTP_404_NOT_FOUND, response.text
        data = response.json()
        assert data["detail"] == f"Record {record_id} not found"

        mock_get_counts.assert_called_once_with(record_id)


    @pytest.mark.asyncio
    async def test_get_record_counts_zero_counts(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting record counts when no data exists for the record."""
        record_id = f"mr-{str(uuid.uuid4())}"
        
        mock_counts = RecordCounts(
            record_id=record_id,
            biomarkers=0,
            diagnosis=0,
            genetics=0,
            procedures=0
        )
        mock_get_counts = AsyncMock(return_value=mock_counts)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_record_counts", mock_get_counts)

        response = auth_client.get(f"/records/counts/{record_id}")

        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["record_id"] == record_id
        assert data["biomarkers"] == 0
        assert data["diagnosis"] == 0
        assert data["genetics"] == 0
        assert data["procedures"] == 0

        mock_get_counts.assert_called_once_with(record_id)

    @pytest.mark.asyncio
    async def test_get_record_counts_service_error(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting record counts when service throws an error."""
        record_id = f"mr-{str(uuid.uuid4())}"

        mock_get_counts = AsyncMock(side_effect=Exception("Database connection error"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_record_counts", mock_get_counts)

        response = auth_client.get(f"/records/counts/{record_id}")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR, response.text
        data = response.json()
        assert "detail" in data
        assert "Error fetching record counts" in data["detail"]

        mock_get_counts.assert_called_once_with(record_id)