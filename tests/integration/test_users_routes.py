import pytest
import uuid
from fastapi import status, HTTPException # Import HTTPException
from unittest.mock import MagicMock, patch, AsyncMock # Import AsyncMock
import uuid

from models import UserORM
from schemas import UserRegistration, User


@pytest.mark.integration
class TestUsersRoutes:

    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    # Add monkeypatch fixture
    def test_get_user_details(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch): 
        """Test getting user details."""
        # Mock the get_user_details method using monkeypatch
        mock_get_details = MagicMock(return_value=mock_user)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_details", mock_get_details)

        response = auth_client.get(f"/users/{mock_user.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(mock_user.id)
        assert data["bubble_id"] == mock_user.bubble_id
        assert data["email"] == mock_user.email
        assert data["bucket_name"] == mock_user.bucket_name

    # Add monkeypatch fixture
    def test_get_user_details_not_found(self, auth_client, mock_n1_process_pipeline, monkeypatch): 
        """Test getting user details for a non-existent user."""
        # Mock get_user_details to raise HTTPException using monkeypatch
        # HTTPException is now imported at the top
        mock_get_details = MagicMock(side_effect=HTTPException(status_code=404, detail="User not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_details", mock_get_details)

        non_existent_id = str(uuid.uuid4())
        response = auth_client.get(f"/users/{non_existent_id}")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "User not found" in response.json()["detail"]
    
    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_register_user(self, auth_client, mock_storage_service, mock_n1_process_pipeline, monkeypatch): 
        """Test registering a new user."""
        user_data = {
            "bubble_id": "87654321",
            "email": "<EMAIL>",
            "develop_mode": False
        }

        # Mock the create_new_bucket method
        mock_storage_service.create_new_bucket.return_value = "new-test-bucket"
        
        # Mock the register_user method
        new_user = MagicMock()
        new_user.id = uuid.uuid4()
        new_user.bubble_id = user_data["bubble_id"]
        new_user.email = user_data["email"]
        new_user.bucket_name = "new-test-bucket"
        # Mock register_user using monkeypatch and AsyncMock
        # AsyncMock is now imported at the top
        mock_register = AsyncMock(return_value=new_user)
        monkeypatch.setattr(mock_n1_process_pipeline, "register_user", mock_register)
        
        # No need to mock check_bubble_id_exists, the check happens inside register_user

        response = auth_client.post("/users/register", json=user_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["bubble_id"] == user_data["bubble_id"]
        assert data["email"] == user_data["email"]
        # assert data["bucket_name"] == "new-test-bucket" # Check existence and type instead
        assert "bucket_name" in data
        assert isinstance(data["bucket_name"], str)
        assert len(data["bucket_name"]) > 0
    
    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_register_user_duplicate_bubble_id(self, auth_client, mock_user, mock_storage_service, mock_n1_process_pipeline, monkeypatch): 
        """Test registering a user with a duplicate bubble_id."""
        user_data = {
            "bubble_id": mock_user.bubble_id,  # Same bubble_id as mock_user
            "email": "<EMAIL>",
            "develop_mode": False
        }
        
        # Mock register_user to raise the 409 conflict exception
        # Imports are now at the top
        mock_register = AsyncMock(side_effect=HTTPException(status_code=409, detail="User with this bubble_id already exists"))
        monkeypatch.setattr(mock_n1_process_pipeline, "register_user", mock_register)

        response = auth_client.post("/users/register", json=user_data)

        assert response.status_code == status.HTTP_409_CONFLICT
        assert "bubble_id already exists" in response.json()["detail"]
    
    # Add monkeypatch fixture
    def test_delete_user_data(self, auth_client, mock_user, mock_storage_service, mock_n1_process_pipeline, monkeypatch): 
        """Test deleting all user data."""
        # Mock the delete_user_data method using monkeypatch
        mock_delete_response = {
            "status": "success",
            "user_id": str(mock_user.id),
            "database": {
                "records_deleted": True,
                "reports_deleted": True
            },
            "files_deleted": 5
        }
        mock_delete = MagicMock(return_value=mock_delete_response)
        # Add comma
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_user_data", mock_delete)
        
        # Mock the get_user_details method (used by the route to verify user exists)
        mock_get_details = MagicMock(return_value=mock_user)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_details", mock_get_details)
        
        response = auth_client.delete(f"/users/{mock_user.id}/data")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["database"]["records_deleted"] is True
        assert data["database"]["reports_deleted"] is True
        assert data["files_deleted"] == 5
    
    @pytest.mark.asyncio
    # Add monkeypatch fixture
    async def test_get_user_stats(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch): 
        """Test getting user statistics."""
        # Mock the get_user_stats method using monkeypatch and AsyncMock
        # Import UserStats to create a proper mock response
        from schemas import UserStats
        mock_stats_response = UserStats(
            report_count=5,
            record_count=10,
            biomarker_count=50,
            medication_count=3,
            procedure_count=2,
            diagnosis_count=1,
            genetics_count=0
        )
        # Add comma, AsyncMock is imported at top
        mock_get_stats = AsyncMock(return_value=mock_stats_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_stats", mock_get_stats)
        
        # Mock the get_user_details method (used by the route to verify user exists)
        mock_get_details = MagicMock(return_value=mock_user)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_details", mock_get_details)
        
        response = auth_client.get(f"/users/{mock_user.id}/stats")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "report_count" in data
        assert "record_count" in data
        assert "biomarker_count" in data
        assert "medication_count" in data
        assert "procedure_count" in data
        assert "diagnosis_count" in data
        assert "genetics_count" in data
        assert isinstance(data["report_count"], int)
        assert isinstance(data["record_count"], int)
        assert isinstance(data["biomarker_count"], int)
        assert isinstance(data["medication_count"], int)
        assert isinstance(data["procedure_count"], int)
        assert isinstance(data["diagnosis_count"], int)
        assert isinstance(data["genetics_count"], int)
        # Verify the actual values match our mock
        assert data["report_count"] == 5
        assert data["record_count"] == 10
        assert data["biomarker_count"] == 50
        assert data["medication_count"] == 3
        assert data["procedure_count"] == 2
        assert data["diagnosis_count"] == 1
        assert data["genetics_count"] == 0
