import pytest
import uuid
from fastapi import status
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from models import UserDiagnosisORM
from schemas import UserDiagnosis<PERSON>reate, UserDiagnosisBase


@pytest.mark.integration
class TestDiagnosesRoutes:

    @pytest.fixture
    def mock_diagnosis(self, db_session, mock_user):
        """Create a mock diagnosis in the database."""
        diagnosis_id = uuid.uuid4()
        diagnosis = UserDiagnosisORM(
            id=diagnosis_id,
            user_id=mock_user.id,
            record_id="record123",
            name="Test Diagnosis",
            icd_code="A00.0",
            snomed_code="123456",
            page_number=1,
            date_diagnosed=datetime.now(timezone.utc),
            date_resolved=None,
            status="active",
            explanation="Test explanation",
        )
        db_session.add(diagnosis)
        db_session.commit()
        db_session.refresh(diagnosis)
        return diagnosis

    @pytest.mark.asyncio
    async def test_create_user_diagnosis(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating a new diagnosis."""
        # Prepare test data
        diagnosis_data = {
            "user_id": str(mock_user.id),
            "record_id": "record123",
            "name": "New Diagnosis",
            "icd_code": "B01.1",
            "snomed_code": "654321",
            "page_number": 2,
            "date_diagnosed": datetime.now(timezone.utc).isoformat(),
            "status": "active",
            "explanation": "New explanation",
        }

        # Mock the create_diagnosis method
        new_diagnosis = MagicMock()
        new_diagnosis.id = uuid.uuid4()
        new_diagnosis.user_id = mock_user.id
        new_diagnosis.record_id = diagnosis_data["record_id"]
        new_diagnosis.name = diagnosis_data["name"]
        new_diagnosis.icd_code = diagnosis_data["icd_code"]
        new_diagnosis.snomed_code = diagnosis_data["snomed_code"]
        new_diagnosis.page_number = diagnosis_data["page_number"]
        new_diagnosis.date_diagnosed = datetime.fromisoformat(diagnosis_data["date_diagnosed"])
        new_diagnosis.date_resolved = None
        new_diagnosis.status = diagnosis_data["status"]
        new_diagnosis.explanation = diagnosis_data["explanation"]
        new_diagnosis.additional_data = None
        new_diagnosis.created_at = datetime.now(timezone.utc)
        new_diagnosis.updated_at = datetime.now(timezone.utc)

        mock_create = AsyncMock(return_value=new_diagnosis)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_diagnosis", mock_create)

        # Make the request
        response = auth_client.post("/diagnoses/", json=diagnosis_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == diagnosis_data["name"]
        assert data["icd_code"] == diagnosis_data["icd_code"]
        assert data["snomed_code"] == diagnosis_data["snomed_code"]
        assert data["page_number"] == diagnosis_data["page_number"]
        assert data["status"] == diagnosis_data["status"]
        assert data["explanation"] == diagnosis_data["explanation"]
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

        # Verify the service method was called with correct arguments
        mock_create.assert_called_once()
        call_args = mock_create.call_args[0][0]
        assert call_args.user_id == uuid.UUID(diagnosis_data["user_id"])
        assert call_args.record_id == diagnosis_data["record_id"]
        assert call_args.name == diagnosis_data["name"]
        assert call_args.icd_code == diagnosis_data["icd_code"]
        assert call_args.snomed_code == diagnosis_data["snomed_code"]
        assert call_args.page_number == diagnosis_data["page_number"]
        assert call_args.status == diagnosis_data["status"]
        assert call_args.explanation == diagnosis_data["explanation"]

    @pytest.mark.asyncio
    async def test_get_user_diagnoses(self, auth_client, mock_user, mock_diagnosis, mock_n1_process_pipeline, monkeypatch):
        """Test getting all diagnoses for a user with pagination."""
        # Mock the get_user_diagnoses_paginated method
        mock_paginated_response = {
            "status": "success",
            "user_id": str(mock_user.id),
            "total_count": 1,
            "page": 1,
            "page_size": 20,
            "total_pages": 1,
            "data": [mock_diagnosis]
        }
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_diagnoses_paginated", mock_get)

        # Make the request
        response = auth_client.get(f"/diagnoses/paginated?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, dict)
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 1
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert data["total_pages"] == 1
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1
        assert data["data"][0]["name"] == mock_diagnosis.name
        assert data["data"][0]["icd_code"] == mock_diagnosis.icd_code
        assert data["data"][0]["snomed_code"] == mock_diagnosis.snomed_code
        assert data["data"][0]["page_number"] == mock_diagnosis.page_number
        assert data["data"][0]["status"] == mock_diagnosis.status
        assert data["data"][0]["explanation"] == mock_diagnosis.explanation
        assert str(mock_diagnosis.id) == data["data"][0]["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params = call_args[0][0]  # The first argument is the query params object
        assert query_params.user_id == mock_user.id
        # Verify the query_params object
        assert query_params.page == 1
        assert query_params.page_size == 20
        assert query_params.sort_by == "created_at"
        assert query_params.is_descending == False

    @pytest.mark.asyncio
    async def test_get_user_diagnosis(self, auth_client, mock_user, mock_diagnosis, mock_n1_process_pipeline, monkeypatch):
        """Test getting a specific diagnosis."""
        # Mock the get_diagnosis method
        mock_get = AsyncMock(return_value=mock_diagnosis)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_diagnosis", mock_get)

        # Make the request
        response = auth_client.get(f"/diagnoses/{mock_diagnosis.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == mock_diagnosis.name
        assert data["icd_code"] == mock_diagnosis.icd_code
        assert data["snomed_code"] == mock_diagnosis.snomed_code
        assert data["page_number"] == mock_diagnosis.page_number
        assert data["status"] == mock_diagnosis.status
        assert data["explanation"] == mock_diagnosis.explanation
        assert str(mock_diagnosis.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, mock_diagnosis.id)

    @pytest.mark.asyncio
    async def test_update_user_diagnosis(self, auth_client, mock_user, mock_diagnosis, mock_n1_process_pipeline, monkeypatch):
        """Test updating a diagnosis."""
        # Prepare update data
        update_data = {
            "name": "Updated Diagnosis",
            "status": "resolved",
            "date_resolved": datetime.now(timezone.utc).isoformat(),
            "explanation": "Updated explanation"
        }

        # Mock the update_diagnosis method
        updated_diagnosis = MagicMock()
        updated_diagnosis.id = mock_diagnosis.id
        updated_diagnosis.user_id = mock_user.id
        updated_diagnosis.record_id = mock_diagnosis.record_id
        updated_diagnosis.name = update_data["name"]
        updated_diagnosis.icd_code = mock_diagnosis.icd_code
        updated_diagnosis.snomed_code = mock_diagnosis.snomed_code
        updated_diagnosis.page_number = mock_diagnosis.page_number
        updated_diagnosis.date_diagnosed = mock_diagnosis.date_diagnosed
        updated_diagnosis.date_resolved = datetime.fromisoformat(update_data["date_resolved"])
        updated_diagnosis.status = update_data["status"]
        updated_diagnosis.explanation = update_data["explanation"]
        updated_diagnosis.additional_data = None
        updated_diagnosis.created_at = mock_diagnosis.created_at
        updated_diagnosis.updated_at = datetime.now(timezone.utc)

        mock_update = AsyncMock(return_value=updated_diagnosis)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_diagnosis", mock_update)

        # Make the request
        response = auth_client.patch(f"/diagnoses/{mock_diagnosis.id}?user_id={mock_user.id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["status"] == update_data["status"]
        assert data["explanation"] == update_data["explanation"]
        assert "date_resolved" in data
        assert data["icd_code"] == mock_diagnosis.icd_code  # Unchanged field
        assert str(mock_diagnosis.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_update.assert_called_once()
        call_args = mock_update.call_args
        assert call_args[0][0] == mock_user.id
        assert call_args[0][1] == mock_diagnosis.id
        assert call_args[0][2].name == update_data["name"]
        assert call_args[0][2].status == update_data["status"]
        assert call_args[0][2].explanation == update_data["explanation"]
        assert call_args[0][2].date_resolved is not None

    @pytest.mark.asyncio
    async def test_delete_user_diagnosis(self, auth_client, mock_user, mock_diagnosis, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a diagnosis."""
        # Mock the delete_diagnosis method
        mock_delete_response = {
            "status": "success",
            "message": f"Diagnosis {mock_diagnosis.id} deleted successfully"
        }
        mock_delete = AsyncMock(return_value=mock_delete_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_diagnosis", mock_delete)

        # Make the request
        response = auth_client.delete(f"/diagnoses/{mock_diagnosis.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert "deleted successfully" in data["message"]

        # Verify the service method was called with correct arguments
        mock_delete.assert_called_once_with(mock_user.id, mock_diagnosis.id)

    @pytest.mark.asyncio
    async def test_create_user_diagnosis_validation_error(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating a diagnosis with invalid data."""
        # Mock the create_diagnosis method to raise HTTPException for user not found
        from fastapi import HTTPException
        mock_create = AsyncMock(side_effect=HTTPException(status_code=404, detail="User not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "create_diagnosis", mock_create)

        # Prepare invalid data (missing required fields)
        invalid_data = {
            "user_id": str(mock_user.id),
            "name": "Invalid Diagnosis"
            # Missing optional fields: record_id, date_diagnosed, status
        }

        # Make the request
        response = auth_client.post("/diagnoses/", json=invalid_data)

        # Since record_id is optional in the schema, the request will reach the service
        # and fail with user not found error (500 -> 404 in this case)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "User not found" in data["detail"]

    @pytest.mark.asyncio
    async def test_get_user_diagnosis_not_found(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting a non-existent diagnosis."""
        # Generate a random UUID for a non-existent diagnosis
        non_existent_id = uuid.uuid4()

        # Mock the get_diagnosis method to raise HTTPException
        from fastapi import HTTPException
        mock_get = AsyncMock(side_effect=HTTPException(status_code=404, detail="Diagnosis not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_diagnosis", mock_get)

        # Make the request
        response = auth_client.get(f"/diagnoses/{non_existent_id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, non_existent_id)

    @pytest.mark.asyncio
    async def test_create_user_diagnoses_bulk(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating multiple diagnoses in bulk."""
        # Prepare test data
        diagnoses_data = [
            {
                "name": "Bulk Diagnosis 1",
                "icd_code": "C01.1",
                "snomed_code": "111222",
                "page_number": 3,
                "date_diagnosed": datetime.now(timezone.utc).isoformat(),
                "status": "active",
                "explanation": "Bulk explanation 1",
            },
            {
                "name": "Bulk Diagnosis 2",
                "icd_code": "D02.2",
                "snomed_code": "333444",
                "page_number": 4,
                "date_diagnosed": datetime.now(timezone.utc).isoformat(),
                "status": "active",
                "explanation": "Bulk explanation 2",
            },
        ]

        bulk_request_data = {
            "user_id": str(mock_user.id),
            "record_id": "record456",
            "diagnoses": diagnoses_data,
        }

        # Mock the create_diagnoses_bulk method
        created_diagnoses = []
        for diag_data in diagnoses_data:
            new_diag = MagicMock()
            new_diag.id = uuid.uuid4()
            new_diag.user_id = mock_user.id
            new_diag.record_id = bulk_request_data["record_id"]
            new_diag.name = diag_data["name"]
            new_diag.icd_code = diag_data["icd_code"]
            new_diag.snomed_code = diag_data["snomed_code"]
            new_diag.page_number = diag_data["page_number"]
            new_diag.date_diagnosed = datetime.fromisoformat(diag_data["date_diagnosed"])
            new_diag.date_resolved = None
            new_diag.status = diag_data["status"]
            new_diag.explanation = diag_data["explanation"]
            new_diag.additional_data = None
            new_diag.created_at = datetime.now(timezone.utc)
            new_diag.updated_at = datetime.now(timezone.utc)
            created_diagnoses.append(new_diag)

        mock_create_bulk = AsyncMock(return_value=created_diagnoses)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_diagnoses_bulk", mock_create_bulk)

        # Make the request
        response = auth_client.post("/diagnoses/bulk", json=bulk_request_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 2
        assert data[0]["name"] == diagnoses_data[0]["name"]
        assert data[1]["name"] == diagnoses_data[1]["name"]

        # Verify the service method was called with correct arguments
        mock_create_bulk.assert_called_once()
        call_args = mock_create_bulk.call_args[0][0]
        assert str(call_args.user_id) == bulk_request_data["user_id"]
        assert call_args.record_id == bulk_request_data["record_id"]
        assert len(call_args.diagnoses) == 2
        assert call_args.diagnoses[0].name == diagnoses_data[0]["name"]
        assert call_args.diagnoses[1].name == diagnoses_data[1]["name"]

    @pytest.mark.asyncio
    async def test_get_user_diagnoses_with_filters_and_sorting(self, auth_client, mock_user, mock_diagnosis, mock_n1_process_pipeline, monkeypatch):
        """Test getting diagnoses with filtering and sorting parameters."""
        # Mock the get_user_diagnoses_paginated method
        mock_paginated_response = {
            "status": "success",
            "user_id": str(mock_user.id),
            "total_count": 1,
            "page": 2,
            "page_size": 10,
            "total_pages": 1,
            "data": [mock_diagnosis]
        }
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_diagnoses_paginated", mock_get)

        # Make the request with various query parameters
        query_params = {
            "user_id": str(mock_user.id),
            "page": 2,
            "page_size": 10,
            "sort_by": "name",
            "is_descending": False,
            "record_id": "record123",
            "status": "active",
            "name": "diabetes",
            "icd_code": "E11",
            "date_diagnosed_from": "2023-01-01T00:00:00Z",
            "date_diagnosed_to": "2023-12-31T23:59:59Z"
        }

        query_string = "&".join([f"{k}={v}" for k, v in query_params.items()])
        response = auth_client.get(f"/diagnoses/paginated?{query_string}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert data["page"] == 2
        assert data["page_size"] == 10

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params_obj = call_args[0][0]  # The first argument is the query params object
        assert str(query_params_obj.user_id) == str(mock_user.id)  # user_id

        # Verify the query_params object has all the filtering parameters
        assert query_params_obj.page == 2
        assert query_params_obj.page_size == 10
        assert query_params_obj.sort_by == "name"
        assert query_params_obj.is_descending == False  # "asc" should map to False
        assert query_params_obj.record_id == "record123"
        assert query_params_obj.status == "active"
        assert query_params_obj.name == "diabetes"
        assert query_params_obj.icd_code == "E11"
        assert query_params_obj.date_diagnosed_from is not None
        assert query_params_obj.date_diagnosed_to is not None
