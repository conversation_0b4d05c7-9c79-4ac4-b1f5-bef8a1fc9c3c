import pytest
from fastapi import status
from fastapi.testclient import TestClient
import base64
import base64

from main import app
from config import USERNAME, PASSWORD


@pytest.mark.integration
class TestMainApp:

    def test_root_endpoint_with_auth(self, auth_client):
        """Test accessing the root endpoint with authentication."""
        # Make request using the authenticated client fixture
        response = auth_client.get("/")
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        assert "html" in response.text.lower()
    
    def test_root_endpoint_without_auth(self, client):
        """Test accessing the root endpoint without authentication."""
        response = client.get("/")
        
        # Should return 401 Unauthorized
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Not authenticated" in response.text
    
    def test_docs_endpoint_with_auth(self, auth_client): # Changed client to auth_client
        """Test accessing the docs endpoint with authentication."""
        # Make request using the authenticated client fixture
        response = auth_client.get("/docs") # Removed headers argument
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        assert "swagger" in response.text.lower()
    
    def test_docs_endpoint_without_auth(self, client):
        """Test accessing the docs endpoint without authentication."""
        response = client.get("/docs")
        
        # Should return 401 Unauthorized
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Not authenticated" in response.text
    
    def test_openapi_endpoint_with_auth(self, auth_client): # Changed client to auth_client
        """Test accessing the OpenAPI endpoint with authentication."""
        # Make request using the authenticated client fixture
        response = auth_client.get("/openapi.json") # Removed headers argument
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "application/json"
        
        # Verify OpenAPI schema contains expected elements
        schema = response.json()
        assert "openapi" in schema
        assert "paths" in schema
        assert "components" in schema
        assert "/users/{user_id}" in schema["paths"]
        assert "/records/user/{user_id}" in schema["paths"]
        # Check for the actual path that exists in the API
        assert "/records/{user_id}/{record_id}" in schema["paths"] or "/records/status" in schema["paths"]
    
    def test_openapi_endpoint_without_auth(self, client):
        """Test accessing the OpenAPI endpoint without authentication."""
        response = client.get("/openapi.json")
        
        # Should return 401 Unauthorized
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Not authenticated" in response.text
    
    def test_invalid_auth_header(self, client):
        """Test accessing a protected endpoint with invalid auth header."""
        # Create invalid auth header
        auth_header = {"Authorization": "Basic invalid_token"}
        
        # Make request
        response = client.get("/docs", headers=auth_header)
        
        # Should return 401 Unauthorized
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication failed" in response.text
    
    def test_wrong_auth_scheme(self, client):
        """Test accessing a protected endpoint with wrong auth scheme."""
        # Create wrong auth scheme header
        auth_header = {"Authorization": "Bearer some_token"}
        
        # Make request
        response = client.get("/docs", headers=auth_header)
        
        # Should return 401 Unauthorized
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication failed" in response.text
