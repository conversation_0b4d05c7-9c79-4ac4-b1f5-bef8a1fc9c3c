import pytest
import uuid
from fastapi import status
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from models import ClinicalDataORM
from models import UserBiomarkerORM
from schemas import UserBiomarkerCreate, UserBiomarkerBase


@pytest.mark.integration
class TestBiomarkersRoutes:

    @pytest.fixture
    def mock_biomarker(self, db_session, mock_user):
        """Create a mock biomarker in the database."""
        biomarker = UserBiomarkerORM(
            id=uuid.uuid4(),
            user_id=mock_user.id,
            record_id="record123",
            test_name="Glucose",
            expanded_test_name="Blood Glucose",
            result="120",
            reference_range="70-99",
            unit="mg/dL",
            context="Diabetes",
            canonical_id=uuid.uuid4(),
            edited=False,
            excluded=False,
            test_date=datetime.now(),
            sample_source="Blood",
            method="Lab",
            additional_data=None,
            page_number=1,
            result_numeric=120.0,
            reference_range_min=70.0,
            reference_range_max=99.0,
            converted_reference_range_min = 60,
            converted_reference_range_max = 90,
            converted_result_numeric = 90,
            out_of_range=True
        )
        db_session.add(biomarker)
        db_session.commit()
        db_session.refresh(biomarker)
        return biomarker

    @pytest.mark.asyncio
    async def test_create_user_biomarker(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating a new biomarker."""
        # Prepare test data
        biomarker_data = {
            "user_id": str(mock_user.id),
            "record_id": "record123",
            "test_name": "New Biomarker",
            "result": "Elevated",
            "reference_range": "20-80",
            "unit": "mg/dL",
            "page_number": 2,
            "context": "New context",
            "expanded_test_name": "Expanded Name",
            "filename": "file.pdf",
            "canonical_id": str(uuid.uuid4()),
        }

        # Mock the create_biomarker method
        new_biomarker = MagicMock()
        new_biomarker.id = uuid.uuid4()
        new_biomarker.user_id = str(mock_user.id)
        new_biomarker.record_id = biomarker_data["record_id"]
        new_biomarker.test_name = biomarker_data["test_name"]
        new_biomarker.result = biomarker_data["result"]
        new_biomarker.reference_range = biomarker_data["reference_range"]
        new_biomarker.sample_source = "Blood"
        new_biomarker.method = "Lab Test"
        new_biomarker.unit = biomarker_data["unit"]
        new_biomarker.page_number = biomarker_data["page_number"]
        new_biomarker.context = biomarker_data["context"]
        new_biomarker.canonical_id = biomarker_data["canonical_id"]
        new_biomarker.expanded_test_name = biomarker_data["expanded_test_name"]
        new_biomarker.filename = biomarker_data["filename"]
        new_biomarker.edited = False
        new_biomarker.excluded = False
        new_biomarker.additional_data = None
        new_biomarker.created_at = datetime.now(timezone.utc)
        new_biomarker.updated_at = datetime.now(timezone.utc)
        new_biomarker.canonical_record = {"canonical_name": "New Canonical Name"}

        mock_create = AsyncMock(return_value=new_biomarker)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_biomarker", mock_create)

        # Make the request
        response = auth_client.post("/biomarkers/", json=biomarker_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["test_name"] == biomarker_data["test_name"]
        assert data["result"] == biomarker_data["result"]
        assert data["reference_range"] == biomarker_data["reference_range"]
        assert data["unit"] == biomarker_data["unit"]
        assert data["page_number"] == biomarker_data["page_number"]
        assert data["context"] == biomarker_data["context"]
        assert data["canonical_id"] == biomarker_data["canonical_id"]
        assert data["canonical_record"]["canonical_name"] == "New Canonical Name"
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

        # Verify the service method was called with correct arguments
        mock_create.assert_called_once()
        call_args = mock_create.call_args[0][0]
        assert str(call_args.user_id) == biomarker_data["user_id"]
        assert call_args.record_id == biomarker_data["record_id"]
        assert call_args.test_name == biomarker_data["test_name"]
        assert call_args.result == biomarker_data["result"]
        assert call_args.reference_range == biomarker_data["reference_range"]
        assert call_args.unit == biomarker_data["unit"]
        assert call_args.page_number == biomarker_data["page_number"]
        assert call_args.context == biomarker_data["context"]
        assert str(call_args.canonical_id) == biomarker_data["canonical_id"]

    @pytest.mark.asyncio
    async def test_get_user_biomarkers(self, auth_client, mock_user, mock_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting all biomarkers for a user."""
        # Mock the get_user_biomarkers method
        mock_biomarkers = [mock_biomarker]
        mock_biomarker.canonical_record = {"canonical_name": "Canonical Name"}
        mock_get = AsyncMock(return_value=mock_biomarkers)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_biomarkers", mock_get)

        # Make the request
        response = auth_client.get(f"/biomarkers/?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        assert data[0]["test_name"] == mock_biomarker.test_name
        assert data[0]["result"] == mock_biomarker.result
        assert data[0]["reference_range"] == mock_biomarker.reference_range
        assert data[0]["unit"] == mock_biomarker.unit
        assert data[0]["page_number"] == mock_biomarker.page_number
        assert data[0]["context"] == mock_biomarker.context
        assert data[0]["canonical_id"] == str(mock_biomarker.canonical_id)
        assert data[0]["canonical_record"]["canonical_name"] == "Canonical Name"
        assert str(mock_biomarker.id) == data[0]["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id)

    @pytest.mark.asyncio
    async def test_get_user_biomarker(self, auth_client, mock_user, mock_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting a specific biomarker."""
        # Mock the get_biomarker method
        mock_biomarker.canonical_record = {"canonical_name": "Canonical Name"}
        mock_get = AsyncMock(return_value=mock_biomarker)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_biomarker", mock_get)

        # Make the request
        response = auth_client.get(f"/biomarkers/{mock_biomarker.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["test_name"] == mock_biomarker.test_name
        assert data["result"] == mock_biomarker.result
        assert data["reference_range"] == mock_biomarker.reference_range
        assert data["unit"] == mock_biomarker.unit
        assert data["page_number"] == mock_biomarker.page_number
        assert data["context"] == mock_biomarker.context
        assert data["canonical_id"] == str(mock_biomarker.canonical_id)
        assert data["canonical_record"]["canonical_name"] == "Canonical Name"
        assert str(mock_biomarker.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, mock_biomarker.id)

    @pytest.mark.asyncio
    async def test_update_user_biomarker(self, auth_client, mock_user, mock_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test updating a biomarker."""
        # Prepare update data
        update_data = {
            "user_id": str(mock_user.id),
            "record_id": "mr-19afefcd-8397-44ff-a21c-0223ffdafcb3",
            "test_name": "Updated Biomarker",
            "result": "High",
            "reference_range": "15-75",
            "unit": "mmol/L",
            "context": "Updated context"
        }

        # Mock the update_biomarker method
        updated_biomarker = MagicMock()
        updated_biomarker.id = mock_biomarker.id
        updated_biomarker.user_id = str(mock_user.id)
        updated_biomarker.record_id = mock_biomarker.record_id
        updated_biomarker.test_name = update_data["test_name"]
        updated_biomarker.result = update_data["result"]
        updated_biomarker.reference_range = update_data["reference_range"]
        updated_biomarker.sample_source = "Blood"
        updated_biomarker.method = "Lab Test"
        updated_biomarker.unit = update_data["unit"]
        updated_biomarker.page_number = mock_biomarker.page_number
        updated_biomarker.context = update_data["context"]
        updated_biomarker.canonical_id = mock_biomarker.canonical_id
        updated_biomarker.expanded_test_name = "Expanded Name"
        updated_biomarker.filename = "file.pdf"
        updated_biomarker.edited = True
        updated_biomarker.excluded = False
        updated_biomarker.additional_data = None
        updated_biomarker.created_at = mock_biomarker.created_at
        updated_biomarker.updated_at = datetime.now(timezone.utc)
        updated_biomarker.canonical_record = {"canonical_name": "Canonical Name"}

        mock_update = AsyncMock(return_value=updated_biomarker)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_biomarker", mock_update)

        # Make the request
        response = auth_client.patch(f"/biomarkers/{mock_biomarker.id}?user_id={mock_user.id}", json=update_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["test_name"] == update_data["test_name"]
        assert data["result"] == update_data["result"]
        assert data["reference_range"] == update_data["reference_range"]
        assert data["unit"] == update_data["unit"]
        assert data["context"] == update_data["context"]
        assert data["canonical_id"] == str(mock_biomarker.canonical_id)
        assert data["canonical_record"]["canonical_name"] == "Canonical Name"
        assert str(mock_biomarker.id) == data["id"]

        # Verify the service method was called with correct arguments
        mock_update.assert_called_once()
        call_args = mock_update.call_args
        assert call_args[0][0] == mock_user.id
        assert call_args[0][1] == mock_biomarker.id
        assert call_args[0][2].test_name == update_data["test_name"]
        assert call_args[0][2].result == update_data["result"]
        assert call_args[0][2].reference_range == update_data["reference_range"]
        assert call_args[0][2].unit == update_data["unit"]
        assert call_args[0][2].context == update_data["context"]

    @pytest.mark.asyncio
    async def test_delete_user_biomarker(self, auth_client, mock_user, mock_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test deleting a biomarker."""
        # Mock the delete_biomarker_record method
        mock_delete_response = {
            "status": "success",
            "message": f"Biomarker {mock_biomarker.id} deleted successfully"
        }
        mock_delete = AsyncMock(return_value=mock_delete_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_biomarker_record", mock_delete)

        # Make the request
        response = auth_client.delete(f"/biomarkers/{mock_biomarker.id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert "deleted successfully" in data["message"]

        # Verify the service method was called with correct arguments
        mock_delete.assert_called_once_with(mock_user.id, mock_biomarker.id)

    @pytest.mark.asyncio
    async def test_create_user_biomarker_validation_error(self, auth_client, mock_user):
        """Test creating a biomarker with invalid data."""
        # Prepare invalid data (missing required fields)
        invalid_data = {
            "user_id": str(mock_user.id),
            "test_name": "Invalid Biomarker"
            # Missing required fields: record_id, filename
        }

        # Make the request
        response = auth_client.post("/biomarkers/", json=invalid_data)

        # Verify the response indicates validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
        # Check that the error message mentions the missing fields
        error_fields = [error["loc"][-1] for error in data["detail"]]
        assert "record_id" in error_fields

    @pytest.mark.asyncio
    async def test_get_user_biomarker_not_found(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting a non-existent biomarker."""
        # Generate a random UUID for a non-existent biomarker
        non_existent_id = uuid.uuid4()

        # Mock the get_biomarker method to raise HTTPException
        from fastapi import HTTPException
        mock_get = AsyncMock(side_effect=HTTPException(status_code=404, detail="Biomarker not found"))
        monkeypatch.setattr(mock_n1_process_pipeline, "get_biomarker", mock_get)

        # Make the request
        response = auth_client.get(f"/biomarkers/{non_existent_id}?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once_with(mock_user.id, non_existent_id)

    @pytest.mark.asyncio
    async def test_create_user_biomarkers_bulk(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test creating multiple biomarkers in bulk."""
        # Prepare test data
        biomarkers_data = [
            {
                "test_name": "Bulk Biomarker 1",
                "result": "Normal",
                "reference_range": "10-50",
                "unit": "mg/dL",
                "page_number": 3,
                "context": "Bulk context 1",
                "expanded_test_name": "Expanded Name 1",
                "filename": "file1.pdf",
                "canonical_id": str(uuid.uuid4()),
            },
            {
                "test_name": "Bulk Biomarker 2",
                "result": "Elevated",
                "reference_range": "20-80",
                "unit": "mmol/L",
                "page_number": 4,
                "context": "Bulk context 2",
                "expanded_test_name": "Expanded Name 2",
                "filename": "file2.pdf",
                "canonical_id": str(uuid.uuid4()),
            },
        ]

        bulk_request_data = {
            "user_id": str(mock_user.id),
            "record_id": "record456",
            "biomarkers": biomarkers_data,
        }

        # Mock the create_biomarkers_bulk method
        created_biomarkers = []
        for biomarker_data in biomarkers_data:
            new_biomarker = MagicMock()
            new_biomarker.id = uuid.uuid4()
            new_biomarker.user_id = str(mock_user.id)
            new_biomarker.record_id = bulk_request_data["record_id"]
            new_biomarker.test_name = biomarker_data["test_name"]
            new_biomarker.result = biomarker_data["result"]
            new_biomarker.reference_range = biomarker_data["reference_range"]
            new_biomarker.sample_source = "Blood"
            new_biomarker.method = "Lab Test"
            new_biomarker.unit = biomarker_data["unit"]
            new_biomarker.page_number = biomarker_data["page_number"]
            new_biomarker.context = biomarker_data["context"]
            new_biomarker.canonical_id = biomarker_data["canonical_id"]
            new_biomarker.expanded_test_name = biomarker_data["expanded_test_name"]
            new_biomarker.filename = biomarker_data["filename"]
            new_biomarker.edited = False
            new_biomarker.excluded = False
            new_biomarker.additional_data = None
            new_biomarker.created_at = datetime.now(timezone.utc)
            new_biomarker.updated_at = datetime.now(timezone.utc)
            new_biomarker.canonical_record = {"canonical_name": f"Bulk Canonical Name {biomarkers_data.index(biomarker_data)+1}"}
            created_biomarkers.append(new_biomarker)

        mock_create_bulk = AsyncMock(return_value=created_biomarkers)
        monkeypatch.setattr(mock_n1_process_pipeline, "create_biomarkers_bulk", mock_create_bulk)

        # Make the request
        response = auth_client.post("/biomarkers/bulk", json=bulk_request_data)

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 2
        assert data[0]["test_name"] == biomarkers_data[0]["test_name"]
        assert data[1]["test_name"] == biomarkers_data[1]["test_name"]
        assert data[0]["canonical_id"] == biomarkers_data[0]["canonical_id"]
        assert data[1]["canonical_id"] == biomarkers_data[1]["canonical_id"]
        assert data[0]["canonical_record"]["canonical_name"] == "Bulk Canonical Name 1"
        assert data[1]["canonical_record"]["canonical_name"] == "Bulk Canonical Name 2"

        # Verify the service method was called with correct arguments
        mock_create_bulk.assert_called_once()
        call_args = mock_create_bulk.call_args[0][0]
        assert str(call_args.user_id) == bulk_request_data["user_id"]
        assert call_args.record_id == bulk_request_data["record_id"]
        assert len(call_args.biomarkers) == 2
        assert call_args.biomarkers[0].test_name == biomarkers_data[0]["test_name"]
        assert call_args.biomarkers[1].test_name == biomarkers_data[1]["test_name"]

    @pytest.mark.asyncio
    async def test_get_user_biomarkers_paginated(self, auth_client, mock_user, mock_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting all biomarkers for a user with pagination."""
        # Mock paginated response
        from schemas import PaginatedBiomarkerResponse
        mock_biomarker.canonical_record = {"canonical_name": "Canonical Name"}
        mock_paginated_response = PaginatedBiomarkerResponse(
            status="success",
            user_id=str(mock_user.id),
            total_count=1,
            page=1,
            page_size=20,
            total_pages=1,
            data=[mock_biomarker]
        )
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_biomarkers_paginated", mock_get)

        # Make the request
        response = auth_client.get(f"/biomarkers/paginated?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 1
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert data["total_pages"] == 1
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1
        assert data["data"][0]["canonical_record"]["canonical_name"] == "Canonical Name"
        assert data["data"][0]["canonical_id"] == str(mock_biomarker.canonical_id)

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params_obj = call_args[0][0]  # The first argument is the query params object
        assert str(query_params_obj.user_id) == str(mock_user.id)  # user_id

        # Verify the query_params object (BiomarkerQueryParams)
        assert query_params_obj.page == 1
        assert query_params_obj.page_size == 20
        assert query_params_obj.sort_by == "created_at"
        assert query_params_obj.is_descending == False
        assert query_params_obj.record_id is None
        assert query_params_obj.test_name is None
        assert query_params_obj.result is None
        assert query_params_obj.unit is None
        assert query_params_obj.sample_source is None
        assert query_params_obj.method is None
        assert query_params_obj.test_date_from is None
        assert query_params_obj.test_date_to is None

    @pytest.mark.asyncio
    async def test_get_user_biomarkers_with_filters_and_sorting(self, auth_client, mock_user, mock_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting biomarkers with filtering and sorting parameters."""
        # Mock paginated response
        from schemas import PaginatedBiomarkerResponse
        mock_biomarker.canonical_record = {"canonical_name": "Canonical Name"}
        mock_paginated_response = PaginatedBiomarkerResponse(
            status="success",
            user_id=str(mock_user.id),
            total_count=1,
            page=2,
            page_size=10,
            total_pages=1,
            data=[mock_biomarker]
        )
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_biomarkers_paginated", mock_get)

        # Query parameters specific to biomarkers
        query_params = {
            "user_id": str(mock_user.id),
            "page": "2",
            "page_size": "10",
            "sort_by": "test_name",
            "is_descending": "false",
            "record_id": "record123",
            "test_name": "Cholesterol",
            "result": "Normal",
            "unit": "mg/dL",
            "sample_source": "Blood",
            "method": "Lab Test",
        }

        query_string = "&".join([f"{k}={v}" for k, v in query_params.items()])
        response = auth_client.get(f"/biomarkers/paginated?{query_string}")

        # Verify response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["page"] == 2
        assert data["page_size"] == 10
        assert data["total_count"] == 1
        assert data["total_pages"] == 1
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1
        assert data["data"][0]["canonical_record"]["canonical_name"] == "Canonical Name"
        assert data["data"][0]["canonical_id"] == str(mock_biomarker.canonical_id)

        # Verify the service method was called with correct arguments
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        query_params_obj = call_args[0][0]  # The first argument is the query params object
        assert str(query_params_obj.user_id) == str(mock_user.id)  # user_id

        # Verify the query_params object (BiomarkerQueryParams)
        assert query_params_obj.page == 2
        assert query_params_obj.page_size == 10
        assert query_params_obj.sort_by == "test_name"
        assert query_params_obj.is_descending == False
        assert query_params_obj.record_id == "record123"
        assert query_params_obj.test_name == "Cholesterol"
        assert query_params_obj.result == "Normal"
        assert query_params_obj.unit == "mg/dL"
        assert query_params_obj.sample_source == "Blood"
        assert query_params_obj.method == "Lab Test"

    @pytest.mark.asyncio
    async def test_get_user_biomarkers_paginated_empty_results(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting biomarkers with pagination when no results found."""
        # Mock empty paginated response
        from schemas import PaginatedBiomarkerResponse
        mock_paginated_response = PaginatedBiomarkerResponse(
            status="success",
            user_id=str(mock_user.id),
            total_count=0,
            page=1,
            page_size=20,
            total_pages=0,
            data=[]
        )
        mock_get = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_biomarkers_paginated", mock_get)

        # Make the request
        response = auth_client.get(f"/biomarkers/paginated?user_id={mock_user.id}")

        # Verify the response
        assert response.status_code == status.HTTP_200_OK, response.text
        data = response.json()
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert data["total_pages"] == 0
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 0

        # Verify the service method was called
        mock_get.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_biomarkers_paginated_validation_error(self, auth_client, mock_user):
        """Test getting biomarkers with invalid pagination parameters."""
        # Make request with invalid parameters
        response = auth_client.get(f"/biomarkers/paginated?user_id={mock_user.id}&page=0&page_size=0")

        # Verify validation error response
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, response.text
        data = response.json()
        assert "detail" in data

        # Check that validation errors mention the invalid fields
        error_fields = [error["loc"][-1] for error in data["detail"]]
        assert "page" in error_fields or "page_size" in error_fields
