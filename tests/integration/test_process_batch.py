import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import AsyncMock
from fastapi import status

@pytest.mark.integration
class TestProcessBatchEndpoint:

    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        from models import UserORM
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @pytest.fixture
    def mock_batch_id(self):
        """Create a mock batch ID."""
        return f"batch-{str(uuid.uuid4())[:8]}"

    @pytest.mark.asyncio
    async def test_process_batch(self, auth_client, mock_user, mock_batch_id, mock_n1_process_pipeline, monkeypatch):
        """Test processing a batch of records."""
        # Mock the update_batch_user_records method
        now = datetime.now(timezone.utc)
        mock_response = {
            "batch_id": mock_batch_id,
            "status": "queued",
            "updated_at": now,
            "record_count": 3
        }
        mock_update_batch = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_batch_user_records", mock_update_batch)

        # Define test parameters
        parser_type = "Sequential"
        parser_cloud = "Google"

        # Make the request
        response = auth_client.post(
            f"/records/process-batch?user_id={mock_user.id}&batch_id={mock_batch_id}&parser_type={parser_type}&parser_cloud={parser_cloud}"
        )

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["batch_id"] == mock_batch_id
        assert data["status"] == "queued"
        assert "updated_at" in data
        assert data["record_count"] == 3

        # Verify the mocked method was called with correct arguments
        mock_update_batch.assert_called_once()
        args, _ = mock_update_batch.call_args
        assert args[0] == mock_user.id  # First arg should be user_id
        assert args[1] == mock_batch_id  # Second arg should be batch_id
        assert args[2] == parser_type  # Third arg should be parser_type
        assert args[3] == parser_cloud  # Fourth arg should be parser_cloud

    @pytest.mark.asyncio
    async def test_process_batch_error(self, auth_client, mock_user, mock_batch_id, mock_n1_process_pipeline, monkeypatch):
        """Test processing a batch of records with an error."""
        # Mock the update_batch_user_records method to raise an exception
        mock_update_batch = AsyncMock(side_effect=Exception("Test error"))
        monkeypatch.setattr(mock_n1_process_pipeline, "update_batch_user_records", mock_update_batch)

        # Make the request
        response = auth_client.post(
            f"/records/process-batch?user_id={mock_user.id}&batch_id={mock_batch_id}"
        )

        # Verify the response
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        data = response.json()
        assert "detail" in data
        assert "Error processing batch" in data["detail"]
