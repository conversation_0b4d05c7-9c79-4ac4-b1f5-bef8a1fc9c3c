import os
import sys
import uuid
import pytest

# Configure pytest-asyncio to use function scope for event loop
# This fixes the PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET) warning
import pytest

# Create a pytest.ini configuration in code
def pytest_configure(config):
    config.inicfg["asyncio_mode"] = "auto"
    # Set the default fixture loop scope to function
    config.inicfg["asyncio_default_fixture_loop_scope"] = "function"
from contextlib import contextmanager # Import contextmanager
from unittest.mock import MagicMock, patch
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Set environment variables for testing
DATABASE_URL = "sqlite:///:memory:"  # Use in-memory SQLite for testing
os.environ["DATABASE_URL"] = DATABASE_URL
# Set GCP_SERVICE_JSON to a mock value instead of empty string
os.environ["GCP_SERVICE_JSON"] = '{"client_email": "<EMAIL>", "private_key": "mock-key"}'

# Import and patch google.cloud.storage before other imports
from unittest.mock import MagicMock, patch, AsyncMock
import google.cloud.storage
# Mock the entire storage Client to avoid credential issues
mock_client = MagicMock()
google.cloud.storage.Client = MagicMock(return_value=mock_client)
# Mock the from_service_account_info method specifically
google.cloud.storage.Client.from_service_account_info = MagicMock(return_value=mock_client)

# Patch N1ProcessPipeline.get_loinc_short_name_map before it's imported
import sys
from unittest.mock import patch

# Define a mock function to replace get_loinc_short_name_map
def mock_get_loinc_short_name_map(self):
    # Create a dictionary of LOINC records for testing
    from schemas import LoincRecord
    return {
        "100000-9": LoincRecord(
            loinc_num="100000-9",
            component="Health informatics pioneer and the father of LOINC",
            property="Hx",
            time_aspct="Pt",
            system="^Patient",
            scale_typ="Nar",
            method_typ="",
            class_="H&P.HX",
            classtype=2,
            long_common_name="Health informatics pioneer and the father of LOINC",
            shortname="Health Info Pioneer+Father of LOINC",
            external_copyright_notice="",
            status="ACTIVE",
            version_first_released="2.74",
            version_last_changed="2.74"
        ),
        "10000-8": LoincRecord(
            loinc_num="10000-8",
            component="R wave duration.lead AVR",
            property="Time",
            time_aspct="Pt",
            system="Heart",
            scale_typ="Qn",
            method_typ="EKG",
            class_="EKG.MEAS",
            classtype=2,
            long_common_name="R wave duration in lead AVR",
            shortname="R wave dur L-AVR",
            external_copyright_notice="",
            status="ACTIVE",
            version_first_released="1.0i",
            version_last_changed="2.48"
        )
    }

# Apply the patch to the module before it's imported
patch('services.N1ProcessPipeline.get_loinc_short_name_map', mock_get_loinc_short_name_map).start()

# Import and patch FileStorageService directly to avoid initialization issues
from storage import FileStorageService
# Create a patched version of the __init__ method to avoid credential issues
original_init = FileStorageService.__init__
def patched_init(self):
    self.storage_client = mock_client
    self.bucket_prefix = "n1u"
    self.bucket_name_len = 12
    self.logger = MagicMock()
# Apply the patch
FileStorageService.__init__ = patched_init

# Add a fixture to restore the original __init__ method after tests
@pytest.fixture(scope="session", autouse=True)
def restore_file_storage_service():
    """Restore the original FileStorageService.__init__ method after all tests."""
    yield
    # Restore the original method after all tests are done
    FileStorageService.__init__ = original_init

# Import app and database components after setting environment variables
from main import app
# Import SessionLocal, Base, get_db, get_db_session
from database import SessionLocal, Base, get_db, get_db_session
# Import get_pipeline dependency
from dependencies import get_pipeline
from models import * # Import all models to ensure they are registered with Base

# Create a fixture for the SQLAlchemy engine
@pytest.fixture(scope="session")
def engine():
    return create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False}, # Required for SQLite in-memory
        poolclass=StaticPool, # Recommended for SQLite testing
    )

# Import alembic for migrations
from alembic.config import Config
from alembic import command

# Create a fixture for the database session
@pytest.fixture(scope="function")
def db_session(engine):
    """Create a new database session for a test."""
    # Create the tables
    Base.metadata.create_all(bind=engine)

    # Run Alembic migrations to ensure all tables are created properly
    # alembic_cfg = Config("alembic.ini")
    # alembic_cfg.set_main_option("sqlalchemy.url", str(engine.url))
    # command.upgrade(alembic_cfg, "head")

    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Patch database.SessionLocal to use our TestingSessionLocal for this test function
    with patch("database.SessionLocal", new=TestingSessionLocal):
        session = TestingSessionLocal()
        try:
            yield session # Provide the session to the test
        finally:
            session.close()
            # Drop the tables after the test
            Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session, mock_n1_process_pipeline): # Add pipeline mock dependency
    """Create a FastAPI TestClient with dependency overrides."""
    # Override the get_pipeline dependency
    app.dependency_overrides[get_pipeline] = lambda: mock_n1_process_pipeline

    with TestClient(app) as c:
        yield c

    # Clean up the override after the test
    del app.dependency_overrides[get_pipeline]

@pytest.fixture(scope="function")
def mock_storage_service():
    """Mock the FileStorageService to avoid actual cloud operations."""
    # First, set up the mock for the storage client
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_bucket.blob.return_value = mock_blob
    mock_blob.generate_signed_url.return_value = "https://mock.storage.url/mock-file?signed=true"

    # Mock the storage client's lookup_bucket and create_bucket methods
    mock_storage_client = MagicMock()
    mock_storage_client.lookup_bucket.return_value = None  # Simulate bucket doesn't exist
    mock_storage_client.create_bucket.return_value = mock_bucket
    mock_storage_client.bucket.return_value = mock_bucket

    # Now patch the FileStorageService
    with patch("storage.FileStorageService") as mock_service:
        # Create a mock instance without referencing the actual class
        mock_instance = MagicMock()

        # Set the storage_client attribute
        mock_instance.storage_client = mock_storage_client

        # Configure common mock methods
        mock_instance.upload_file_to_gcs.return_value = "https://storage.googleapis.com/mock-bucket/mock-file.pdf"
        mock_instance.get_signed_url.return_value = "https://mock.storage.url/mock-file?signed=true"
        mock_instance.create_new_bucket.return_value = "mock-bucket-name"
        mock_instance.delete_user_files.return_value = 5  # Mock deleting 5 files
        mock_instance.delete_record_file.return_value = True
        mock_instance.delete_report_file.return_value = True
        mock_instance.delete_file.return_value = True

        # Add async methods that might be used
        mock_instance.generate_report = AsyncMock(return_value="https://mock.storage.url/mock-report.pdf")
        mock_instance.create_pdf = AsyncMock(return_value="mock-report.pdf")
        mock_instance.post_to_bubble = AsyncMock(return_value=None)

        # Configure the mock class to return our mock instance
        mock_service.return_value = mock_instance

        yield mock_instance

# Import the real N1ProcessPipeline and AsyncMock
from services import N1ProcessPipeline
from unittest.mock import AsyncMock

@pytest.fixture(scope="function")
def mock_connection_manager():
    """Create a mock ConnectionManager for testing."""
    from ws_service import ConnectionManager
    mock_manager = MagicMock(spec=ConnectionManager)
    mock_manager.broadcast_update = AsyncMock()
    return mock_manager

@pytest.fixture(scope="function")
def mock_n1_process_pipeline(mock_storage_service, db_session, mock_connection_manager): # Add mock_connection_manager dependency
    """
    Create a REAL N1ProcessPipeline instance but mock methods
    that perform external calls or background tasks.
    """
    # First, patch the get_loinc_short_name_map method to use our mock function
    with patch.object(N1ProcessPipeline, 'get_loinc_short_name_map', mock_get_loinc_short_name_map):
        # Then patch the FileStorageService class before instantiating N1ProcessPipeline
        with patch("services.FileStorageService", return_value=mock_storage_service):
            # Instantiate the real pipeline with the mock connection manager
            # It gets the db session internally via get_db_session, which is patched by db_session fixture
            real_pipeline = N1ProcessPipeline(mock_connection_manager)

    # Mock methods that interact with external systems or background tasks
    real_pipeline.update_bubble_record_status = AsyncMock(return_value={"status": "success", "response": "mocked"})
    real_pipeline.update_bubble_report_status = AsyncMock(return_value={"status": "success", "response": "mocked"})
    real_pipeline.sync_biomarkers_to_bubble = AsyncMock(return_value={"status": "success", "message": "mocked sync"})

    # Mock methods that launch cloud jobs directly if called by routes
    # Assuming launch_report_generation_job is a standalone function imported in services.py
    # If it's called by the pipeline instance, mock it on the instance:
    # real_pipeline.launch_report_generation_job = AsyncMock(return_value="mock_operation_id")
    # If it's a separate function, patch it globally if needed:
    # patch('services.launch_report_generation_job', new_callable=AsyncMock).start() # Remember to stop patch later if needed

    # Mock generate_new_report to prevent actual job launch if not already covered
    # We might need to mock specific methods called *within* the route handlers if the route handler itself isn't mocked
    # For now, let's assume mocking the external calls is sufficient.

    # The real methods like get_record_status, get_user_details, etc., will run,
    # using the patched test database session provided by the db_session fixture.

    return real_pipeline

@pytest.fixture(scope="function")
def mock_user():
    """Create a mock user."""
    return MagicMock(
        id=uuid.uuid4(),
        bubble_id="12345678",
        email="<EMAIL>",
        develop_mode=False,
        bucket_name="test-bucket"
    )

@pytest.fixture(scope="function")
def mock_record(mock_user):
    """Create a mock record."""
    return MagicMock(
        id=str(uuid.uuid4()),
        user_id=mock_user.id,
        url="https://example.com/test.pdf",
        file_name="test.pdf",
        type="RECORD",
        status="COMPLETED",
        progress=100,
        batch_id="batch123"
    )

@pytest.fixture(scope="function")
def mock_report(mock_user):
    """Create a mock report."""
    return MagicMock(
        id=str(uuid.uuid4()),
        user_id=mock_user.id,
        progress=100,
        status="COMPLETED",
        url="https://example.com/test-report.pdf",
        file_name="Test Report",
        config={
            "report_name": "Test Report",
            "report_format": "pdf",
            "report_style": "casual",
            "model_name": "o3-mini",
            "report_language": "english",
            "temperature": 1.0,
            "custom_prompt": "",
            "report_flow": "Template"
        }
    )

@pytest.fixture(scope="function")
def api_key_header():
    """Return a header with a valid API key for testing."""
    from config import N1_API_HEADER_NAME, N1_API_KEY # Keep for potential API key tests
    return {N1_API_HEADER_NAME: N1_API_KEY}

@pytest.fixture(scope="function")
def auth_client(client, api_key_header): # Added api_key_header dependency
    """Return a client with Basic authentication headers and API key."""
    from config import USERNAME, PASSWORD
    import base64

    credentials = f"{USERNAME}:{PASSWORD}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    auth_header = {"Authorization": f"Basic {encoded_credentials}"}

    # Add both Basic auth and API key headers
    client.headers.update(auth_header)
    client.headers.update(api_key_header)
    return client