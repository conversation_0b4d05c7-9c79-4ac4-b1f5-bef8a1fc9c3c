#!/usr/bin/env python3
"""
Test runner script for the N1 API.

This script provides a convenient way to run tests with different configurations.
It uses pytest under the hood and provides options for running specific test types,
generating coverage reports, and more.

Usage:
    python tests/run_tests.py [options]

Options:
    --unit           Run only unit tests
    --integration    Run only integration tests
    --all            Run all tests (default)
    --coverage       Generate coverage report
    --verbose        Run with verbose output
    --file FILE      Run tests from a specific file
    --help           Show this help message
"""

import sys
import os
import subprocess
import argparse

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run tests for the N1 API")
    
    # Test type options
    test_type = parser.add_mutually_exclusive_group()
    test_type.add_argument("--unit", action="store_true", help="Run only unit tests")
    test_type.add_argument("--integration", action="store_true", help="Run only integration tests")
    test_type.add_argument("--all", action="store_true", help="Run all tests (default)")
    
    # Other options
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--verbose", action="store_true", help="Run with verbose output")
    parser.add_argument("--file", type=str, help="Run tests from a specific file")
    
    args = parser.parse_args()
    
    # Default to running all tests if no test type is specified
    if not (args.unit or args.integration or args.all):
        args.all = True
    
    return args

def run_tests(args):
    """Run the tests with the specified options."""
    # Set environment variables for testing
    os.environ["DATABASE_URL"] = "sqlite:///:memory:"
    
    # Base command
    cmd = ["pytest", "-xvs"]  # -x: exit on first failure, -v: verbose, -s: show output
    
    # Add test type
    if args.unit:
        cmd.append("-m")
        cmd.append("unit")
    elif args.integration:
        cmd.append("-m")
        cmd.append("integration")
    
    # Add specific file if provided
    if args.file:
        cmd.append(args.file)
    
    # Add coverage if requested
    if args.coverage:
        # Explicitly specify each module to cover
        cmd.append("--cov=main")
        cmd.append("--cov=routes")
        cmd.append("--cov=database")
        cmd.append("--cov=dependencies")
        cmd.append("--cov=services")
        cmd.append("--cov=config")
        cmd.append("--cov=tests/integration/test_main.py")
        cmd.append("--cov-report=term")
        cmd.append("--cov-report=html")
    
    # Add verbose if requested
    if args.verbose:
        cmd.append("-v")
    
    # Run the command
    print(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    
    return result.returncode

def main():
    """Main entry point."""
    args = parse_args()
    
    # Ensure we're running from the project root
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    os.chdir(project_root)
    
    # Run the tests
    return run_tests(args)

if __name__ == "__main__":
    sys.exit(main())
