import pytest
from unittest.mock import patch, MagicMock, AsyncMock

def test_storage_service_mock(mock_storage_service):
    """Test that the storage service mock is properly configured."""
    # Verify that the mock has all the expected methods
    assert hasattr(mock_storage_service, 'upload_file_to_gcs')
    assert hasattr(mock_storage_service, 'get_signed_url')
    assert hasattr(mock_storage_service, 'create_new_bucket')
    assert hasattr(mock_storage_service, 'delete_user_files')
    assert hasattr(mock_storage_service, 'delete_record_file')
    assert hasattr(mock_storage_service, 'delete_report_file')
    assert hasattr(mock_storage_service, 'delete_file')
    assert hasattr(mock_storage_service, 'generate_report')
    assert hasattr(mock_storage_service, 'create_pdf')
    assert hasattr(mock_storage_service, 'post_to_bubble')
    
    # Verify that the methods return the expected values
    assert mock_storage_service.upload_file_to_gcs() == "https://storage.googleapis.com/mock-bucket/mock-file.pdf"
    assert mock_storage_service.get_signed_url() == "https://mock.storage.url/mock-file?signed=true"
    assert mock_storage_service.create_new_bucket() == "mock-bucket-name"
    assert mock_storage_service.delete_user_files() == 5
    assert mock_storage_service.delete_record_file() is True
    assert mock_storage_service.delete_report_file() is True
    assert mock_storage_service.delete_file() is True
    
    # Verify that we're not using the actual FileStorageService class
    with patch("storage.FileStorageService") as mock_class:
        # This should not raise an error if we're not importing the actual class
        pass

@pytest.mark.asyncio
async def test_storage_service_async_methods(mock_storage_service):
    """Test that the async methods of the storage service mock are properly configured."""
    # Verify that the async methods return the expected values
    assert await mock_storage_service.generate_report() == "https://mock.storage.url/mock-report.pdf"
    assert await mock_storage_service.create_pdf() == "mock-report.pdf"
    assert await mock_storage_service.post_to_bubble() is None
