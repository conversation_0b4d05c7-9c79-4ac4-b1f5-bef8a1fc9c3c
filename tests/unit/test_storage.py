"""
Unit tests for the storage module.

This module contains tests for the FileStorageService class.
"""

import pytest
import uuid
from unittest.mock import patch, MagicMock, AsyncMock  # Import AsyncMock
from storage import FileStorageService
from schemas import N1ReportGenRequest

@pytest.mark.unit
class TestFileStorageService:
    """Tests for the FileStorageService class."""

    def test_init(self):
        """Test the initialization of the FileStorageService."""
        # Since we've globally patched the Client in conftest.py, we just need to verify
        # that the service initializes correctly with our mock
        service = FileStorageService()
        assert service is not None
        assert service.storage_client is not None # Check if the client was created
        # We don't need to verify the client was called since we're using a global mock

    def test_create_new_bucket(self, mock_storage_service):
        """Test creating a new bucket."""
        # Configure the mock
        mock_storage_service.create_new_bucket.return_value = "test-bucket-123"
        
        # Call the method
        bucket_name = mock_storage_service.create_new_bucket()
        
        # Assert the result
        assert bucket_name == "test-bucket-123"
        mock_storage_service.create_new_bucket.assert_called_once()

    def test_generate_report(self, mock_storage_service):
        """Test generating a report."""
        # Configure the mock
        mock_storage_service.generate_report = MagicMock()
        
        # Create a request with a valid UUID
        request = N1ReportGenRequest(
            id="test-id",
            user_id=uuid.uuid4()
        )
        
        # Call the method - we're not awaiting it since we're mocking it
        mock_storage_service.generate_report(request)
        
        # Assert the mock was called
        mock_storage_service.generate_report.assert_called_once()
    
    def test_get_report(self, mock_storage_service):
        """Test getting a report."""
        # Configure the mock's return value directly
        expected_url = "https://storage.googleapis.com/test-bucket/test-report.pdf"
        mock_storage_service.get_report.return_value = expected_url

        # Call the method
        url = mock_storage_service.get_report("test-user", "test-report")

        # Assert the result
        assert url == expected_url
        # Verify the mock method was called (no need to check internal calls in unit test)
        mock_storage_service.get_report.assert_called_once_with("test-user", "test-report")

    @pytest.mark.asyncio
    async def test_create_pdf(self, mock_storage_service):
        """Test creating a PDF."""
        # Configure the mock's return value directly
        expected_path = "test-id.pdf"
        # Use AsyncMock for the method itself if the test needs to await it
        mock_storage_service.create_pdf = AsyncMock(return_value=expected_path)

        # Call the method
        report_path = await mock_storage_service.create_pdf("test-id", "# Test Document")

        # Assert the result
        assert report_path == expected_path
        # Verify the mock method was called
        mock_storage_service.create_pdf.assert_called_once_with("test-id", "# Test Document")

    def test_get_signed_url(self, mock_storage_service):
        """Test getting a signed URL."""
        # Configure the mock
        expected_url = "https://storage.googleapis.com/test-bucket/test-file.pdf?signed=true"
        mock_storage_service.get_signed_url.return_value = expected_url

        # Call the method
        url = mock_storage_service.get_signed_url("test-user", "test-id", "record", "test-file.pdf", "test-bucket")

        # Assert the result
        assert url == expected_url
        mock_storage_service.get_signed_url.assert_called_once_with("test-user", "test-id", "record", "test-file.pdf", "test-bucket")

    def test_upload_to_gcs(self, mock_storage_service):
        """Test uploading to GCS."""
        # Configure the mock
        mock_storage_service.upload_to_gcs.return_value = "https://storage.googleapis.com/test-bucket/test-file.pdf?signed=true"
        
        # Call the method
        url = mock_storage_service.upload_to_gcs("test-user", "test-id", "test-file.pdf", "test-bucket")
        
        # Assert the result
        assert url == "https://storage.googleapis.com/test-bucket/test-file.pdf?signed=true"
        mock_storage_service.upload_to_gcs.assert_called_once()

    @pytest.mark.asyncio
    async def test_post_to_bubble(self, mock_storage_service):
        """Test posting to Bubble."""
        # Configure the mock as AsyncMock
        mock_storage_service.post_to_bubble = AsyncMock()

        # Call the method
        await mock_storage_service.post_to_bubble("test-user", "test-id", "https://example.com/test.pdf")
        
        # Assert the mock was called
        mock_storage_service.post_to_bubble.assert_called_once_with("test-user", "test-id", "https://example.com/test.pdf")
    
    def test_upload_file_to_gcs(self, mock_storage_service):
        """Test uploading a file to GCS."""
        # Configure the mock
        mock_storage_service.upload_file_to_gcs.return_value = "https://storage.googleapis.com/test-bucket/test-file.pdf?signed=true"
        
        # Create a mock file object
        mock_file = MagicMock()
        
        # Call the method
        url = mock_storage_service.upload_file_to_gcs(mock_file, "test-file.pdf", "application/pdf", "test-user", "record", "test-id", "test-bucket")
        
        # Assert the result
        assert url == "https://storage.googleapis.com/test-bucket/test-file.pdf?signed=true"
        mock_storage_service.upload_file_to_gcs.assert_called_once()

    def test_delete_file(self, mock_storage_service):
        """Test deleting a file."""
        # Configure the mock
        mock_storage_service.delete_file.return_value = True
        
        # Call the method
        result = mock_storage_service.delete_file("test-user", "record", "test-id", "test-file.pdf", "test-bucket")
        
        # Assert the result
        assert result is True
        mock_storage_service.delete_file.assert_called_once()

    def test_delete_record_file(self, mock_storage_service):
        """Test deleting a record file."""
        # Configure the mock
        mock_storage_service.delete_record_file.return_value = True
        
        # Call the method
        result = mock_storage_service.delete_record_file("test-user", "test-id", "test-file.pdf", "test-bucket")
        
        # Assert the result
        assert result is True
        mock_storage_service.delete_record_file.assert_called_once()

    def test_delete_report_file(self, mock_storage_service):
        """Test deleting a report file."""
        # Configure the mock
        mock_storage_service.delete_report_file.return_value = True
        
        # Call the method
        result = mock_storage_service.delete_report_file("test-user", "test-id", "test-file.pdf", "test-bucket")
        
        # Assert the result
        assert result is True
        mock_storage_service.delete_report_file.assert_called_once()

    def test_delete_user_files(self, mock_storage_service):
        """Test deleting user files."""
        # Configure the mock
        mock_storage_service.delete_user_files.return_value = 5
        
        # Call the method
        result = mock_storage_service.delete_user_files("test-user", "test-bucket")
        
        # Assert the result
        assert result == 5
        mock_storage_service.delete_user_files.assert_called_once()

    def test_get_random_bucket_name(self, mock_storage_service):
        """Test getting a random bucket name."""
        # Configure the mock
        mock_storage_service.get_random_bucket_name.return_value = "abcdef123456"
        
        # Call the method
        bucket_name = mock_storage_service.get_random_bucket_name()
        
        # Assert the result
        assert bucket_name == "abcdef123456"
        mock_storage_service.get_random_bucket_name.assert_called_once()
