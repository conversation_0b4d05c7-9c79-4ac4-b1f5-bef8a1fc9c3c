import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>

from services import N1ProcessPipeline
from models import UserORM, UserRecordRequestORM, UserReportGenRequestORM
from schemas import UserRegistration, RecordProgressUpdateRequest, ReportProgressUpdateRequest

@pytest.mark.unit
class TestN1ProcessPipeline:

    @pytest.fixture
    def mock_connection_manager(self):
        """Create a mock ConnectionManager for testing."""
        from ws_service import ConnectionManager
        from unittest.mock import AsyncMock
        mock_manager = MagicMock(spec=ConnectionManager)
        mock_manager.broadcast_update = AsyncMock()
        return mock_manager

    @pytest.fixture
    def pipeline(self, mock_storage_service, db_session, mock_connection_manager): # Add mock_connection_manager dependency
        """Create an N1ProcessPipeline instance with mocked storage service and patched db session."""
        # Patch storage service and the db session getter used internally by the service
        with patch("services.FileStorageService", return_value=mock_storage_service), \
             patch("services.get_db_session", return_value=db_session): # Patch get_db_session
            pipeline = N1ProcessPipeline(mock_connection_manager)
            # The pipeline instance will now use the patched get_db_session internally
            pipeline.storage_service = mock_storage_service # Keep existing mock
            yield pipeline

    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @pytest.fixture
    def mock_record(self, db_session, mock_user):
        """Create a mock record in the database."""
        record_id = str(uuid.uuid4())
        record = UserRecordRequestORM(
            id=record_id,
            user_id=mock_user.id,
            file_name="test.pdf",
            type="RECORD",
            status="COMPLETED",
            progress=100,
            batch_id="batch123"
        )
        db_session.add(record)
        db_session.commit()
        db_session.refresh(record)
        return record

    @pytest.fixture
    def mock_report(self, db_session, mock_user):
        """Create a mock report in the database."""
        report_id = str(uuid.uuid4())
        config = {
            "report_name": "Test Report",
            "report_format": "pdf",
            "report_style": "casual",
            "model_name": "o3-mini",
            "report_language": "english",
            "temperature": 1.0,
            "custom_prompt": "",
            "report_flow": "Template"
        }

        report = UserReportGenRequestORM(
            id=report_id,
            user_id=mock_user.id,
            progress=100,
            status="COMPLETED",
            url="https://example.com/test-report.pdf",
            file_name="Test Report",
            config=config,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        db_session.add(report)
        db_session.commit()
        db_session.refresh(report)
        return report

    def test_get_user_details(self, pipeline, mock_user):
        """Test getting user details."""
        # Call the method
        user = pipeline.get_user_details(str(mock_user.id))

        # Verify the result
        assert user.id == mock_user.id
        assert user.bubble_id == mock_user.bubble_id
        assert user.email == mock_user.email
        assert user.bucket_name == mock_user.bucket_name

    def test_get_user_details_not_found(self, pipeline):
        """Test getting user details for a non-existent user."""
        non_existent_id = str(uuid.uuid4())

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            pipeline.get_user_details(non_existent_id)

        assert excinfo.value.status_code == 404
        assert "User not found" in excinfo.value.detail

    def test_get_record_status(self, pipeline, mock_user, mock_record):
        """Test getting record status."""
        # Call the method
        record = pipeline.get_record_status(str(mock_user.id), mock_record.id)

        # Verify the result
        assert record.id == mock_record.id
        assert record.user_id == mock_user.id
        assert record.status == mock_record.status
        assert record.progress == mock_record.progress

    def test_get_record_status_not_found(self, pipeline, mock_user):
        """Test getting record status for a non-existent record."""
        non_existent_id = str(uuid.uuid4())

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            pipeline.get_record_status(str(mock_user.id), non_existent_id)

        assert excinfo.value.status_code == 404
        assert "Record not found" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_get_user_records(self, pipeline, mock_user, mock_record):
        """Test getting all records for a user."""
        # Call the method
        records = await pipeline.get_user_records(str(mock_user.id))

        # Verify the result
        assert len(records) == 1
        assert records[0].id == mock_record.id
        assert str(records[0].user_id) == str(mock_user.id)
        assert records[0].status == mock_record.status

    @pytest.mark.asyncio
    async def test_get_user_records_empty(self, pipeline, db_session):
        """Test getting records for a user with no records."""
        # Create a user with no records
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="87654321",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="empty-bucket"
        )
        db_session.add(user)
        db_session.commit()

        # Call the method
        records = await pipeline.get_user_records(str(user_id))

        # Verify the result
        assert len(records) == 0

    def test_get_report_status(self, pipeline, mock_user, mock_report):
        """Test getting report status."""
        # Call the method
        report = pipeline.get_report_status(str(mock_user.id), mock_report.id)

        # Verify the result
        assert report.id == mock_report.id
        assert str(report.user_id) == str(mock_user.id)
        assert report.status == mock_report.status
        assert report.progress == mock_report.progress

    def test_get_report_status_not_found(self, pipeline, mock_user):
        """Test getting report status for a non-existent report."""
        non_existent_id = str(uuid.uuid4())

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            pipeline.get_report_status(str(mock_user.id), non_existent_id)

        assert excinfo.value.status_code == 404
        assert "Report status not found" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_update_record_progress(self, pipeline, mock_user, mock_record):
        """Test updating record progress."""
        # Setup test parameters
        update_request = RecordProgressUpdateRequest(
            user_id=str(mock_user.id),
            record_id=mock_record.id,
            progress=50,
            status="IN_PROGRESS",
            message="Processing record",
            error=None,
            version=None
        )

        # Call the method
        updated_record = await pipeline.update_record_progress(update_request)

        # Verify the result
        assert updated_record.id == mock_record.id
        assert updated_record.progress == 50
        assert updated_record.status == "IN_PROGRESS"

    @pytest.mark.asyncio
    async def test_update_report_progress(self, pipeline, mock_user, mock_report):
        """Test updating report progress."""
        # Setup test parameters
        update_request = ReportProgressUpdateRequest(
            user_id=str(mock_user.id),
            report_id=mock_report.id,
            progress=50,
            status="IN_PROGRESS",
            message="Generating report",
            error=None,
            version=None
        )

        # Mock the update_bubble_report_status method
        with patch.object(pipeline, 'update_bubble_report_status') as mock_update:
            mock_update.return_value = {"status": "success"}

            # Call the method
            updated_report = await pipeline.update_report_progress(update_request)

            # Verify the result
            assert updated_report.id == mock_report.id
            assert updated_report.progress == 50
            assert updated_report.status == "IN_PROGRESS"

            # Verify the update_bubble_report_status method was called
            mock_update.assert_called_once()

    @pytest.mark.asyncio
    async def test_register_user(self, pipeline, db_session, mock_storage_service):
        """Test registering a new user."""
        # Setup test parameters
        user_registration = UserRegistration(
            bubble_id="87654321",
            email="<EMAIL>",
            develop_mode=False
        )

        # Mock the create_new_bucket method
        mock_storage_service.create_new_bucket.return_value = "new-test-bucket"

        # Call the method
        user = await pipeline.register_user(user_registration)

        # Verify the result
        assert user.bubble_id == user_registration.bubble_id
        assert user.email == user_registration.email
        assert user.bucket_name == "new-test-bucket"

        # Verify the user was added to the database
        user_in_db = db_session.query(UserORM).filter(UserORM.bubble_id == user_registration.bubble_id).first()
        assert user_in_db is not None
        assert user_in_db.email == user_registration.email

    @pytest.mark.asyncio
    async def test_register_user_duplicate_bubble_id(self, pipeline, mock_user):
        """Test registering a user with a duplicate bubble_id."""
        # Setup test parameters
        user_registration = UserRegistration(
            bubble_id=mock_user.bubble_id,  # Same bubble_id as mock_user
            email="<EMAIL>",
            develop_mode=False
        )

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            await pipeline.register_user(user_registration)

        assert excinfo.value.status_code == 409
        assert "bubble_id already exists" in excinfo.value.detail

    def test_delete_user_data(self, pipeline, mock_user, mock_storage_service):
        """Test deleting all user data."""
        # Mock the delete_user_files method
        mock_storage_service.delete_user_files.return_value = 5  # Mock deleting 5 files

        # Mock the __delete_user_data_from_table method to avoid SQL errors
        with patch.object(pipeline, '_N1ProcessPipeline__delete_user_data_from_table') as mock_delete:
            mock_delete.return_value = 1  # Mock that 1 row was deleted from each table

            # Call the method
            result = pipeline.delete_user_data(str(mock_user.id))

            # Verify the result
            assert result.status == "success"
            assert result.user_id == str(mock_user.id)
            assert result.database.records_deleted is True
            assert result.database.reports_deleted is True
            assert result.files_deleted == 5

            # Verify the storage service was called to delete files
            mock_storage_service.delete_user_files.assert_called_once_with(
                str(mock_user.id), mock_user.bucket_name
            )

            # Verify that __delete_user_data_from_table was called for each table
            # The method should be called for each table that has user_id column
            assert mock_delete.call_count >= 4  # At least 4 tables should be cleaned

    def test_delete_record(self, pipeline, mock_user, mock_record, mock_storage_service):
        """Test deleting a record."""
        # Mock the delete_record_file method
        mock_storage_service.delete_record_file.return_value = True

        # Call the method
        result = pipeline.delete_record(str(mock_user.id), mock_record.id)

        # Verify the result
        assert result["status"] == "success"
        assert result["record_deleted"] is True

        # Verify the storage service was called to delete the file
        mock_storage_service.delete_record_file.assert_called_once_with(
            str(mock_user.id), mock_record.id, mock_record.file_name, mock_user.bucket_name
        )

    def test_delete_record_not_found(self, pipeline, mock_user):
        """Test deleting a non-existent record."""
        non_existent_id = str(uuid.uuid4())

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            pipeline.delete_record(str(mock_user.id), non_existent_id)

        assert excinfo.value.status_code == 404
        assert "Record not found" in excinfo.value.detail

    def test_delete_report(self, pipeline, mock_user, mock_report, mock_storage_service):
        """Test deleting a report."""
        # Mock the delete_report_file method
        mock_storage_service.delete_report_file.return_value = True

        # Call the method
        result = pipeline.delete_report(str(mock_user.id), mock_report.id)

        # Verify the result
        assert result["status"] == "success"
        assert result["report_deleted"] is True

        # Verify the storage service was called to delete the file
        mock_storage_service.delete_report_file.assert_called_once_with(
            str(mock_user.id), mock_report.id, f"{mock_report.file_name}.pdf", mock_user.bucket_name
        )

    def test_delete_report_not_found(self, pipeline, mock_user):
        """Test deleting a non-existent report."""
        non_existent_id = str(uuid.uuid4())

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            pipeline.delete_report(str(mock_user.id), non_existent_id)

        assert excinfo.value.status_code == 404
        assert "Report not found" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_get_user_stats(self, pipeline, mock_user, mock_record, mock_report):
        """Test getting user statistics."""
        # Call the method
        stats = await pipeline.get_user_stats(str(mock_user.id))

        # Verify the result is a UserStats object
        from schemas import UserStats
        assert isinstance(stats, UserStats)
        assert stats.report_count == 1
        assert stats.record_count == 1
        assert stats.biomarker_count == 0  # No clinical data created in this test
        assert stats.medication_count == 0
        assert stats.procedure_count == 0
        assert stats.diagnosis_count == 0
        assert stats.genetics_count == 0

    @pytest.mark.asyncio
    async def test_reprocess_single_records(self, pipeline, mock_user, mock_record):
        """Test reprocessing a single record."""
        # Setup test parameters
        parser_type = "Sequential"
        parser_cloud = "Google"
        parser_model = "gpt-4o"
        background_tasks = MagicMock()

        # Mock the __process_batch_update method
        with patch.object(pipeline, '_N1ProcessPipeline__process_batch_update') as mock_process:
            # Call the method
            result = await pipeline.reprocess_single_records(
                str(mock_user.id),
                mock_record.id,
                parser_type,
                parser_cloud,
                background_tasks,
                parser_model
            )

            # Verify the result
            assert result["record_id"] == mock_record.id
            assert result["status"] == "queued"
            assert "updated_at" in result
            assert result["record_count"] == 1

            # Verify the __process_batch_update method was called with correct arguments
            background_tasks.add_task.assert_called_once_with(
                pipeline._N1ProcessPipeline__process_batch_update,
                str(mock_user.id),
                mock_user.bucket_name,
                parser_type,
                parser_cloud,
                [mock_record.id],
                mock_record.id,  # Using record_id as batch_id for standalone processing
                parser_model
            )

    @pytest.mark.asyncio
    async def test_reprocess_single_records_error(self, pipeline, mock_user):
        """Test reprocessing a single record with an error."""
        # Setup test parameters
        parser_type = "Sequential"
        parser_cloud = "Google"
        parser_model = "claude-3.5-sonnet"
        background_tasks = MagicMock()
        non_existent_id = str(uuid.uuid4())

        # Mock the get_user_details method to raise an exception
        with patch.object(pipeline, 'get_user_details', side_effect=HTTPException(status_code=404, detail="User not found")):
            # Verify that HTTPException is raised
            with pytest.raises(HTTPException) as excinfo:
                await pipeline.reprocess_single_records(
                    str(mock_user.id),
                    non_existent_id,
                    parser_type,
                    parser_cloud,
                    background_tasks,
                    parser_model
                )

            assert excinfo.value.status_code == 500
            assert "User not found" in excinfo.value.detail

    @pytest.fixture
    def mock_medication(self, db_session, mock_user):
        """Create a mock medication in the database."""
        from datetime import datetime, timezone
        medication_id = uuid.uuid4()
        from models import UserMedicationORM
        medication = UserMedicationORM(
            id=medication_id,
            user_id=mock_user.id,
            name="Test Medication",
            brand_name="Test Brand",
            url="https://example.com/medication",
            dosage=10.0,
            unit="mg",
            type="pill",
            started_from=datetime.now(timezone.utc),
            frequency="daily",
            reason="Test reason"
        )
        db_session.add(medication)
        db_session.commit()
        db_session.refresh(medication)
        return medication

    @pytest.mark.asyncio
    async def test_create_medication(self, pipeline, mock_user, db_session):
        """Test creating a new medication."""
        from schemas import UserMedicationCreate
        from datetime import datetime, timezone

        # Setup test parameters
        started_from = datetime.now(timezone.utc)
        medication_data = UserMedicationCreate(
            user_id=mock_user.id,
            name="New Medication",
            brand_name="New Brand",
            url="https://example.com/new-medication",
            dosage=20.0,
            unit="mg",
            type="liquid",
            started_from=started_from,
            frequency="twice daily",
            reason="New reason"
        )

        # Call the method
        result = await pipeline.create_medication(medication_data)

        # Verify the result
        assert result.name == medication_data.name
        assert result.brand_name == medication_data.brand_name
        assert result.url == medication_data.url
        assert result.dosage == medication_data.dosage
        assert result.unit == medication_data.unit
        assert result.type == medication_data.type
        assert result.frequency == medication_data.frequency
        assert result.reason == medication_data.reason
        assert result.user_id == mock_user.id

        # Verify the medication was added to the database
        from models import UserMedicationORM
        medication_in_db = db_session.query(UserMedicationORM).filter(
            UserMedicationORM.user_id == mock_user.id,
            UserMedicationORM.name == medication_data.name
        ).first()
        assert medication_in_db is not None
        assert medication_in_db.brand_name == medication_data.brand_name
        assert medication_in_db.dosage == medication_data.dosage

    @pytest.mark.asyncio
    async def test_get_user_medications(self, pipeline, mock_user, mock_medication):
        """Test getting all medications for a user."""
        # Call the method
        medications = await pipeline.get_user_medications(str(mock_user.id))

        # Verify the result
        assert len(medications) == 1
        assert medications[0].id == mock_medication.id
        assert medications[0].user_id == mock_user.id
        assert medications[0].name == mock_medication.name
        assert medications[0].brand_name == mock_medication.brand_name
        assert medications[0].dosage == mock_medication.dosage
        assert medications[0].unit == mock_medication.unit
        assert medications[0].type == mock_medication.type
        assert medications[0].frequency == mock_medication.frequency
        assert medications[0].reason == mock_medication.reason

    @pytest.mark.asyncio
    async def test_get_user_medications_empty(self, pipeline, db_session):
        """Test getting medications for a user with no medications."""
        # Create a user with no medications
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="87654321",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="empty-bucket"
        )
        db_session.add(user)
        db_session.commit()

        # Call the method
        medications = await pipeline.get_user_medications(str(user_id))

        # Verify the result
        assert len(medications) == 0

    @pytest.mark.asyncio
    async def test_get_medication(self, pipeline, mock_user, mock_medication):
        """Test getting a specific medication."""
        # Call the method
        medication = await pipeline.get_medication(str(mock_user.id), str(mock_medication.id))

        # Verify the result
        assert medication.id == mock_medication.id
        assert medication.user_id == mock_user.id
        assert medication.name == mock_medication.name
        assert medication.brand_name == mock_medication.brand_name
        assert medication.dosage == mock_medication.dosage
        assert medication.unit == mock_medication.unit
        assert medication.type == mock_medication.type
        assert medication.frequency == mock_medication.frequency
        assert medication.reason == mock_medication.reason

    @pytest.mark.asyncio
    async def test_get_medication_not_found(self, pipeline, mock_user):
        """Test getting a non-existent medication."""
        non_existent_id = str(uuid.uuid4())

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            await pipeline.get_medication(str(mock_user.id), non_existent_id)

        assert excinfo.value.status_code == 404
        assert "Medication not found" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_update_medication(self, pipeline, mock_user, mock_medication):
        """Test updating a medication."""
        from schemas import MedicationUpdate

        # Setup test parameters
        update_data = MedicationUpdate(
            name="Updated Medication",
            dosage=15.0,
            unit="ml",
            frequency="three times daily"
        )

        # Call the method
        updated_medication = await pipeline.update_medication(
            str(mock_user.id),
            str(mock_medication.id),
            update_data
        )

        # Verify the result
        assert updated_medication.id == mock_medication.id
        assert updated_medication.user_id == mock_user.id
        assert updated_medication.name == update_data.name
        assert updated_medication.dosage == update_data.dosage
        assert updated_medication.unit == update_data.unit
        assert updated_medication.frequency == update_data.frequency
        assert updated_medication.brand_name == mock_medication.brand_name  # Unchanged field
        assert updated_medication.type == mock_medication.type  # Unchanged field
        assert updated_medication.reason == mock_medication.reason  # Unchanged field

    @pytest.mark.asyncio
    async def test_update_medication_not_found(self, pipeline, mock_user):
        """Test updating a non-existent medication."""
        from schemas import MedicationUpdate
        non_existent_id = str(uuid.uuid4())
        update_data = MedicationUpdate(name="Updated Medication")

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            await pipeline.update_medication(str(mock_user.id), non_existent_id, update_data)

        assert excinfo.value.status_code == 404
        assert "Medication not found" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_delete_medication(self, pipeline, mock_user, mock_medication):
        """Test deleting a medication."""
        # Call the method
        result = await pipeline.delete_medication(str(mock_user.id), str(mock_medication.id))

        # Verify the result
        assert result["status"] == "success"
        assert "deleted successfully" in result["message"]

        # Verify the medication was deleted from the database
        from models import UserMedicationORM
        with pytest.raises(HTTPException) as excinfo:
            await pipeline.get_medication(str(mock_user.id), str(mock_medication.id))

        assert excinfo.value.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_medication_not_found(self, pipeline, mock_user):
        """Test deleting a non-existent medication."""
        non_existent_id = str(uuid.uuid4())

        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as excinfo:
            await pipeline.delete_medication(str(mock_user.id), non_existent_id)

        assert excinfo.value.status_code == 404
        assert "Medication not found" in excinfo.value.detail
