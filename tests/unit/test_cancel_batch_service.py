import pytest
import uuid
from unittest.mock import patch

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services import N1ProcessPipeline
from models import UserORM, UserRecordRequestORM

@pytest.mark.unit
class TestCancelBatchService:
    
    @pytest.fixture
    def mock_connection_manager(self):
        """Create a mock ConnectionManager for testing."""
        from ws_service import ConnectionManager
        from unittest.mock import AsyncMock, MagicMock
        mock_manager = MagicMock(spec=ConnectionManager)
        mock_manager.broadcast_update = AsyncMock()
        return mock_manager

    @pytest.fixture
    def pipeline(self, mock_storage_service, db_session, mock_connection_manager):
        """Create an N1ProcessPipeline instance with mocked storage service and patched db session."""
        with patch("services.FileStorageService", return_value=mock_storage_service), \
             patch("services.get_db_session", return_value=db_session):
            pipeline = N1ProcessPipeline(mock_connection_manager)
            pipeline.storage_service = mock_storage_service
            yield pipeline
    
    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user
    
    @pytest.fixture
    def mock_batch_records(self, db_session, mock_user):
        """Create mock records in the database for a batch."""
        batch_id = f"batch-{str(uuid.uuid4())[:8]}"
        records = []
        
        # Create 3 records for the batch
        for i in range(3):
            record_id = str(uuid.uuid4())
            record = UserRecordRequestORM(
                id=record_id,
                user_id=str(mock_user.id),
                file_name=f"test{i}.pdf",
                type="RECORD",
                status="PENDING",
                progress=0,
                batch_id=batch_id
            )
            db_session.add(record)
            records.append(record)
        
        db_session.commit()
        for record in records:
            db_session.refresh(record)
        
        return {"batch_id": batch_id, "records": records}
    
    @pytest.mark.asyncio
    async def test_cancel_batch(self, pipeline, mock_user, mock_batch_records):
        """Test cancelling a batch of records."""
        # Mock the delete_record method to return success
        with patch.object(pipeline, 'delete_record') as mock_delete:
            mock_delete.return_value = {
                "status": "success",
                "message": "Record deleted successfully",
                "record_deleted": True,
                "clinical_data_deleted_count": 0,
                "file_deleted": True
            }
            
            # Call the method
            result = await pipeline.cancel_batch(
                str(mock_user.id),
                mock_batch_records["batch_id"]
            )
            
            # Verify the result
            assert result["status"] == "success"
            assert result["message"] == f"Batch {mock_batch_records['batch_id']} cancelled successfully"
            assert result["total_records"] == 3
            assert result["records_deleted"] == 3
            assert len(result["failed_records"]) == 0
            
            # Verify delete_record was called for each record
            assert mock_delete.call_count == 3
            for record in mock_batch_records["records"]:
                mock_delete.assert_any_call(str(mock_user.id), record.id)
    
    @pytest.mark.asyncio
    async def test_cancel_batch_no_records(self, pipeline, mock_user):
        """Test cancelling a batch with no records."""
        # Setup test parameters
        batch_id = f"nonexistent-batch-{str(uuid.uuid4())[:8]}"
        
        # Call the method
        result = await pipeline.cancel_batch(str(mock_user.id), batch_id)
        
        # Verify the result
        assert result["status"] == "success"
        assert result["message"] == f"No records found for batch {batch_id}"
        assert result["records_deleted"] == 0
    
    @pytest.mark.asyncio
    async def test_cancel_batch_partial_failure(self, pipeline, mock_user, mock_batch_records):
        """Test cancelling a batch with some records failing to delete."""
        # Mock the delete_record method to succeed for first record and fail for others
        def mock_delete_side_effect(user_id, record_id):
            if record_id == mock_batch_records["records"][0].id:
                return {
                    "status": "success",
                    "message": "Record deleted successfully",
                    "record_deleted": True
                }
            else:
                raise Exception(f"Failed to delete record {record_id}")
        
        with patch.object(pipeline, 'delete_record', side_effect=mock_delete_side_effect):
            # Call the method
            result = await pipeline.cancel_batch(
                str(mock_user.id),
                mock_batch_records["batch_id"]
            )
            
            # Verify the result
            assert result["status"] == "success"
            assert result["total_records"] == 3
            assert result["records_deleted"] == 1
            assert len(result["failed_records"]) == 2
            
            # Check that the failed records are correctly identified
            failed_ids = [item["record_id"] for item in result["failed_records"]]
            assert mock_batch_records["records"][1].id in failed_ids
            assert mock_batch_records["records"][2].id in failed_ids
    
    @pytest.mark.asyncio
    async def test_cancel_batch_error(self, pipeline, mock_user):
        """Test cancelling a batch with an error."""
        # Setup test parameters
        batch_id = f"batch-{str(uuid.uuid4())[:8]}"
        
        # Mock the get_user_details method to raise an exception
        with patch.object(pipeline, 'get_user_details', side_effect=Exception("Test error")):
            # Verify that HTTPException is raised
            with pytest.raises(HTTPException) as excinfo:
                await pipeline.cancel_batch(str(mock_user.id), batch_id)
            
            assert excinfo.value.status_code == 500
            assert f"Error cancelling batch" in excinfo.value.detail
