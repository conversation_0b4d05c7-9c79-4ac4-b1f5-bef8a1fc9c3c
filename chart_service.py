from pyecharts import options as opts
from pyecharts.charts import Bar, Line
from pyecharts.commons.utils import JsC<PERSON>
from bs4 import BeautifulSoup, Tag
from schemas import ChartData, UserBiomarker
from typing import List, Dict, Any
from datetime import datetime
import logging


logger = logging.getLogger(__name__)


def _extract_javascript_from_html(html_content: str) -> str:
    """Extracts JavaScript content from HTML using BeautifulSoup.

    Args:
        html_content: HTML string containing script tags

    Returns:
        str: JavaScript content without script tags
    """
    # Parse the HTML content
    soup = BeautifulSoup(html_content, "html.parser")

    # Find the body tag first
    body_tag = soup.find("body")
    if isinstance(body_tag, Tag):
        # Find only the first script tag in the body
        first_script = body_tag.find("script")
        if isinstance(first_script, Tag) and first_script.string:
            return first_script.string.strip()

    # Fallback: if no body or script in body, find first script anywhere
    first_script = soup.find("script")
    if isinstance(first_script, Tag) and first_script.string:
        return first_script.string.strip()

    return ""


def _prepare_biomarker_data_for_charts(
    biomarkers: List[UserBiomarker],
) -> Dict[str, Any]:
    """Prepare biomarker data for charting.

    Args:
        biomarkers: List of UserBiomarker objects

    Returns:
        Dict containing processed data for charts
    """
    if not biomarkers:
        return {
            "dates": [],
            "values": [],
            "test_names": [],
            "units": [],
            "reference_ranges": [],
            "ref_min": [],
            "ref_max": [],
            "chart_title": "No Biomarker Data Available",
            "y_axis_label": "Value",
        }

    # Sort biomarkers by test_date
    sorted_biomarkers = sorted(
        [
            b
            for b in biomarkers
            if b.test_date is not None and b.result_numeric is not None
        ],
        key=lambda x: x.test_date or datetime.min,
    )

    # Extract data for charts
    dates = []
    values = []
    test_names = []
    units = set()
    reference_ranges = []
    ref_min: List[float | None] = []
    ref_max: List[float | None] = []

    # Determine if we have numeric data

    for biomarker in sorted_biomarkers:
        if biomarker.test_date:
            dates.append(biomarker.test_date.strftime("%Y-%m-%d"))
            values.append(biomarker.result_numeric)
            test_names.append(biomarker.test_name)
            units.add(biomarker.unit)
            ref_min.append(biomarker.reference_range_min)
            ref_max.append(biomarker.reference_range_max)
            reference_ranges.append(biomarker.reference_range)

    # Get the most common unit or first available, filtering out None/empty values
    filtered_units = [u for u in units if u and u.strip()]
    unit = filtered_units[0] if filtered_units else ""

    # Create chart title
    first_test_name = test_names[0] if test_names else "Biomarker"
    chart_title = f"{first_test_name} Trends Over Time"

    return {
        "dates": dates,
        "values": values,
        "test_names": test_names,
        "units": list(units),
        "reference_ranges": reference_ranges,
        "ref_min": ref_min,
        "ref_max": ref_max,
        "chart_title": chart_title,
        "y_axis_label": f"Value ({unit})" if unit else "Value",
        "unit": unit,
    }


def create_biomarker_line_chart(biomarkers: List[UserBiomarker]) -> Line:
    """Creates a line chart for biomarker trends over time.

    Args:
        biomarkers: List of UserBiomarker objects

    Returns:
        Line: Line chart instance
    """
    data = _prepare_biomarker_data_for_charts(biomarkers)

    if not data["dates"]:
        # Create empty chart with message
        line = (
            Line()
            .add_xaxis(["No Data"])
            .add_yaxis("No Data Available", [{"value": 0}], is_smooth=True)
            .set_global_opts(
                title_opts=opts.TitleOpts(title="No Biomarker Data Available"),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
            )
        )
        return line

    # Create line chart
    line = Line().add_xaxis(data["dates"])

    # Add main biomarker value series
    line.add_yaxis(
        "Biomarker Value",
        data["values"],
        is_smooth=True,
        symbol="circle",
        symbol_size=10,
        linestyle_opts=opts.LineStyleOpts(width=3, color="#035B64"),
        areastyle_opts=opts.AreaStyleOpts(opacity=0.3, color="#E6FFF7"),
        itemstyle_opts=opts.ItemStyleOpts(color="#035B64"),
        label_opts=opts.LabelOpts(is_show=False),
        markpoint_opts=opts.MarkPointOpts(
            data=[
                opts.MarkPointItem(type_="max", name="Maximum"),
                opts.MarkPointItem(type_="min", name="Minimum"),
            ]
        ),
    )

    # Add reference minimum line
    line.add_yaxis(
        "Reference Min",
        data["ref_min"],
        is_smooth=False,
        is_connect_nones=True,  # Do not connect points with null data
        symbol="emptyCircle",
        symbol_size=8,
        linestyle_opts=opts.LineStyleOpts(
            width=1, color="#0000FF", type_="dashed", opacity=0.6
        ),
        itemstyle_opts=opts.ItemStyleOpts(color="#0000FF"),
        label_opts=opts.LabelOpts(is_show=False),
    )

    # Add reference maximum line
    line.add_yaxis(
        "Reference Max",
        data["ref_max"],
        is_smooth=False,
        symbol="emptyCircle",
        symbol_size=8,
        linestyle_opts=opts.LineStyleOpts(
            width=1, color="#FF0000", type_="dashed", opacity=0.6
        ),
        itemstyle_opts=opts.ItemStyleOpts(color="#FF0000"),
        label_opts=opts.LabelOpts(is_show=False),
    )

    # Inject reference ranges data into JavaScript context first
    reference_ranges_js = f"var referenceRanges = {data['reference_ranges']};"
    line.add_js_funcs(reference_ranges_js)

    # Set global options after injecting JS functions
    line.set_global_opts(
        title_opts=opts.TitleOpts(
            title=data["chart_title"],
            subtitle=(
                f"Unit: {data['unit']}" if data["unit"] else "Biomarker Trend Analysis"
            ),
        ),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            formatter=JsCode(
                """
            function(params) {
                var param = params[0];
                return param.name + '<br/><b>' +
                       param.seriesName + ':</b> ' + param.value + '<br/>' +
                       '<b>Reference Range:</b> ' + (param.dataIndex < referenceRanges.length ? referenceRanges[param.dataIndex] : 'N/A');
            }
            """
            ),
        ),
        xaxis_opts=opts.AxisOpts(
            name="Test Date",
            name_location="middle",
            name_gap=50,
            name_textstyle_opts=opts.TextStyleOpts(font_size=16, font_weight="bold"),
            axislabel_opts=opts.LabelOpts(
                rotate=30, interval=0, font_size=10, font_weight="bold", margin=20
            ),
        ),
        yaxis_opts=opts.AxisOpts(
            name=data["y_axis_label"],
            name_location="middle",
            name_gap=50,
            name_textstyle_opts=opts.TextStyleOpts(font_size=16, font_weight="bold"),
            axislabel_opts=opts.LabelOpts(font_size=14, font_weight="bold"),
        ),
        legend_opts=opts.LegendOpts(
            orient="horizontal", pos_top="8%", pos_left="center"
        ),
        datazoom_opts=[
            opts.DataZoomOpts(
                is_show=True,
                type_="slider",
                xaxis_index=[0],
                range_start=0,
                range_end=100,
                pos_bottom="15%",
            ),
            opts.DataZoomOpts(
                is_show=True,
                type_="inside",
                xaxis_index=[0],
                range_start=0,
                range_end=100,
                pos_bottom="15%",
            ),
        ],
    )

    return line


def create_biomarker_bar_chart(biomarkers: List[UserBiomarker]) -> Bar:
    """Creates a bar chart for biomarker values.

    Args:
        biomarkers: List of UserBiomarker objects

    Returns:
        Bar: Bar chart instance
    """
    data = _prepare_biomarker_data_for_charts(biomarkers)

    if not data["dates"]:
        # Create empty chart with message
        bar = (
            Bar()
            .add_xaxis(["No Data"])
            .add_yaxis("No Data Available", [0])
            .set_global_opts(
                title_opts=opts.TitleOpts(title="No Biomarker Data Available"),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
            )
        )
        return bar

    # Create bar chart
    bar = (
        Bar()
        .add_xaxis(data["dates"])
        .add_yaxis("Biomarker Value", data["values"])
        .set_global_opts(
            title_opts=opts.TitleOpts(
                title=data["chart_title"],
                subtitle=(
                    f"Unit: {data['unit']}"
                    if data["unit"]
                    else "Biomarker Value Distribution"
                ),
            ),
            tooltip_opts=opts.TooltipOpts(
                trigger="axis",
                axis_pointer_type="shadow",
                formatter=JsCode(
                    """
                function(params) {
                    var param = params[0];
                    return param.name + '<br/><b>' +
                           param.seriesName + ':</b> ' + param.value + '<br/>' +
                           '<b>Reference Range:</b> ' + (param.dataIndex < referenceRanges.length ? referenceRanges[param.dataIndex] : 'N/A');
                }
                """
                ),
            ),
            xaxis_opts=opts.AxisOpts(
                name="Test Date",
                name_location="middle",
                name_gap=50,
                name_textstyle_opts=opts.TextStyleOpts(
                    font_size=16, font_weight="bold"
                ),
                axislabel_opts=opts.LabelOpts(
                    rotate=30, interval=0, font_size=10, font_weight="bold", margin=8
                ),
            ),
            yaxis_opts=opts.AxisOpts(
                name=data["y_axis_label"],
                name_location="middle",
                name_gap=50,
                name_textstyle_opts=opts.TextStyleOpts(
                    font_size=16, font_weight="bold"
                ),
                axislabel_opts=opts.LabelOpts(font_size=14, font_weight="bold"),
            ),
            legend_opts=opts.LegendOpts(
                orient="horizontal", pos_top="8%", pos_left="center"
            ),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=True,
                    type_="slider",
                    xaxis_index=[0],
                    range_start=0,
                    range_end=100,
                    pos_bottom="15%",
                ),
                opts.DataZoomOpts(
                    is_show=True,
                    type_="inside",
                    xaxis_index=[0],
                    range_start=0,
                    range_end=100,
                    pos_bottom="15%",
                ),
            ],
        )
        .set_series_opts(
            label_opts=opts.LabelOpts(is_show=True, position="top", font_size=10),
            itemstyle_opts=opts.ItemStyleOpts(
                color="#035B64", border_color="#1976D2", border_width=1
            ),
        )
    )

    # Inject reference ranges data into JavaScript context
    reference_ranges_js = f"var referenceRanges = {data['reference_ranges']};"
    bar.add_js_funcs(reference_ranges_js)

    return bar


def generate_biomarker_line_chart(biomarkers: List[UserBiomarker]) -> str:
    """Generates a line chart for biomarker trends over time.

    Args:
        biomarkers: List of UserBiomarker objects

    Returns:
        str: Rendered chart in HTML format
    """
    try:
        line = create_biomarker_line_chart(biomarkers)
        line.renderer = "svg"
        return line.render_embed()
    except Exception as e:
        logger.error(f"Error generating biomarker line chart: {str(e)}")
        return f"<html><body><h1>Error</h1><p>Failed to generate line chart: {str(e)}</p></body></html>"


def generate_biomarker_bar_chart(biomarkers: List[UserBiomarker]) -> str:
    """Generates a bar chart for biomarker values.

    Args:
        biomarkers: List of UserBiomarker objects

    Returns:
        str: Rendered chart in HTML format
    """
    try:
        bar = create_biomarker_bar_chart(biomarkers)
        bar.renderer = "svg"
        return bar.render_embed()
    except Exception as e:
        logger.error(f"Error generating biomarker bar chart: {str(e)}")
        return f"<html><body><h1>Error</h1><p>Failed to generate bar chart: {str(e)}</p></body></html>"


def get_biomarker_line_chart_data(biomarkers: List[UserBiomarker]) -> ChartData:
    """Gets biomarker line chart data with chart_id and javascript.

    Args:
        biomarkers: List of UserBiomarker objects

    Returns:
        ChartData: ChartData object containing chart_id and javascript
    """
    try:
        line = create_biomarker_line_chart(biomarkers)
        html_content = line.render_embed()
        data = _prepare_biomarker_data_for_charts(biomarkers)

        return ChartData(
            chart_id=line.get_chart_id(),
            javascript=_extract_javascript_from_html(html_content),
            chart_type="line",
            data=data,
        )
    except Exception as e:
        logger.error(f"Error getting biomarker line chart data: {str(e)}")
        return ChartData(
            chart_id="error_chart",
            javascript=f"console.error('Chart generation failed: {str(e)}');",
            chart_type="line",
            data={"error": str(e)},
        )


def get_biomarker_bar_chart_data(biomarkers: List[UserBiomarker]) -> ChartData:
    """Gets biomarker bar chart data with chart_id and javascript.

    Args:
        biomarkers: List of UserBiomarker objects

    Returns:
        ChartData: ChartData object containing chart_id and javascript
    """
    try:
        bar = create_biomarker_bar_chart(biomarkers)
        html_content = bar.render_embed()
        data = _prepare_biomarker_data_for_charts(biomarkers)

        return ChartData(
            chart_id=bar.get_chart_id(),
            javascript=_extract_javascript_from_html(html_content),
            chart_type="bar",
            data=data,
        )
    except Exception as e:
        logger.error(f"Error getting biomarker bar chart data: {str(e)}")
        return ChartData(
            chart_id="error_chart",
            javascript=f"console.error('Chart generation failed: {str(e)}');",
            chart_type="bar",
            data={"error": str(e)},
        )


def create_biomarker_comparison_chart(
    biomarkers_dict: Dict[str, List[UserBiomarker]],
) -> Line:
    """Creates a line chart comparing multiple biomarkers or canonical groups.

    Args:
        biomarkers_dict: Dictionary where keys are biomarker names/groups and values are lists of UserBiomarker objects

    Returns:
        Line: Line chart instance for comparison
    """
    if not biomarkers_dict:
        return create_biomarker_line_chart([])  # Return empty chart

    line = Line()
    all_dates = set()
    all_units = set()

    # Collect all unique dates and units
    for biomarkers in biomarkers_dict.values():
        for biomarker in biomarkers:
            if biomarker.test_date:
                all_dates.add(biomarker.test_date.strftime("%Y-%m-%d"))
            if biomarker.unit and biomarker.unit.strip():
                all_units.add(biomarker.unit.strip())

    # Sort dates
    sorted_dates = sorted(list(all_dates))
    line.add_xaxis(sorted_dates)

    # Determine common unit for y-axis label
    filtered_units = [u for u in all_units if u and u.strip()]
    common_unit = filtered_units[0] if len(filtered_units) == 1 else ""
    y_axis_label = f"Value ({common_unit})" if common_unit else "Value"

    # Add series for each biomarker group
    for group_name, biomarkers in biomarkers_dict.items():
        data = _prepare_biomarker_data_for_charts(biomarkers)
        if data["values"]:
            # Create a mapping of dates to values
            date_value_map = dict(zip(data["dates"], data["values"]))

            # Create values array aligned with sorted_dates, replacing None with 0
            aligned_values = [date_value_map.get(date, 0) for date in sorted_dates]

            line.add_yaxis(
                group_name,
                aligned_values,
                is_smooth=True,
                symbol="circle",
                symbol_size=6,
                is_connect_nones=False,
            )

    # Set global options
    line.set_global_opts(
        title_opts=opts.TitleOpts(
            title="Biomarker Comparison Over Time", subtitle="Multiple biomarker trends"
        ),
        tooltip_opts=opts.TooltipOpts(trigger="axis"),
        xaxis_opts=opts.AxisOpts(
            name="Test Date",
            name_location="middle",
            name_gap=50,
            name_textstyle_opts=opts.TextStyleOpts(font_size=16, font_weight="bold"),
            axislabel_opts=opts.LabelOpts(
                rotate=45, interval=0, font_size=12, font_weight="bold"
            ),
        ),
        yaxis_opts=opts.AxisOpts(
            name=y_axis_label,
            name_location="middle",
            name_gap=50,
            name_textstyle_opts=opts.TextStyleOpts(font_size=16, font_weight="bold"),
            axislabel_opts=opts.LabelOpts(font_size=14, font_weight="bold"),
        ),
        legend_opts=opts.LegendOpts(
            orient="horizontal", pos_top="8%", pos_left="center"
        ),
        datazoom_opts=[
            opts.DataZoomOpts(
                is_show=True,
                type_="slider",
                xaxis_index=[0],
                range_start=0,
                range_end=100,
            )
        ],
    )

    return line
