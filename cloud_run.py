from google.cloud import run_v2
from google.cloud.run_v2 import RunJobRequest, EnvVar
import config
from schemas import N1ReportGenRequest
import logging


logger = logging.getLogger(__name__)


async def launch_report_generation_job(request: N1ReportGenRequest, bucket_name: str = None):
    """
    Runs an existing Cloud Run Job for sequential report generation with an overridden environment variable.
    Args:
        user_id: The USER_ID value to override.
        report_id: The ID of the Google Cloud project.
        file_name: The name to give to the report file.
    """
    user_id = request.user_id
    report_id = request.id
    file_name = f"{request.config.report_name}.pdf"
    model = request.config.model_name
    custom_prompt = request.config.custom_prompt
    report_format = request.config.report_format
    report_style = request.config.report_style
    report_language = request.config.report_language
    temperature = request.config.temperature
    report_flow = request.config.report_flow

    job_parent = f"projects/{config.PROJECT_ID}/locations/{config.REGION}"
    if report_flow == "Langroid":
        await launch_langroid_report_generation_job(request, bucket_name)
    elif report_flow == "Functional":
        await launch_functional_report_generation_job(request, bucket_name)
    else:
        if report_flow == "Template":
            job_full_name = f"{job_parent}/jobs/{config.TEMPLATE_REPORT_GENERATION_JOB_NAME}"
        elif report_flow == "Sequential":
            job_full_name = f"{job_parent}/jobs/{config.SEQUENTIAL_REPORT_GENERATION_JOB_NAME}"
        else:
            raise ValueError(f"Invalid report flow: {report_flow}")

        # Use provided bucket_name or fall back to DEV_BUCKET_NAME
        bucket = bucket_name if bucket_name else config.DEV_BUCKET_NAME

        overrides = RunJobRequest.Overrides(
            container_overrides=[
                RunJobRequest.Overrides.ContainerOverride(
                    env=[
                        EnvVar(name="USER_ID", value=user_id),
                        EnvVar(name="CHR_ID", value=report_id),
                        EnvVar(name="BUCKET_NAME", value=bucket),
                        EnvVar(name="CHR_FILENAME", value=file_name),
                        EnvVar(name="GCP_SERVICE_JSON", value=config.GCP_SERVICE_JSON),
                        EnvVar(name="DATABASE_URL", value=config.DATABASE_URL),
                        EnvVar(name="TABLE_NAME", value=config.BIOMARKERS_TABLE_NAME),
                        EnvVar(name="REPORT_GENERATION_MODEL", value=model),
                        EnvVar(name="DOCUMENT_PROCESSING_MODEL", value=model),
                        EnvVar(name="OPENAI_API_KEY", value=config.OPENAI_API_KEY),
                        EnvVar(name="OPENAI_BASE_URL", value=config.OPENAI_BASE_URL),
                        EnvVar(name="PROJECT_ID", value=config.PROJECT_ID),
                        EnvVar(name="REGION", value=config.REGION),
                        EnvVar(name="TEMPERATURE", value=str(temperature)),
                        EnvVar(name="LANGUAGE", value=report_language),
                        EnvVar(name="N1_API_HEADER", value=config.N1_API_HEADER_NAME),
                        EnvVar(name="N1_API_KEY", value=config.N1_API_KEY),
                        EnvVar(name="N1_API_BASE_URL", value=config.N1_API_BASE_URL),
                    ],
                ),
            ]
        )
        try:
            async with run_v2.JobsAsyncClient() as client:
                request = RunJobRequest(
                    name=job_full_name,
                    overrides=overrides,
                )

                operation = await client.run_job(request=request)
                operation_id = operation.operation.name.split('/')[-1]
                logger.info(f"Launched job {job_full_name} with operation ID {operation_id} ")
                logger.debug(f"Environment variables: USER_ID={user_id}, CHR_ID={report_id}, FILE_NAME={file_name}, BUCKET_NAME={bucket}")
                return operation_id

        except Exception as e:
            raise RuntimeError(f"Failed to launch job: {str(e)}")


async def launch_langroid_report_generation_job(request: N1ReportGenRequest, bucket_name: str = None, region: str = "asia-east1", job_name: str = "langroid-reportgen-workflow-t2"):
    """
    Runs an existing Cloud Run Job for Langroid report generation flow.
    """
    user_id = request.user_id
    report_id = request.id
    file_name = f"{request.config.report_name}.pdf"
    model = request.config.model_name
    custom_prompt = request.config.custom_prompt
    report_format = request.config.report_format
    report_style = request.config.report_style
    report_language = request.config.report_language
    temperature = request.config.temperature
    report_flow = request.config.report_flow

    job_parent = f"projects/{config.PROJECT_ID}/locations/{region}"
    job_full_name = f"{job_parent}/jobs/{job_name}"

    overrides = RunJobRequest.Overrides(
        container_overrides=[
            RunJobRequest.Overrides.ContainerOverride(
                env=[
                    EnvVar(name="USER_ID", value=user_id),
                    EnvVar(name="CHR_ID", value=report_id),
                    EnvVar(name="BUCKET_NAME", value=bucket_name),
                    EnvVar(name="CHR_FILENAME", value=file_name),
                    EnvVar(name="CHAPTER_PLANS", value="n1-systems-approach"),
                    EnvVar(name="DATABASE_URL", value=config.DATABASE_URL),
                    EnvVar(name="TABLE_NAME", value="clinical_data"),
                    EnvVar(name="REPORT_MODEL", value=model),
                    EnvVar(name="OPENAI_API_KEY", value=config.OPENAI_API_KEY),
                    EnvVar(name="OPENAI_BASE_URL", value=config.OPENAI_BASE_URL),
                    EnvVar(name="OPENAI_API_BASE", value=config.OPENAI_BASE_URL),
                    EnvVar(name="PROJECT_ID", value=config.PROJECT_ID),
                    EnvVar(name="REGION", value=config.REGION),
                    EnvVar(name="TEMPERATURE", value=str(temperature)),
                    EnvVar(name="LANGUAGE", value=report_language),
                    # langroid extras
                    EnvVar(name="QDRANT_API_KEY", value=config.QDRANT_API_KEY),
                    EnvVar(name="QDRANT_API_URL", value=config.QDRANT_API_URL),
                    EnvVar(name="EXA_API_KEY", value=config.EXA_API_KEY ),
                    EnvVar(name="FIRECRAWL_API_KEY", value=config.FIRE_CRAWL_API_KEY),
                    EnvVar(name="ROUTER_MODEL", value=config.ROUTER_MODEL),
                    EnvVar(name="DEBUG", value="True"),
                    EnvVar(name="N1_API_HEADER", value=config.N1_API_HEADER_NAME),
                    EnvVar(name="N1_API_KEY", value=config.N1_API_KEY),
                    EnvVar(name="N1_API_REPORT_STATUS_URL", value=config.N1_API_BASE_URL + "/reports/status"),
                ],
                # args=["python", "-m", "scripts.main", "--chapters", "1,4,6,8"]
            ),
        ]
    )
    try:
        async with run_v2.JobsAsyncClient() as client:
            request = RunJobRequest(
                name=job_full_name,
                overrides=overrides,
            )

            operation = await client.run_job(request=request)
            operation_id = operation.operation.name.split('/')[-1]
            logger.info(f"Launched job {job_full_name} with operation ID {operation_id} ")

            return operation_id

    except Exception as e:
        raise RuntimeError(f"Failed to launch job: {str(e)}")


async def launch_functional_report_generation_job(request: N1ReportGenRequest, bucket_name: str = None, region: str = "asia-east1", job_name: str = "functional-report-gen"):
    """
    Runs an existing Cloud Run Job for Functional report generation flow.
    """
    user_id = request.user_id
    report_id = request.id
    file_name = f"{request.config.report_name}.pdf"
    model = request.config.model_name
    report_language = request.config.report_language
    temperature = request.config.temperature

    job_parent = f"projects/{config.PROJECT_ID}/locations/{region}"
    job_full_name = f"{job_parent}/jobs/{job_name}"

    overrides = RunJobRequest.Overrides(
        container_overrides=[
            RunJobRequest.Overrides.ContainerOverride(
                env=[
                    EnvVar(name="USER_ID", value=user_id),
                    EnvVar(name="CHR_ID", value=report_id),
                    EnvVar(name="BUCKET_NAME", value=bucket_name),
                    EnvVar(name="CHR_FILENAME", value=file_name),
                    EnvVar(name="DATABASE_URL", value=config.DATABASE_URL),
                    EnvVar(name="TABLE_NAME", value="clinical_data"),
                    EnvVar(name="MODEL", value=model),
                    EnvVar(name="OPENAI_API_KEY", value=config.OPENAI_API_KEY),
                    EnvVar(name="OPENAI_BASE_URL", value=config.OPENAI_BASE_URL),
                    EnvVar(name="GCP_SERVICE_JSON", value=config.GCP_SERVICE_JSON),
                    EnvVar(name="REGION", value=config.REGION),
                    EnvVar(name="TEMPERATURE", value=str(temperature)),
                    EnvVar(name="LANGUAGE", value=report_language),
                    EnvVar(name="DEBUG", value="True"),
                    EnvVar(name="N1_API_HEADER", value=config.N1_API_HEADER_NAME),
                    EnvVar(name="N1_API_KEY", value=config.N1_API_KEY),
                    EnvVar(name="N1_API_BASE_URL", value=config.N1_API_BASE_URL),
                    EnvVar(name="N1_API_REPORT_STATUS_ENDPOINT", value="/reports/status"),
                ],
            ),
        ]
    )
    try:
        async with run_v2.JobsAsyncClient() as client:
            request = RunJobRequest(
                name=job_full_name,
                overrides=overrides,
            )

            operation = await client.run_job(request=request)
            operation_id = operation.operation.name.split('/')[-1]
            logger.info(f"Launched job {job_full_name} with operation ID {operation_id} ")

            return operation_id

    except Exception as e:
        raise RuntimeError(f"Failed to launch job: {str(e)}")