# Table Schema Overview

## 1. Medicine and Supplements

| Column              | Type      | Description                                                  |
|---------------------|-----------|--------------------------------------------------------------|
| user_id             | UUID      | Identifier for the user                                      |
| id                  | UUID      | Unique identifier for this medication entry                  |
| common_name         | VARCHAR   | Drug or supplement name                                      |
| brand_name          | VARCHAR   | Drug or supplement brand name *(compulsory)*                 |
| dosage              | FLOAT     | Amount per administration (e.g. “500”)                       |
| unit                | VARCHAR   | Unit of administration (e.g. “mg”) *(compulsory)*            |
| route/type          | VARCHAR   | Method of administration (e.g. “oral”, “IV”)                 |
| frequency           | VARCHAR   | How often taken (e.g. “twice daily”)                         |
| start_date          | DATE      | When the user began taking it                                |
| end_date            | DATE      | When the user stopped (if applicable)                        |
| indication/reason   | TEXT      | Reason or condition for prescription                         |
| URL                 | OPTIONAL VARCHAR | URL of the supplement or medication *(compulsory)*    |
| notes               | OPTIONAL TEXT    | Any additional free-text remarks                      |
| page_number         | INTEGER   | Page number in the original document (starting from 1)       |
| additional_data     | JSON      | Additional data attributes  |

---

## 2. Biomarkers

| Column                | Type            | Description                                               |
|-----------------------|-----------------|-----------------------------------------------------------|
| user_id               | UUID            | Identifier for the user                                   |
| id                    | UUID            | Unique identifier for this lab result                     |
| record_id             | VARCHAR         | Unique identifier of the record this was extracted from   |
| test_name             | VARCHAR         | Biomarker name (e.g. “Hemoglobin A1c”)                    |
| raw_value             | VARCHAR         | Result value                                              |
| numeric_value         | OPTIONAL FLOAT  | Only populated if the value/result is a number            |
| unit                  | OPTIONAL VARCHAR| Unit of measure (e.g. “%”, “mg/dL”)                       |
| reference_range       | OPTIONAL VARCHAR| Lab’s normal range (e.g. “4.0–5.6%”)                      |
| reference_range_high  | OPTIONAL FLOAT  | Lab’s high range value (e.g. “4.0”)                       |
| reference_range_low   | OPTIONAL FLOAT  | Lab’s low range value (e.g. “5.6”)                        |
| observation_date      | DATE            | When the sample was observed                              |
| sample source         | VARCHAR         | Type of specimen (e.g. “blood”, “urine”)                  |
| method                | VARCHAR         | Assay method (e.g. “immunoassay”)                         |
| loinc_code            | VARCHAR         | LOINC code that corresponds to this biomarker             |
| out_of_range          | BOOLEAN         | Flag if biomarker out of range                            |
| page_number           | INTEGER         | Page number in the original document(starting from 1)     |
| additional_data       | JSON            | Additional data attributes  |

---

## 3. Diagnoses

| Column         | Type              | Description                                               |
|----------------|-------------------|-----------------------------------------------------------|
| user_id        | UUID              | Identifier for the user                                   |
| id             | UUID              | Unique identifier for this diagnosis entry                |
| record_id      | VARCHAR           | Unique identifier of the record this was extracted from   |
| name           | VARCHAR           | Diagnosis name (e.g. “Type 2 Diabetes Mellitus”)          |
| icd_code       | OPTIONAL VARCHAR  | ICD-11, ICD-10, (or ICD-9) code                           |
| snomed_code    | OPTIONAL VARCHAR  | Snomed code                                               |
| date_diagnosed | DATE              | When the diagnosis was made                               |
| date_resolved  | OPTIONAL DATE     | When the diagnosis was resolved                           |
| status         | VARCHAR           | e.g. “active”, “resolved”, “chronic”                      |
| explanation    | TEXT              | Brief explanation of diagnosis                            |
| page_number    | INTEGER           | Page number in the original document(starting from 1)     |
| additional_data| JSON              | Additional data attributes                                |


---

## 4. Procedures

| Column          | Type     | Description                                              |
|------------------|----------|---------------------------------------------------------|
| user_id          | UUID     | Identifier for the user                                 |
| id               | UUID     | Unique identifier for this procedure                    |
| name             | VARCHAR  | Procedure name (e.g. “Colonoscopy”)                     |
| cpt_code         | OPTIONAL VARCHAR | CPT or HCPCS code                               |
| date_performed   | DATE     | When the procedure took place                           |
| outcome          | TEXT     | Result or immediate follow-up findings                  |
| explanation      | TEXT     | Explanation of procedure                                |
| record_id        | UUID     | Unique identifier of the record this was extracted from |
| page_number      | INTEGER  | Page number in the original document(starting from 1)   |
| additional_data  | JSON     | Additional data attributes                              |

---

## 5. Genetics

| Column         | Type    | Description                                                |
|----------------|---------|------------------------------------------------------------|
| user_id        | UUID    | Identifier for the user                                    |
| id             | UUID    | Unique identifier for this genetic finding                 |
| gene           | VARCHAR | Gene symbol (e.g. “BRCA1”)                                 |
| allele         | VARCHAR | Allele                                                     |
| prevalence     | FLOAT   | How often it appears in the population                     |
| variant        | VARCHAR | Variant nomenclature (e.g. “c.68_69del”)                   |
| test_date      | DATE    | When the genetic test was performed                        |
| record_id      | VARCHAR | Unique identifier of the record this was extracted from    |
| page_number    | INTEGER | Page number in the original document(starting from 1)      |
| additional_data  | JSON     | Additional data attributes                              |

---

## Progress Tracker Stages

**Steps in Progress Tracker:**

1. **Starting**
2. **Extracting**
   - Verbatim copy-paste from document
3. **Inferring**
   - AI populates fields based on data in record
4. **Enriching**
   - AI populates fields using external knowledge (e.g. health areas, LOINC via MCP server)
5. **Updating**
   - Final adjustments before completion
6. **Completed**
   - All fields populated, table finalized

