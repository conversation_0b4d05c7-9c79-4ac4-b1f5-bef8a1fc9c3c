from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from config import DATABASE_URL
import sys
import logging
from contextlib import contextmanager

# Configure logger
logger = logging.getLogger(__name__)


# Log debug information
logger.info(f"DATABASE_URL from environment: {DATABASE_URL}")

# Create SQLAlchemy engine
if DATABASE_URL:
    try:
        # Use the DATABASE_URL environment variable if available
        logger.info(f"Attempting to connect using DATABASE_URL: {DATABASE_URL}")

        # Check if using SQLite (for tests)
        if DATABASE_URL.startswith('sqlite'):
            # SQLite doesn't support all pool parameters
            engine = create_engine(
                DATABASE_URL,
                pool_pre_ping=True  # This parameter is supported by SQLite
            )
        else:
            # Configure connection pool with optimized settings for PostgreSQL
            engine = create_engine(
                DATABASE_URL,
                pool_size=20,  # Reduced from 30
                max_overflow=20,  # Reduced from 30
                pool_timeout=120,  # Increased from 60 to give more time for connections
                pool_recycle=1800,  # Recycle connections after 30 minutes instead of 1 hour
                pool_pre_ping=True  # Enable connection health checks before using from pool
            )
        # Test the connection
        with engine.connect() as conn:
            logger.info("Database connection successful!")
    except Exception as e:
        logger.error(f"Error connecting to database with DATABASE_URL: {str(e)}")
        sys.exit(1)
else:
    logger.error("DATABASE_URL not found in environment variables.")
    logger.error("Please set the DATABASE_URL environment variable or source the env.sh file.")
    logger.error("You can source the env.sh file with: source env.sh")
    sys.exit(1)

# create SQLAlchemy ORM session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


@contextmanager
def get_db_session(commit=True):
    """Context manager for database sessions.

    Usage:
        with get_db_session() as db:
            # Use db for database operations
            db.add(some_object)
            # No need to call commit() as it's handled by the context manager
    """
    session = SessionLocal()
    try:
        yield session
        if commit :
            session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
