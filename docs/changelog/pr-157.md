## Changes

- Fix: Test cases
- chore: add changelog entry for PR 157
- Fix: Added alembic record, fixed record test cases, added canonical data in biomarkers
- chore: add changelog entry for PR 157
- Fix : PR comments
- chore: add changelog entry for PR 157
- Fix : PR Comments
- chore: add changelog entry for PR 157
- Fix : PR comments
- chore: add changelog entry for PR 157
- Refactor biomarker handling: Remove LOINC references and integrate canonical logic UserBiomarkerORM instead of ClinicalDataORM. - Created new integration tests for canonical endpoints. - Adjusted utility functions to accommodate the removal of LOINC and added handling for canonical names.
## PR Details
- PR #157
- Author: yash-n1
- Target Branch: develop
