## Changes

- printing status code (#96)
- failsafe check (#94)
- chore: add changelog entry for PR 93
- Enhance changelog workflow to fetch full history and handle missing merge base gracefully, ensuring accurate commit listings for PRs.
- Remove changelog entries for PR 91 and PR 92 as part of repository cleanup.
- Improve changelog generation by dynamically determining the merge base commit to show only unique commits for the current PR.
- chore: add changelog entry for PR 92
- chore: add changelog entry for PR 91
- Refactor changelog generation to use the base branch dynamically instead of hardcoding 'main'
- chore: add changelog entry for PR 91
- Updating changelog logic
- chore: update docs/changelog.md
- added diagnosis and procedure syncing (#89)
- added ref range min max is numeric and out of range (#88)
- added admin flag to user (#87)
- Cu 86c3ryr0u update user stats to include diagnosis procedures medications and genetics (#86)
- include all data types in stats (#85)
- convert whole pydantic types to ORM objects (#84)
- delete all user data (#83)
- Cu 86c3rerr2 add pagenumber to biomarkers table ashrith jacob (#82)
- chore: update docs/changelog.md
- Cu 86c3gx0um update add jsonb field to all tables (#80)
- added features for genetics (#79)
- added user procedure features (#77)
- cleanup: set all config vars in one branch (#78)
- update and push uv.lock
- Cu 86c3nqm42 extraction status bug (#76)
- update the schema to include page_number
- chore: update docs/changelog.md
- added features for supporting diagnoses (#74)
- update schemas: set id names to follow standard practice (#73)
- update README with table schemas (#72)
- improved info for better openapi spec generation (#71)
- add chr chat streaming endpoint (#70)
- change MODEL value in config (#68)
- add flag to skip bubble updates (#67)
-  Add new env var support for CHAPTER_PLANS (#66)
- chore: update docs/changelog.md
- added tests for loinc (#62)
## PR Details
- PR #97
- Author: ashrithjacob
- Target Branch: main
