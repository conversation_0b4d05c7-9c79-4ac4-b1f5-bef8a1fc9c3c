## Changes

- Cu 86c46gm09 paginated endpoint for biomarkers umar shah (#146)
- chore: add changelog entry for PR 145
- paginated medication endpoint (#144)
- paginated procedures endpoint (#143)
- chore: add changelog entry for PR 142
- added paginated genetics endpoint (#141)
- chore: add changelog entry for PR 140
- added realtime progress updates via websockets (#139)
- added documentation to run langroid locally (#137)
- chore: add changelog entry for PR 138
- Cu 86c47mzcx n1 api hotfixes umar shah (#136)
- chore: add changelog entry for PR 135
- make record_id optional for diagnoses and procedures (#134)
- make sort_order -> is_descending (#133)
- added demo charts service and a test endpoint (#132)
- chore: add changelog entry for PR 130
- paginated and filtered diagnoses (#131)
- chore: add changelog entry for PR 130
- Cu 86c42ydmd invalidate user cache on update profile umar shah (#129)
- chore: add changelog entry for PR 128
- Cu 86c4304cm modify report generate endpoint to handle new fields  (#126)
- changes to the env environment example
- chore: add changelog entry for PR 125
- Cu 86c42r8kp create user endpoint for ai flows to get user profile data umar shah (#124)
- chore: add changelog entry for PR 123
- Cu 86c4265fx bulk create endpoints for extracted entities umar shah (#122)
- chore: add changelog entry for PR 121
- Cu 86c3vkt66 post processing work for biomarkers ashrith jacob (#120)
- chore: add changelog entry for PR 119
- Cu 86c425fjt bubbl endpoint authentication umar shah (#118)
- added auth headers to bubbl calls (#117)
- fix failing tests (#116)
- chore: add changelog entry for PR 115
- Cu 86c3vkt66 post processing work for biomarkers ashrith jacob (#114)
- chore: add changelog entry for PR 113
- Cu 86c3vkt66 post processing work for biomarkers ashrith jacob (#112)
- chore: add changelog entry for PR 111
- Cu 86c3vkt66 post processing work for biomarkers ashrith jacob (#110)
- chore: add changelog entry for PR 109
- update schema changes
- chore: add changelog entry for PR 107
- Debugging api (#108)
- chore: add changelog entry for PR 107
- fix schema differences (#106)
- chore: add changelog entry for PR 105
- Feature/buildfile (#104)
- chore: add changelog entry for PR 103
- rename duplicate function names (#102)
- chore: add changelog entry for PR 100
- fix endpoint return value
- chore: add changelog entry for PR 99
- fix alignment of function (#98)
- chore: add changelog entry for PR 97
- printing status code (#96)
- failsafe check (#94)
- chore: add changelog entry for PR 93
- Enhance changelog workflow to fetch full history and handle missing merge base gracefully, ensuring accurate commit listings for PRs.
- Remove changelog entries for PR 91 and PR 92 as part of repository cleanup.
- Improve changelog generation by dynamically determining the merge base commit to show only unique commits for the current PR.
- chore: add changelog entry for PR 92
- chore: add changelog entry for PR 91
- Refactor changelog generation to use the base branch dynamically instead of hardcoding 'main'
- chore: add changelog entry for PR 91
- Updating changelog logic
- chore: update docs/changelog.md
- added diagnosis and procedure syncing (#89)
- added ref range min max is numeric and out of range (#88)
- added admin flag to user (#87)
- Cu 86c3ryr0u update user stats to include diagnosis procedures medications and genetics (#86)
- include all data types in stats (#85)
- convert whole pydantic types to ORM objects (#84)
- delete all user data (#83)
- Cu 86c3rerr2 add pagenumber to biomarkers table ashrith jacob (#82)
- chore: update docs/changelog.md
- Cu 86c3gx0um update add jsonb field to all tables (#80)
- added features for genetics (#79)
- added user procedure features (#77)
- cleanup: set all config vars in one branch (#78)
- update and push uv.lock
- Cu 86c3nqm42 extraction status bug (#76)
- update the schema to include page_number
- chore: update docs/changelog.md
- added features for supporting diagnoses (#74)
- update schemas: set id names to follow standard practice (#73)
- update README with table schemas (#72)
- improved info for better openapi spec generation (#71)
- add chr chat streaming endpoint (#70)
- change MODEL value in config (#68)
- add flag to skip bubble updates (#67)
-  Add new env var support for CHAPTER_PLANS (#66)
- chore: update docs/changelog.md
- added tests for loinc (#62)
## PR Details
- PR #147
- Author: umars
- Target Branch: main
