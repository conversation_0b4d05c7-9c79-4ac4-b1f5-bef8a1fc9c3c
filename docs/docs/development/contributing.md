# Contributing Guide

Thank you for your interest in contributing to the N1 API project! This guide will help you understand how to contribute effectively.

## Code of Conduct

Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

## Development Workflow

1. **Fork the Repository**
   - Create a fork of the main repository
   - Clone your fork locally

2. **Create a Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Changes**
   - Follow the coding standards
   - Write tests for new features
   - Update documentation

4. **Commit Changes**
   ```bash
   git commit -m "Description of changes"
   ```

5. **Push Changes**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create Pull Request**
   - Open a PR against the main repository
   - Include a clear description
   - Reference any related issues

## Coding Standards

1. **Python Style**
   - Follow PEP 8 guidelines
   - Use type hints
   - Document functions and classes

2. **Testing**
   - Write unit tests for new features
   - Maintain test coverage
   - Run tests before submitting PR

3. **Documentation**
   - Update relevant documentation
   - Include docstrings
   - Add comments for complex logic

## Pull Request Process

1. **Review Requirements**
   - All tests must pass
   - Code must be properly formatted
   - Documentation must be updated

2. **Review Process**
   - At least one approval required
   - Address review comments
   - Update PR as needed

3. **Merge Process**
   - Squash commits if needed
   - Use conventional commit messages
   - Update version numbers if required

## Getting Help

- Open an issue for questions
- Join our development chat
- Contact the maintainers

## Thank You!

We appreciate your contributions to making the N1 API better! 