# Development Setup

This guide will help you set up your development environment for the N1 API project.

## Prerequisites

- Python 3.8 or higher
- Git
- Virtual environment (recommended)

## Installation Steps

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-org/n1-api.git
   cd n1-api
   ```

2. **Create and Activate Virtual Environment**
   ```bash
   # Windows
   python -m venv venv
   .\venv\Scripts\activate

   # Linux/MacOS
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure Environment Variables**
   ```bash
   cp env.sh.example env.sh
   # Edit env.sh with your configuration
   ```

5. **Initialize Database**
   ```bash
   python database.py
   ```

6. **Run the Development Server**
   ```bash
   python main.py
   ```

## Development Tools

- **Code Editor**: VS Code (recommended)
- **API Testing**: Postman or curl
- **Database Management**: SQLAlchemy
- **Version Control**: Git

## Common Issues

1. **Database Connection**
   - Ensure PostgreSQL is running
   - Check connection string in env.sh

2. **Dependencies**
   - Use exact versions from requirements.txt
   - Recreate virtual environment if needed

3. **Environment Variables**
   - All required variables must be set
   - Check env.sh.example for reference

## Next Steps

- [API Overview](../api/overview.md)
- [Contributing Guide](contributing.md) 