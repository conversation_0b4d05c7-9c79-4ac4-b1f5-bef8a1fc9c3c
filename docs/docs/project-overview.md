# Project Overview

Welcome to the N1 API documentation. This documentation provides comprehensive information about the N1 API project, including its architecture, features, and development guidelines.

## About the Project

The N1 API is a FastAPI-based service that acts as the orchestrator of all AI workflows. It primarily handles two types of workflows:

1. **Records**: Preprocessing of medical records that users upload as files
2. **Reports**: Generation of comprehensive medical reports based on available medical records

## Key Features

- PDF generation from Markdown content
- Google Cloud Storage integration
- Signed URL generation for secure file access
- Docker containerization
- Background task processing
- User data management
- Clinical data management
- Biomarker retrieval and filtering
- Record and report deletion

## Documentation Structure

This documentation is organized into several sections:

1. **Project Overview** (you are here)
   - General information about the project
   - Key features and capabilities
   - Project structure

2. **API Reference**
   - Detailed API documentation
   - Endpoint specifications
   - Request/response formats

3. **Development**
   - Setup instructions
   - Contributing guidelines
   - Development workflow

## Getting Started

To get started with the N1 API:

1. Review the [Setup Guide](../development/setup.md)
2. Explore the [API Reference](../api/overview.md)
3. Check the [Contributing Guide](../development/contributing.md)

## Project README

For more detailed information about the project, including setup instructions, deployment details, and architecture overview, please refer to the [Project README](../README.md). 