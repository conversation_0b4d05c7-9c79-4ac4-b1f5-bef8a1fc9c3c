# API Endpoints

This document provides detailed information about all available API endpoints.

## Authentication

All endpoints require authentication using JWT tokens.

### Get Authentication Token

```http
POST /api/v1/auth/token
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

Response:
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer"
}
```

## User Management

### Get User Profile

```http
GET /api/v1/users/me
Authorization: Bearer {token}
```

Response:
```json
{
    "id": 1,
    "username": "user123",
    "email": "<EMAIL>",
    "role": "user"
}
```

### Update User Profile

```http
PUT /api/v1/users/me
Authorization: Bearer {token}
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

## Resource Endpoints

### List Resources

```http
GET /api/v1/resources
Authorization: Bearer {token}
```

Query Parameters:
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 10)
- `sort`: Sort field (default: created_at)
- `order`: Sort order (asc/desc)

### Get Resource

```http
GET /api/v1/resources/{id}
Authorization: Bearer {token}
```

### Create Resource

```http
POST /api/v1/resources
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "New Resource",
    "description": "Resource description"
}
```

### Update Resource

```http
PUT /api/v1/resources/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Updated Resource",
    "description": "Updated description"
}
```

### Delete Resource

```http
DELETE /api/v1/resources/{id}
Authorization: Bearer {token}
```

## Error Responses

All endpoints may return the following error responses:

```json
{
    "error": {
        "code": "ERROR_CODE",
        "message": "Error description"
    }
}
```

Common error codes:
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error 