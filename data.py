"""
Main data processing service that integrates with the web API client
"""
import json
from typing import Dict, Any, List
from web import WebAPIClient
import logging

logger = logging.getLogger(__name__)

class DataProcessingService:
    def __init__(self, web_api_url: str = "http://localhost:8080"):
        self.web_api_client = WebAPIClient(web_api_url)
        
    def extract_medical_data(self, uploaded_file_path: str) -> Dict[str, Any]:
        """
        Extract medical data from uploaded file
        This is a placeholder - implement your actual extraction logic here
        """
        # Placeholder implementation
        # Replace this with your actual data extraction logic
        extracted_data = {
            "record_id": "generated_id",
            "user_id": "extracted_user_id",
            "year": 2023,
            "month": 12,
            "day": 15,
            "test_name": "extracted_test_name",
            "result": "extracted_result",
            "reference_range": "extracted_reference_range",
            "sample_source": "extracted_sample_source",
            "method": "extracted_method",
            "unit": "extracted_unit",
            "filename": uploaded_file_path,
            "context": "extracted_context",
            "loinc_code": "extracted_loinc_code",
            "health_areas": ["extracted_health_area"]
        }
        return extracted_data
    
    def process_uploaded_file(self, file_path: str) -> Dict[str, Any]:
        """
        Complete pipeline: extract data and send for post-processing
        """
        try:
            # Step 1: Extract data from the uploaded file
            logger.info(f"Extracting data from {file_path}")
            extracted_data = self.extract_medical_data(file_path)
            
            # Step 2: Send to web API for post-processing
            logger.info("Sending data for post-processing")
            processed_data = self.web_api_client.send_for_processing(extracted_data)
            
            if processed_data:
                logger.info("Successfully processed medical record")
                return processed_data
            else:
                logger.error("Failed to post-process the data")
                return extracted_data  # Return original data if post-processing fails
                
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            raise
    
    def batch_process_files(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        """
        Process multiple files in batch
        """
        results = []
        for file_path in file_paths:
            try:
                result = self.process_uploaded_file(file_path)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {str(e)}")
                # Continue with other files
                continue
        return results

# Example usage
def main():
    # Initialize the service
    service = DataProcessingService("http://localhost:8080")
    
    # Process a single file
    try:
        result = service.process_uploaded_file("sample_medical_record.pdf")
        print("Processed result:")
        print(json.dumps(result, indent=2))
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()