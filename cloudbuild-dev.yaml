steps:
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '--build-arg'
      - VERSION_ARG=$SHORT_SHA
      - '-t'
      - 'gcr.io/n1-healthcare/github.com/n1healthcare/api-backend:$BRANCH_NAME'
      - '-t'
      - 'gcr.io/n1-healthcare/github.com/n1healthcare/api-backend:$SHORT_SHA'
      - .
    id: BuildImage
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - 'gcr.io/n1-healthcare/github.com/n1healthcare/api-backend:$SHORT_SHA'
    id: PushImageSHA
    waitFor:
      - BuildImage
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - 'gcr.io/n1-healthcare/github.com/n1healthcare/api-backend:$BRANCH_NAME'
    id: PushImageBranch
    waitFor:
      - BuildImage
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk
    args:
      - beta
      - run
      - jobs
      - deploy
      - n1-dev-migration-job
      - '--image'
      - 'gcr.io/n1-healthcare/github.com/n1healthcare/api-backend:$SHORT_SHA'
      - '--tasks'
      - '1'
      - '--set-secrets'
      - 'DATABASE_URL=N1_DEV_DB_URL:1'
      - '--vpc-egress'
      - private-ranges-only
      - '--network'
      - n1-default
      - '--subnet'
      - n1-default
      - '--command'
      - alembic
      - '--args'
      - 'upgrade,head'
      - '--region'
      - us-central1
      - '--project'
      - $PROJECT_ID
      - '--quiet'
    id: DeployMigrationJob
    waitFor:
      - PushImageSHA
    entrypoint: gcloud
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk
    args:
      - beta
      - run
      - jobs
      - execute
      - n1-dev-migration-job
      - '--wait'
      - '--region'
      - us-central1
      - '--project'
      - $PROJECT_ID
    id: ExecuteMigrationJob
    waitFor:
      - DeployMigrationJob
    entrypoint: gcloud
images:
  - 'gcr.io/n1-healthcare/github.com/n1healthcare/api-backend:$BRANCH_NAME'
  - 'gcr.io/n1-healthcare/github.com/n1healthcare/api-backend:$SHORT_SHA'
options:
  logging: CLOUD_LOGGING_ONLY
