[project]
name = "n1-api"
version = "0.1.1"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.13",
    "fastapi[all]>=0.115.8",
    "google-cloud-run>=0.10.15",
    "google-cloud-storage>=3.0.0",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.10.6",
    "sqlalchemy>=2.0.38",
    "aioboto3>=11.2.0",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "pytest-cov>=6.1.1",
    "mkdocs>=1.6.1",
    "mkdocs-material>=9.6.12",
    "alembic>=1.15.2",
    "httpx>=0.28.1",
    "pandas>=2.3.0",
    "langchain-openai>=0.3.22",
    "litellm>=1.72.4",
    "cachetools>=5.5.2",
    "pyecharts>=2.0.8",
    "beautifulsoup4>=4.13.4",
]

[tool.setuptools]
packages = ["routes", "alembic", "tests", "utils"]
