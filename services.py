import logging
from datetime import datetime, timezone, timedelta
from storage import FileStorageService
from typing import List, Dict, Optional, Any
from models import (
    UserRecordRequestORM,
    UserReportGenRequestORM,
    #remove clinicaldata
    ClinicalDataORM,
    #ToDo : remove loinc
    LoincRecordORM,
    CanonicalRecordORM,
    UserBiomarkerORM,
    UserORM,
    UserRequestStageORM,
    UserQuestionResponseORM,
    UserProfileORM,
    UserMedicationORM,
    UserDiagnosisORM,
    UserProcedureORM,
    UserGeneticsORM,
)
from schemas import (
    N1RecordUpdateRequest,
    N1ReportGenRequest,
    BiomarkerResponse,
    ProcedureResponse,
    DiagnosisResponse,
    UserDeletionResponse,
    DBRowDeletionStatus,
    ClinicalData,
    UserBiomarkerBase,
    RecordProgressUpdateRequest,
    ReportProgressUpdateRequest,
    #ToDo : remove loinc
    LoincR<PERSON>ord as LoincRecordSchema,
    CanonicalBiomarkerBase,
    ClinicalDataDeleted,
    CanonicalBiomarkerCreate,
    User,
    UserStats,
    RecordCounts,
    UserRegistration,
    QuestionResponse,
    UserProfile,
    UserMedication,
    UserMedicationCreate,
    MedicationUpdate,
    UserDiagnosis,
    UserDiagnosisCreate,
    UserDiagnosisBase,
    UserDiagnosisBulkCreate,
    UserProcedure,
    UserProcedureCreate,
    UserProcedureBase,
    UserProcedureBulkCreate,
    UserGenetics,
    UserGeneticsCreate,
    UserGeneticsBase,
    UserGeneticsBulkCreate,
    UserBiomarker,
    UserBiomarkerCreate,
    UserBiomarkerBase,
    UserBiomarkerBulkCreate,
    BulkCanonicalBiomarkerCreate,
    CanonicalBiomarker,
    CanonicalBiomarkerUpdate,
    CanonicalBiomarkerQueryParams,
    PaginatedCanonicalBiomarkerResponse,
    UserDetails,
    DiagnosisQueryParams,
    PaginatedDiagnosisResponse,
    GeneticsQueryParams,
    PaginatedGeneticsResponse,
    ProcedureQueryParams,
    PaginatedProcedureResponse,
    MedicationQueryParams,
    PaginatedMedicationResponse,
    BiomarkerQueryParams,
    PaginatedBiomarkerResponse,
    RecordQueryParams,
    PaginatedRecordResponse,
    ReportQueryParams,
    PaginatedReportResponse,
    BatchCompletionRequest,
    BatchCompletionResponse
)

from pagination_helper import (
    PaginationHelper,
    get_medication_pagination_config,
    get_diagnosis_pagination_config,
    get_genetics_pagination_config,
    get_procedure_pagination_config,
    get_biomarker_pagination_config,
    get_record_pagination_config,
    get_report_pagination_config,
    get_canonical_biomarker_pagination_config
)

from pagination_helper import (
    PaginationHelper,
    get_medication_pagination_config,
    get_diagnosis_pagination_config,
    get_genetics_pagination_config,
    get_procedure_pagination_config,
    get_biomarker_pagination_config
)

from sqlalchemy import text, func

from sqlalchemy.orm import Query
from fastapi import HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
import json
import aiohttp
from cloud_run import launch_report_generation_job
from cachetools import LRUCache, cached
from cachetools.keys import hashkey
from typing import Union, Optional, Dict, List


import config
from database import get_db_session
from batch_run import launch_data_processing_job_gcp
from aws_batch import launch_data_processing_job_aws
import re
from uuid import UUID
from ws_service import ConnectionManager

USER_CACHE = LRUCache(maxsize=1000)


BUBBLE_API_HEADERS = {
    "Content-Type": "application/json"
}
if config.BUBBLE_API_AUTH_KEY:  # Add Authorization only if auth key is non-empty
    BUBBLE_API_HEADERS["Authorization"] = f"Bearer {config.BUBBLE_API_AUTH_KEY}"

class N1ProcessPipeline:
    # Class-level logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    logger.propagate = True
    storage_service = FileStorageService()

    def __init__(self, connection_manager: ConnectionManager):
        """ instance specific initialization here """
        self.logger.debug("Created new N1ProcessPipeline Object")
        self.loinc_short_map = self.get_loinc_short_name_map()
        # self.canonical_short_map = self.get_canonical_short_name_map()
        self.connection_manager = connection_manager

    def get_loinc_short_name_map(self, ) -> Dict[str, LoincRecordSchema]:
        """ get the LOINC short name map """
        loinc_map = {}
        with get_db_session() as db:
            # Fetch all LoincRecord objects from the database
            loinc_records = db.query(LoincRecordORM).all()
            # Create a dictionary mapping LOINC codes to their short names
            for record in loinc_records:
                record: LoincRecordSchema = LoincRecordSchema.model_validate(record)
                loinc_map[record.loinc_num] = record
        return loinc_map

    def get_canonical_short_name_map(self, ) -> Dict[str, CanonicalBiomarkerBase]:
        """ get the Canonical short name map """
        canonical_map = {}
        with get_db_session() as db:
            # Fetch all CanonicalBiomarkerBase objects from the database
            canonical_records = db.query(CanonicalRecordORM).all()
            # Create a dictionary mapping Canonical codes to their short names
            for record in canonical_records:
                record: CanonicalBiomarkerBase = CanonicalBiomarkerBase.model_validate(record)
                canonical_map[record.canonical_name] = record
        return canonical_map

    async def __process_batch_update(self, user_id: str, bucket_name:str, parser_type:str, parser_cloud:str, record_ids:list, batch_id:str|None = None, parser_model:str|None = None):
        """ assume all options are already validated and correct """
        if parser_cloud == "Google":
            parser_config = config.GCP_DATA_PROCESSING_PARSERS.get(parser_type)
            if parser_config:
                parser = parser_config.copy()  # Avoid mutating global config
                if parser_model:
                    parser['model'] = parser_model
                await launch_data_processing_job_gcp(user_id, record_ids, bucket_name, parser, batch_id)
            else:
                self.logger.error(f"{parser_type} Not Supported on cloud {parser_cloud}")
        elif parser_cloud == "Amazon":
            parser_config = config.AWS_DATA_PROCESSING_PARSERS.get(parser_type)
            if parser_config:
                parser = parser_config.copy()  # Avoid mutating global config
                if parser_model:
                    parser['model'] = parser_model
                await launch_data_processing_job_aws(user_id, record_ids, bucket_name, parser)
            else:
                self.logger.error(f"{parser_type} Not Supported on cloud {parser_cloud}")
        else:
            self.logger.error(f"Unknown parser cloud option found {parser_cloud}")

    async def __process_record_update(self, user_id: UUID, record_id: str, batch_id: str, file_object, file_name: str, content_type: str):
        """
        Internal method to process the record update in the background
        Args:
            user_id (UUID): The user ID
            record_id (str): The record ID
            batch_id (str): The batch ID for the record
            file_object: The file object to be uploaded
            file_name (str): The name of the file
            content_type (str): The content type of the file
        Returns:

        """
        try:
            now = datetime.now(timezone.utc)
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                request_orm = UserRecordRequestORM(
                    id=record_id,
                    user_id=user_details.id,
                    file_name=file_name,
                    progress=0,
                    type="RECORD",
                    status="PENDING",
                    config=None,
                    batch_id=batch_id,
                    error=None,
                    created_at=now,
                    updated_at=now
                )
                db.add(request_orm)
                db.commit()
                try:
                    # Upload file to GCS
                    url = self.storage_service.upload_file_to_gcs(file_object, file_name, content_type, str(user_details.id), "record", record_id, user_details.bucket_name)

                    # Update record with URL
                    setattr(request_orm, 'url', url)
                    setattr(request_orm, 'status', "SYNCED")

                    db.add(request_orm)
                    db.commit()
                    db.refresh(request_orm)
                    # Call update_bubble_record_status after the session is closed
                    # Construct N1RecordUpdateRequest from the ORM instance
                    # Ensure all necessary fields for N1RecordUpdateRequest are present in request_orm
                    # or handle potential missing fields if model_validate is strict.
                    record_update_payload = N1RecordUpdateRequest.model_validate(request_orm)
                    await self.update_bubble_record_status(record_update_payload)
                    # ToDo remove background processing
                    if batch_id is None or len(batch_id) == 0:
                        raise ValueError(f"Record processing request for user {user_details.id} for record {record_id} does not have a batch id")
                except Exception as e:
                    # Update record with error status
                    self.logger.error(str(e))
                    setattr(request_orm, 'status', "ERROR")
                    setattr(request_orm, 'error', str(e))
                    db.add(request_orm)
                    db.commit()
        except Exception as e:
            # Update record with error status
            self.logger.error(str(e))

    async def update_existing_user_records(self, user_id: UUID, batch_id:str|None, parser_type:str, parser_cloud:str, resume:bool, background_tasks: BackgroundTasks, parser_model:str|None = None):
        try:
            # Create initial record
            now = datetime.now(timezone.utc)
            user_details = self.get_user_details(user_id)
            # Get all record ids for the user and batch_id from record_requests
            with get_db_session(commit=False) as db:
                # Build query with user_id filter
                query = db.query(UserRecordRequestORM).filter(
                    UserRecordRequestORM.user_id == user_id
                )

                # Add batch_id filter only if batch_id is not None
                if batch_id is not None:
                    query = query.filter(UserRecordRequestORM.batch_id == batch_id)

                # Add progress filter if resume is True
                if resume:
                    query = query.filter(UserRecordRequestORM.progress < 100)

                records = query.all()

            record_ids = [record.id for record in records]
            self.logger.info(f"Found {len(record_ids)} records for user {user_id} and batch {batch_id if batch_id else 'all'}")

            # Delete associated clinical data records for these record IDs in a single query
            if record_ids:  # Only proceed if there are record IDs
                with get_db_session() as db:
                    deleted_count = db.query(ClinicalDataORM).filter(
                        ClinicalDataORM.user_id == user_id,
                        ClinicalDataORM.record_id.in_(record_ids)
                    ).delete()
                    self.logger.info(f"Deleted {deleted_count} clinical data records for {len(record_ids)} records")
                    db.commit()

            # Add task to background tasks
            # TODO: parser_cloud should ideally be a parameter for update_existing_user_records.
            # Defaulting to "Google" for now to resolve immediate Pylance error.
            # This should be reviewed and potentially changed to be passed from the route.
            background_tasks.add_task(
                self.__process_batch_update,
                str(user_details.id),
                user_details.bucket_name,
                parser_type,
                parser_cloud,
                record_ids,
                batch_id,
                parser_model
            )
            return {"batch_id": batch_id, "status": "queued", "updated_at": now, "record_count": len(record_ids)}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    async def update_batch_user_records(self, user_id: UUID, batch_id:str, parser_type:str, parser_cloud:str, parser_model:str|None, background_tasks: BackgroundTasks) -> Dict[str, Any]:
        """
        launch batch processing request
        Args:
            user_id (UUID): id of the user
            batch_id (str): id of the batch
            parser_type (str): parser workflow to be used (default Sequential)
            parser_cloud (str): cloud to be used (default GCP)
            parser_model (str|None): AI model to use for parsing (optional, uses default if not provided)
        Returns:
            dict: A dictionary containing the batch processing request details
        """
        try:
            # Create initial record
            now = datetime.now(timezone.utc)
            user_details = self.get_user_details(user_id)
            # Get all record ids for the user and batch_id from record_requests
            with get_db_session(commit=False) as db:
                records = db.query(UserRecordRequestORM).filter(
                    UserRecordRequestORM.user_id == user_id,
                    UserRecordRequestORM.batch_id == batch_id
                ).all()
            record_ids = [record.id for record in records]
            self.logger.info(f"Found {len(record_ids)} records for user {user_id} and batch {batch_id}")
            # Add task to background tasks
            background_tasks.add_task(
                self.__process_batch_update,
                str(user_details.id),
                user_details.bucket_name,
                parser_type,
                parser_cloud,
                record_ids,
                batch_id,
                parser_model
            )
            return {"batch_id": batch_id, "status": "queued", "updated_at": now, "record_count": len(record_ids)}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    async def reprocess_single_records(self, user_id: UUID, record_id:str, parser_type:str, parser_cloud:str, background_tasks: BackgroundTasks, parser_model:str|None = None):
        """
        Reprocess a single record and queue it for background processing
        Args:
            user_id (UUID): id of the user
            record_id (str): id of the record to be reprocessed
            parser_type (str): parser workflow to be used (default Sequential)
            parser_cloud (str): cloud to be used (default GCP)
            background_tasks (BackgroundTasks): background tasks instance to be used
            parser_model (str|None): AI model to use for parsing (optional, uses default if not provided)
        Returns:
            None
        """

        try:
            now = datetime.now(timezone.utc)
            user_details = self.get_user_details(user_id)
            # Add task to background tasks
            background_tasks.add_task(
                self.__process_batch_update,
                str(user_details.id),
                user_details.bucket_name,
                parser_type,
                parser_cloud,
                [record_id],
                record_id,   ## TODO passing record id as batch id when record is processed as standalone
                parser_model
            )
            return {"record_id": record_id, "status": "queued", "updated_at": now, "record_count": 1}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    async def update_user_records(self, user_id: UUID, record_id: str, batch_id:str, file_object, file_name: str, content_type: str, background_tasks: BackgroundTasks):
        """
        Queue the record update for background processing and return immediately.
        The actual processing happens in _process_record_update.
        Args:
            user_id (UUID): The user ID
            record_id (str): The record ID
            batch_id (str): The batch ID for the record
            file_object: The file object to be uploaded
            file_name (str): The name of the file
            content_type (str): The content type of the file
            background_tasks (BackgroundTasks): FastAPI BackgroundTasks instance
        """
        try:
            # Create initial record
            now = datetime.now(timezone.utc)
            user_details = self.get_user_details(user_id)
            # Add task to background tasks
            background_tasks.add_task(
                self.__process_record_update,
                user_details.id,
                record_id,
                batch_id,
                file_object,
                file_name,
                content_type
            )
            return {"id": record_id, "status": "queued", "updated_at": now}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def get_file_url(self, user_id: UUID, object_id: str, object_type:str, object_name: str) -> str:
        """
        get the pre-signed url to access the file by url
        Args:
            user_id (UUID): The user ID
            object_id (str): The object ID
            object_type (str): The type of the object (e.g., "record", "report")
            object_name (str): The name of the object
        Returns:
            str: The pre-signed URL to access the file
        """
        user_details = self.get_user_details(user_id)
        return self.storage_service.get_signed_url(str(user_details.id), object_id, object_type, object_name, user_details.bucket_name)

    def get_record_status(self, user_id: UUID, record_id: str) -> UserRecordRequestORM:
        """
        Get the status of a specific user record.
        Args:
            user_id (UUID): The user ID
            record_id (str): The record ID
        Returns:
            UserRecordRequestORM: The record status
        Raises:
            HTTPException: If the user is not found or an error occurs

        """
        user_details = self.get_user_details(user_id)
        with get_db_session(commit=False) as db:
            rec_status = db.query(UserRecordRequestORM).filter(
                    UserRecordRequestORM.user_id == str(user_details.id),
                    UserRecordRequestORM.id == record_id).first()
            if rec_status is None:
                raise HTTPException(status_code=404, detail="Record not found")
            if rec_status.status != "PENDING" and rec_status.file_name is not None:
                url = self.get_file_url(user_id, record_id, "record", rec_status.file_name)
                setattr(rec_status, 'url', url) # Use setattr for ORM attribute
            return rec_status
    
    async def get_user_records(self, user_id: UUID) -> List[N1RecordUpdateRequest]:
        """
        Get all records for a specific user

        Args:
            user_id (UUID): The user ID

        Returns:
            List[N1RecordUpdateRequest]: List of all records for the user

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                records = db.query(UserRecordRequestORM).filter_by(user_id=user_details.id).all()
                if not records:
                    # Return empty list if no records found
                    return []

                # Convert ORM objects to N1RecordUpdateRequest objects
                return [N1RecordUpdateRequest.model_validate(record) for record in records]
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching records for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user records: {str(e)}"
            )

    async def generate_new_report(self, request: N1ReportGenRequest):
        self.logger.info(f"Generating new report {request}")
        report_config = request.config.model_dump(mode='json') if request.config else None
        self.logger.info(f"Report config {report_config}")
        prompts = [prompt.model_dump(mode='json') for prompt in request.prompts] if request.prompts else None
        self.logger.info(f"Report prompts {prompts}")

        with get_db_session() as db:
            user_details = self.get_user_details(request.user_id)
            request_orm = UserReportGenRequestORM(
                id=request.id,
                user_id=str(user_details.id),
                progress=1,
                status="STARTING",
                url=None,
                workflow_name=request.workflow_name,
                workflow_id=request.workflow_id,
                version=request.version,
                report_language=request.report_language,
                file_name=request.config.report_name if request.config else None,
                config=report_config,  # Convert Pydantic model to dict for JSON serialization
                prompts=prompts,
                error=None,
                created_at=request.created_at,
                updated_at=request.updated_at
            )

            db.add(request_orm)

            updated_request = request.model_copy(update={"user_id": str(user_details.id)})
            operation_id = await launch_report_generation_job(updated_request, user_details.bucket_name)
            self.logger.info(f"Report generation job launched with operation ID {operation_id}")

    def get_report_status(self, user_id: UUID, report_id: str) -> N1ReportGenRequest:
        """
        Get the status of a report generation request

        Args:
            user_id (UUID): The user ID
            report_id (str): The report ID

        Returns:
            N1ReportGenRequest: The report generation request status
        """
        user_details = self.get_user_details(user_id)
        with get_db_session(commit=False) as db:
            self.logger.info(f"Getting report status for user {str(user_details.id)}, report {report_id}")
            report_status = db.query(UserReportGenRequestORM).filter_by(id=report_id, user_id=str(user_details.id)).first()
            if not report_status:
                raise HTTPException(status_code=404, detail="Report status not found")
            if report_status.config and isinstance(report_status.config, dict) and 'report_name' in report_status.config:
                url = self.get_file_url(user_id, report_id, "chr", f"{report_status.config['report_name']}.pdf")
                setattr(report_status, 'url', url) # Use setattr for ORM attribute
            # Create a dictionary from the ORM object
            report_dict = self.__get_report_status_dict(report_status)
            return N1ReportGenRequest.model_validate(report_dict)

    def __get_report_status_dict(self, report_status:UserReportGenRequestORM) -> dict:
        report_dict = {
            "id": report_status.id,
            "user_id": report_status.user_id,
            "progress": report_status.progress,
            "status": report_status.status,
            "url": report_status.url,
            "error": report_status.error,
            "created_at": report_status.created_at,
            "updated_at": report_status.updated_at
        }
        # Handle the config field separately - if it's a string, parse it as JSON
        if report_status.config is not None and isinstance(report_status.config, str):
            try:
                report_dict["config"] = json.loads(report_status.config)
            except json.JSONDecodeError:
                self.logger.error(f"Failed to parse config JSON: {report_status.config}")
                report_dict["config"] = "{}"
        else:
            report_dict["config"] = report_status.config
        return report_dict

    async def update_record_progress(self, update_request: RecordProgressUpdateRequest) -> N1RecordUpdateRequest:
        """
        Update the progress, status, and error of a record

        Args:
            update_request (RecordProgressUpdateRequest): The update request containing user_id, record_id, progress, status, and error

        Returns:
            N1RecordUpdateRequest: The updated record
        """
        try:
            user_details = self.get_user_details(update_request.user_id)
            with get_db_session() as db:
                # Get current record status
                record: UserRecordRequestORM = self.get_record_status(user_details.id, update_request.record_id)

                # Update the specific fields
                setattr(record, 'progress', update_request.progress)
                setattr(record, 'status', update_request.status)
                if update_request.version is not None:
                    setattr(record, 'version', update_request.version)
                if update_request.error is not None:
                    setattr(record, 'error', update_request.error)
                if update_request.message is not None:
                    setattr(record, 'message', update_request.message)

                # Update the timestamp
                setattr(record, 'updated_at', datetime.now(timezone.utc))

                # Save changes to database
                db.add(record)
                db.commit()
                db.refresh(record)

                # Convert ORM object to dict for logging
                record_status = N1RecordUpdateRequest.model_validate(record)
                self.logger.info(record_status.model_dump_json(indent=2))

                # Get a fresh copy of the record before closing the session
                record_id = str(record.id)

            # Broadcast WebSocket update
            try:
                await self.connection_manager.broadcast_update("update_record_progress", update_request.model_dump(mode='json'), str(update_request.user_id))
            except Exception as ws_error:
                self.logger.warning(f"Failed to broadcast WebSocket update: {ws_error}")
            # Call update_bubble_record_status after the session is closed
            await self.update_bubble_record_status(record_status)

            # Get a fresh copy of the record for return
            with get_db_session() as db:
                return self.get_record_status(user_details.id, record_id)

        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating record progress for user {update_request.user_id}, record {update_request.record_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating record progress: {str(e)}")

    async def update_report_progress(self, update_request: ReportProgressUpdateRequest) -> N1ReportGenRequest:
        """
        Update the progress, status, error, and message of a report

        Args:
            update_request (ReportProgressUpdateRequest): The update request containing user_id, report_id, progress, status, error, and message

        Returns:
            N1ReportGenRequest: The updated report
        """
        user_details = self.get_user_details(update_request.user_id)
        report_id = update_request.report_id
        user_id = str(user_details.id)
        self.logger.info(f"Updating report progress for user {user_id}, report {update_request.report_id}")
        try:
            with get_db_session() as db:
                # Get the ORM object from the database
                report_orm = db.query(UserReportGenRequestORM).filter_by(
                    id=report_id,
                    user_id=user_id
                ).first()

                if not report_orm:
                    raise HTTPException(status_code=404, detail="Report not found")

                # Update the specific fields
                setattr(report_orm, 'progress', update_request.progress)
                setattr(report_orm, 'status', update_request.status)
                setattr(report_orm, 'message', update_request.message)

                if update_request.error is not None:
                    setattr(report_orm, 'error', update_request.error)

                now = datetime.now(timezone.utc)

                # Update the timestamp
                setattr(report_orm, 'updated_at', now)

                # Create UserRequestStageORM
                request_stage_orm = UserRequestStageORM(
                    request_id=report_id,
                    status=update_request.status,
                    progress=update_request.progress,
                    details=update_request.message,
                    error=update_request.error,
                    created_at=now,
                    updated_at=now
                )
                # Add both objects to the session
                db.add(request_stage_orm)
                db.add(report_orm)
                # Commit once for both objects
                db.commit()
                db.refresh(report_orm)

                # Create a dictionary from the ORM object
                report_dict = self.__get_report_status_dict(report_orm)

                # Store the refreshed data in a new variable before closing session
                refreshed_report = N1ReportGenRequest.model_validate(report_dict)

                # Call update_bubble_report_status
                await self.update_bubble_report_status(
                    user_id,
                    report_id,
                    request_stage_orm.status,
                    request_stage_orm.progress,
                    request_stage_orm.details,
                    request_stage_orm.created_at,
                    request_stage_orm.updated_at
                )

                # Broadcast WebSocket update
                try:
                    await self.connection_manager.broadcast_update("update_report_progress", update_request.model_dump(mode='json'), user_id)
                except Exception as ws_error:
                    self.logger.warning(f"Failed to broadcast WebSocket update: {ws_error}")

                # Return the updated report
                return refreshed_report
        except Exception as e:
            self.logger.error(f"Error updating report progress for user {update_request.user_id}, report {update_request.report_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating report progress: {str(e)}")


    async def update_clinical_data(self, user_id: UUID, record_id: str, biomarker_id: UUID, update_data: dict) -> ClinicalData:
        """
        Update a clinical data record for a user

        Args:
            user_id (UUID): The user ID
            record_id (str): The record ID
            biomarker_id (str): The clinical data ID
            update_data (dict): The data to update

        Returns:
            ClinicalData: The updated clinical data record
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the clinical data record
                clinical_data = db.query(ClinicalDataORM).filter(
                    ClinicalDataORM.user_id == str(user_details.id),
                    ClinicalDataORM.id == biomarker_id,
                    ClinicalDataORM.record_id == record_id
                ).first()

                if not clinical_data:
                    raise HTTPException(status_code=404, detail=f"Clinical data record not found for user {str(user_details.id)}, record {record_id}, and id {biomarker_id}")

                # Update the fields
                for key, value in update_data.items():
                    if hasattr(clinical_data, key) and value is not None:
                        setattr(clinical_data, key, value)

                # Set the edited flag to true
                setattr(clinical_data, 'edited', True)

                # Update the updated_at timestamp
                setattr(clinical_data, 'updated_at',datetime.now(timezone.utc))

                # Commit the changes
                db.add(clinical_data)
                db.commit()
                db.refresh(clinical_data)

                # Convert ORM object to Pydantic model
                return ClinicalData.model_validate(clinical_data)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating clinical data for user {user_id}, record {record_id}, and id {biomarker_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating clinical data: {str(e)}")


    async def get_user_biomarkers_response(self, user_id: UUID, record_id: Optional[str] = None) -> BiomarkerResponse:
        """
        Get all biomarker records for a specific user

        Args:
            user_id (UUID): The user ID
            record_id (Optional[str]): The record ID to filter by (optional)
        Returns:
            BiomarkerResponse: Response object containing the status, user ID, fields, and data
        Raises:
            HTTPException: If the user is not found or an error occurs

        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                query = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.user_id == user_details.id,
                    UserBiomarkerORM.excluded == False  # Only include records that are not excluded
                )
                if record_id:
                    query: Query[UserBiomarkerORM] = query.filter(UserBiomarkerORM.record_id == record_id)
                orm_records: List[UserBiomarkerORM] = query.all()

                if not orm_records:
                    records: List[UserBiomarkerBase] = []
                else:
                    # Convert ORM objects to Pydantic models
                    records: List[UserBiomarkerBase] = [UserBiomarkerBase.model_validate(record) for record in orm_records]

                return BiomarkerResponse(
                    status="success",
                    user_id=user_details.id,
                    fields=list(UserBiomarkerBase.model_fields.keys()),
                    data=records

                )
        except Exception as e:
            self.logger.error(f"Error fetching biomarkers for user {user_id}: {str(e)}")
            raise

    async def get_user_procedures_response(self, user_id: UUID, record_id: Optional[str] = None) -> ProcedureResponse:
        """
        Get all procedure records for a specific user
        Args:
            user_id (UUID): The user ID
            record_id (Optional[str]): The record ID to filter by (optional)
        Returns:
            ProcedureResponse: Response object containing the status, user ID, fields, and data
        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                query = db.query(UserProcedureORM).filter(
                    UserProcedureORM.user_id == str(user_details.id),
                )
                if record_id:
                    query: Query[UserProcedureORM] = query.filter(UserProcedureORM.record_id == record_id)
                orm_records: List[UserProcedureORM] = query.all()

                if not orm_records:
                    records: List[UserProcedure] = []
                else:
                    # Convert ORM objects to Pydantic models
                    records: List[UserProcedure] = [UserProcedure.model_validate(record) for record in orm_records]

                return ProcedureResponse(
                    status="success",
                    user_id=str(user_details.id),
                    fields=list(UserProcedure.model_fields.keys()),
                    data=records
                )
        except Exception as e:
            self.logger.error(f"Error fetching procedures for user {user_id}: {str(e)}")
            raise

    async def get_user_diagnosis_response(self, user_id: UUID, record_id: Optional[str] = None) -> DiagnosisResponse:
        """
        Get all diagnosis records for a specific user

        Args:
            user_id (UUID): The user ID
            record_id (Optional[str]): The record ID to filter by (optional)
        Returns:
            DiagnosisResponse: Response object containing the status, user ID, fields, and data
        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                query = db.query(UserDiagnosisORM).filter(
                    UserDiagnosisORM.user_id == str(user_details.id),
                )
                if record_id:
                    query: Query[UserDiagnosisORM] = query.filter(UserDiagnosisORM.record_id == record_id)
                orm_records: List[UserDiagnosisORM] = query.all()

                if not orm_records:
                    records: List[UserDiagnosis] = []
                else:
                    # Convert ORM objects to Pydantic models
                    records: List[UserDiagnosis] = [UserDiagnosis.model_validate(record) for record in orm_records]

                return DiagnosisResponse(
                    status="success",
                    user_id=str(user_details.id),
                    fields=list(UserDiagnosis.model_fields.keys()),
                    data=records
                )
        except Exception as e:
            self.logger.error(f"Error fetching biomarkers for user {user_id}: {str(e)}")
            raise

    def __delete_user_data_from_table(self, user_id: str, table_name: str) -> int:
        """
        Delete all user data from a specific table

        Args:
            user_id (str): The user ID
            table_name (str): The name of the table to delete data from

        Returns:
            int: The number of rows deleted
        """
        with get_db_session() as db:
            delete_query = text(f"DELETE FROM {table_name} WHERE user_id = :user_id")
            result = db.execute(delete_query, {"user_id": str(user_id)})
            db.commit()
            return result.rowcount # type: ignore


    def delete_user_data(self, user_id: UUID) -> UserDeletionResponse:
        """
        Delete all user data from database and storage
        Args:
            user_id (UUID): The user ID
        Returns:
            UserDeletionResponse: Response object containing deletion status and counts
        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            found_user_id = str(user_details.id)
            # Delete database records
            # self.__delete_user_data_from_table(found_user_id, UserRequestStageORM.__table__.name) TODO: remove this table using foreign key constraints
            self.__delete_user_data_from_table(found_user_id, UserReportGenRequestORM.__table__.name)
            self.__delete_user_data_from_table(found_user_id, UserQuestionResponseORM.__table__.name)
            self.__delete_user_data_from_table(found_user_id, UserRecordRequestORM.__table__.name)
            self.__delete_user_data_from_table(found_user_id, CanonicalRecordORM.__table__.name)
            self.__delete_user_data_from_table(found_user_id, UserMedicationORM.__table__.name)

            file_count = self.storage_service.delete_user_files(str(user_details.id), user_details.bucket_name)

            self.logger.info(f"Successfully deleted all data for user {user_id}")

            return UserDeletionResponse(
                status="success",
                user_id=str(user_details.id),
                database=DBRowDeletionStatus(
                    records_deleted=True,
                    reports_deleted=True
                ),
                files_deleted=file_count
            )
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting data for user {user_id}: {str(e)}")
            return UserDeletionResponse(
                status="error",
                user_id=str(user_id),
                message=str(e)
            )

    def delete_record(self, user_id: UUID, record_id: str) -> dict:
        """
        Delete a record and all associated clinical data

        Args:
            user_id (UUID): The user ID
            record_id (str): The record ID

        Returns:
            dict: A dictionary with the status and counts of deleted records

        Raises:
            HTTPException: If the record is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the record
                record = db.query(UserRecordRequestORM).filter(
                    UserRecordRequestORM.user_id == str(user_details.id),
                    UserRecordRequestORM.id == record_id
                ).first()

                if not record:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Record not found for user {str(user_details.id)} and record {record_id}"
                    )

                # Store the file name before deleting the record
                file_name = record.file_name

                # Delete all associated clinical data
                clinical_data_deleted = db.query(ClinicalDataORM).filter(
                    ClinicalDataORM.user_id == str(user_details.id),
                    ClinicalDataORM.record_id == record_id
                ).delete()

                # Delete the record from the database
                db.delete(record)
                db.commit()

                # Delete the file from cloud storage
                file_deleted = False
                if file_name:
                    file_deleted = self.storage_service.delete_record_file(str(user_details.id), record_id, str(file_name), user_details.bucket_name)

                self.logger.info(f"Successfully deleted record {record_id} for user {user_details.id}, {clinical_data_deleted} associated clinical data records, and file {file_name} (deleted: {file_deleted})")

                return {
                    "status": "success",
                    "message": f"Record {record_id} and all associated clinical data deleted successfully",
                    "record_deleted": True,
                    "clinical_data_deleted_count": clinical_data_deleted,
                    "file_deleted": file_deleted
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting record {record_id} for user {user_details.id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting record: {str(e)}"
            )

    async def update_bubble_record_status(self, record_status: N1RecordUpdateRequest) -> dict:

                                        #   user_id: str, record_id: str, status: str, progress: int,
                                        #  message: str, created_at: datetime, updated_at: datetime) -> dict:
        """
        Post record status update to Bubble API

        Args:
            record_status (N1RecordUpdateRequest): Latest RFecord Status object from the db:



        Returns:
            dict: Response from the Bubble API
        """

        try:
            payload = {
                "user_id": str(record_status.user_id),
                "record_id": record_status.id,
                "status": record_status.status,
                "progress": record_status.progress,
                "message": record_status.error if record_status.error else record_status.message,
                "created_at": record_status.created_at.isoformat() if record_status.created_at else datetime.now(timezone.utc).isoformat(),
                "updated_at": record_status.updated_at.isoformat() if record_status.updated_at else datetime.now(timezone.utc).isoformat()
            }

            if config.SKIP_BUBBLE_UPDATES:
                self.logger.info(f"Skipping Bubble API update in update_bubble_record_status for user:  {str(record_status.user_id)}, record: {record_status.id} progress {record_status.progress}, status {record_status.status}")
                return {"status": "success", "message": "Bubble API update skipped"}

            # Check if the user is a test user
            self.logger.info(f"Posting record status update to Bubble API for user: {record_status.user_id}, record: {record_status.id}")

            response = await self.__post_data_to_api(config.BUBBLE_RECORD_STATUS_URL, data=payload, headers=BUBBLE_API_HEADERS)
            return {"status": "success", "response": response}

        except Exception as e:
            self.logger.error(f"Error posting record status to Bubble API: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def update_bubble_report_status(self, user_id: str, report_id: str, status: str, progress: int,
                                        message: str, created_at: datetime, updated_at: datetime) -> dict:
        """
        Post report status update to Bubble API

        Args:
            user_id (str): User identifier
            report_id (str): Report identifier
            status (str): Status of the report (e.g., "IN_PROGRESS", "COMPLETED")
            progress (int): Progress value between 0 and 100
            message (str): Status message or description
            created_at (datetime): Creation timestamp
            updated_at (datetime): Update timestamp

        Returns:
            dict: Response from the Bubble API
        """
        try:
            payload = {
                "user_id": user_id,
                "report_id": report_id,
                "status": status,
                "progress": progress,
                "message": message,
                "created_at": created_at.isoformat(),
                "updated_at": updated_at.isoformat()
            }

            if config.SKIP_BUBBLE_UPDATES:
                self.logger.info(f"Skipping Bubble API update in update_bubble_report_status for user: {user_id}, report: {report_id} progress {progress}, status {status}")
                return {"status": "success", "message": "Bubble API update skipped"}

            self.logger.info(f"Posting report status update to Bubble API for user: {user_id}, report: {report_id}")

            response = await self.__post_data_to_api(config.BUBBLE_REPORT_STATUS_URL, data=payload, headers=BUBBLE_API_HEADERS)
            return response # Ensure the response from the API call is returned

        except Exception as e:
            self.logger.error(f"Error posting report status to Bubble API: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def sync_biomarkers_to_bubble(self, user_id: UUID, record_id: str) -> dict:
        """
        Sync biomarkers for a record to Bubble API

        Args:
            user_id (UUID): The user ID
            record_id (str): The record ID

        Returns:
            dict: Response from the Bubble API
        """
        try:
            user_details = self.get_user_details(user_id)
            if not user_details:
                raise HTTPException(status_code=404, detail="User not found")
            # First update the record status
            update_request = RecordProgressUpdateRequest(
                user_id=user_details.id,
                record_id=record_id,
                 progress=98,
                message="Biomarkers synced",
                status="BIOMARKERS_SYNCED",
                error=None,
                version=None
            )
            data = await self.update_record_progress(update_request)

            return {
                "status": "success",
                "message": f"Data for record {record_id} synced to Bubble API",
                "response": data
            }
        except Exception as e:
            self.logger.error(f"Error syncing data to Bubble API: {str(e)}")
            raise

    def get_loinc_record(self, loinc_num: str) -> LoincRecordSchema:
        """
        Get a LOINC record by its code

        Args:
            loinc_num (str): The LOINC code to look up

        Returns:
            LoincRecordSchema: The LOINC record

        Raises:
            HTTPException: If the LOINC record is not found or an error occurs
        """
        loinc_record = self.loinc_short_map.get(loinc_num)

        if not loinc_record:
            raise HTTPException(
                status_code=404,
                detail=f"LOINC record not found for code {loinc_num}"
            )
        return loinc_record

    def get_canonical_record(self, canonical_name: str) -> CanonicalBiomarkerBase:
        """
        Get a Canonical record by its name

        Args:
            canonical_name (str): The canonical code to look up

        Returns:
            CanonicalBiomarkerBase: The canonical record

        Raises:
            HTTPException: If the canonical record is not found or an error occurs
        """
        canonical_record = self.canonical_short_map.get(canonical_name)

        if not canonical_record:
            raise HTTPException(
                status_code=404,
                detail=f"Canonical record not found for code {canonical_name}"
            )
        return canonical_record

    def delete_report(self, user_id: UUID, report_id: str) -> dict:
        """
        Delete a report and its associated file from cloud storage

        Args:
            user_id (UUID): The user ID
            report_id (str): The report ID

        Returns:
            dict: A dictionary with the status and details of the deletion

        Raises:
            HTTPException: If the report is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the report
                report = db.query(UserReportGenRequestORM).filter(
                    UserReportGenRequestORM.user_id == str(user_details.id),
                    UserReportGenRequestORM.id == report_id
                ).first()

                if not report:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Report not found for user {str(user_details.id)} and report {report_id}"
                    )

                # Store the file name before deleting the report
                file_name = f"{report.file_name}.pdf" if report.file_name else None

                # Delete the report request from the database
                db.delete(report)
                db.commit()

                # Delete the file from cloud storage
                file_deleted = False
                if file_name:
                    file_deleted = self.storage_service.delete_report_file(str(user_details.id), report_id, file_name, user_details.bucket_name)

                self.logger.info(f"Successfully deleted report {report_id} for user {user_details.id} and file {file_name} (deleted: {file_deleted})")

                return {
                    "status": "success",
                    "message": f"Report {report_id} deleted successfully",
                    "report_deleted": True,
                    "file_deleted": file_deleted
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting report {report_id} for user {user_details.id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting report: {str(e)}"
            )

    def search_loinc_by_shortname(self, search_term: str, limit: int = 10) -> List[LoincRecordSchema]:
        """
        Search for LOINC records by shortname using PostgreSQL trigram similarity

        Args:
            search_term (str): The search term to match against shortname
            limit (int): Maximum number of results to return (default: 10)

        Returns:
            List[LoincRecordSchema]: List of matching LOINC records with similarity scores

        Raises:
            HTTPException: If an error occurs during the search
        """
        try:
            # Use PostgreSQL's trigram similarity (%) operator for fuzzy matching
            # This requires the pg_trgm extension to be enabled in the database
            # CREATE EXTENSION IF NOT EXISTS pg_trgm;

            # Use raw SQL to get both the records and similarity scores
            query = text("""
                SELECT *, similarity(shortname, :search_term) AS similarity_score
                FROM loinc_records
                WHERE shortname % :search_term
                ORDER BY shortname <-> :search_term
                LIMIT :limit
            """)
            with get_db_session(commit=False) as db:
                result = db.execute(query, {"search_term": search_term, "limit": limit})
                records = result.fetchall()

                if not records:
                    return []

                # Convert result rows to Pydantic models with similarity scores
                loinc_records = []
                for record in records:
                    # Convert row to dict
                    record_dict = {column: value for column, value in zip(result.keys(), record)}
                    # Create LoincRecordSchema with similarity score
                    loinc_record = LoincRecordSchema.model_validate(record_dict)
                    loinc_records.append(loinc_record)

                return loinc_records
        except Exception as e:
            self.logger.error(f"Error searching LOINC records by shortname '{search_term}': {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error searching LOINC records: {str(e)}"
            )

    def search_canonical_by_shortname(self, search_term: str, limit: int = 10) -> List[CanonicalBiomarkerBase]:
        """
        Search for Canonical records by shortname using PostgreSQL trigram similarity

        Args:
            search_term (str): The search term to match against shortname
            limit (int): Maximum number of results to return (default: 10)

        Returns:
            List[CanonicalBiomarkerBase]: List of matching Canonical records with similarity scores

        Raises:
            HTTPException: If an error occurs during the search
        """
        try:
            # Use PostgreSQL's trigram similarity (%) operator for fuzzy matching
            # This requires the pg_trgm extension to be enabled in the database
            # CREATE EXTENSION IF NOT EXISTS pg_trgm;

            # Use raw SQL to get both the records and similarity scores
            query = text("""
                SELECT *, similarity(shortname, :search_term) AS similarity_score
                FROM canonical_biomarkers
                WHERE shortname % :search_term
                ORDER BY shortname <-> :search_term
                LIMIT :limit
            """)
            with get_db_session(commit=False) as db:
                result = db.execute(query, {"search_term": search_term, "limit": limit})
                records = result.fetchall()

                if not records:
                    return []

                # Convert result rows to Pydantic models with similarity scores
                canonical_records = []
                for record in records:
                    # Convert row to dict
                    record_dict = {column: value for column, value in zip(result.keys(), record)}
                    # Create CanonicalBiomarkerBase with similarity score
                    canonical_record = CanonicalBiomarkerBase.model_validate(record_dict)
                    canonical_records.append(canonical_record)

                return canonical_records
        except Exception as e:
            self.logger.error(f"Error searching Canonical records by shortname '{search_term}': {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error searching Canonical records: {str(e)}"
            )

    def search_loinc_by_component(self, search_term: str, limit: int = 10) -> List[LoincRecordSchema]:
        """
        Search for LOINC records by component using PostgreSQL trigram similarity

        Args:
            search_term (str): The search term to match against component
            limit (int): Maximum number of results to return (default: 10)

        Returns:
            List[LoincRecordSchema]: List of matching LOINC records with similarity scores

        Raises:
            HTTPException: If an error occurs during the search
        """
        try:
            # Use PostgreSQL's trigram similarity (%) operator for fuzzy matching
            # This requires the pg_trgm extension to be enabled in the database

            # Use raw SQL to get both the records and similarity scores
            query = text("""
                SELECT *, similarity(component, :search_term) AS similarity_score
                FROM loinc_records
                WHERE component % :search_term
                ORDER BY component <-> :search_term
                LIMIT :limit
            """)
            with get_db_session(commit=False) as db:
                result = db.execute(query, {"search_term": search_term, "limit": limit})
                records = result.fetchall()

                if not records:
                    return []

                # Convert result rows to Pydantic models with similarity scores
                loinc_records = []
                for record in records:
                    # Convert row to dict
                    record_dict = {column: value for column, value in zip(result.keys(), record)}
                    # Create LoincRecordSchema with similarity score
                    loinc_record = LoincRecordSchema.model_validate(record_dict)
                    loinc_records.append(loinc_record)

                return loinc_records
        except Exception as e:
            self.logger.error(f"Error searching LOINC records by component '{search_term}': {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error searching LOINC records: {str(e)}"
            )

    def search_canonical_by_component(self, search_term: str, limit: int = 10) -> List[CanonicalBiomarkerBase]:
        """
        Search for Canonical records by component using PostgreSQL trigram similarity

        Args:
            search_term (str): The search term to match against component
            limit (int): Maximum number of results to return (default: 10)

        Returns:
            List[CanonicalBiomarkerBase]: List of matching Canonical records with similarity scores

        Raises:
            HTTPException: If an error occurs during the search
        """
        try:
            # Use PostgreSQL's trigram similarity (%) operator for fuzzy matching
            # This requires the pg_trgm extension to be enabled in the database

            # Use raw SQL to get both the records and similarity scores
            query = text("""
                SELECT *, similarity(component, :search_term) AS similarity_score
                FROM canonical_records
                WHERE component % :search_term
                ORDER BY component <-> :search_term
                LIMIT :limit
            """)
            with get_db_session(commit=False) as db:
                result = db.execute(query, {"search_term": search_term, "limit": limit})
                records = result.fetchall()

                if not records:
                    return []

                # Convert result rows to Pydantic models with similarity scores
                canonical_records = []
                for record in records:
                    # Convert row to dict
                    record_dict = {column: value for column, value in zip(result.keys(), record)}
                    # Create CanonicalBiomarkerBase with similarity score
                    canonical_record = CanonicalBiomarkerBase.model_validate(record_dict)
                    canonical_records.append(canonical_record)

                return canonical_records
        except Exception as e:
            self.logger.error(f"Error searching Canonical records by component '{search_term}': {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error searching Canonical records: {str(e)}"
            )

    async def delete_biomarker(self, user_id: UUID, record_id: str, biomarker_id: UUID) -> ClinicalDataDeleted:
        """
        Delete a biomarker record for a user (soft delete)

        Args:
            user_id (UUID): The user ID
            record_id (str): The record ID
            clinical_data_id (UUID): The clinical data ID

        Returns:
            ClinicalData: The updated clinical data record

        Raises:
            HTTPException: If the clinical data record is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the clinical data record
                clinical_data = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.user_id == user_details.id,
                    UserBiomarkerORM.id == biomarker_id,
                    UserBiomarkerORM.record_id == record_id
                ).first()

                if not clinical_data:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Clinical data record not found for user {str(user_details.id)}, record {record_id}, and id {biomarker_id}"
                    )

                # Set the excluded flag to true
                setattr(clinical_data, 'excluded', True)
                setattr(clinical_data, 'updated_at', datetime.now(timezone.utc))

                # Commit the changes
                db.add(clinical_data)
                db.commit()
                db.refresh(clinical_data)

                # Convert ORM object to Pydantic model
                return ClinicalDataDeleted(
                    biomarker_id=clinical_data.id,
                    user_id=clinical_data.user_id,
                    record_id=clinical_data.record_id
                )
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting biomarker record for user {str(user_details.id)}, record {record_id}, and id {biomarker_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting biomarker record: {str(e)}"
            )

    async def register_user(self, user_registration: UserRegistration) -> User:
        """
        Register a new user in the system

        Args:
            user_registration (UserRegistration): The user registration data

        Returns:
            User: The created user

        Raises:
            HTTPException: If a user with the same bubble_id already exists
            or if a user with the same email and develop_mode already exists
        """
        try:
            with get_db_session() as db:
                # Check if user with same bubble_id exists
                existing_user_bubble = db.query(UserORM).filter(
                    UserORM.bubble_id == user_registration.bubble_id
                ).first()

                if existing_user_bubble:
                    raise HTTPException(
                        status_code=409,
                        detail="User with this bubble_id already exists"
                    )

                # Check if user with same email and develop_mode exists
                existing_user_email = db.query(UserORM).filter(
                    (UserORM.email == user_registration.email) &
                    (UserORM.develop_mode == user_registration.develop_mode)
                ).first()

                if existing_user_email:
                    raise HTTPException(
                        status_code=409,
                        detail=f"User with this email already exists in {'develop' if user_registration.develop_mode else 'production'} mode"
                    )

                # If develop_mode is true, use config.DEV_BUCKET_NAME, otherwise create a new bucket
                if user_registration.develop_mode:
                    # For development mode, we'll still use config.DEV_BUCKET_NAME as it's a shared bucket for all dev users
                    bucket_name = config.DEV_BUCKET_NAME
                else:
                    bucket_name = self.storage_service.create_new_bucket()

                # Create a new user
                new_user = UserORM(**user_registration.model_dump(), bucket_name=bucket_name)

                db.add(new_user)
                db.flush()
                db.commit()
                db.refresh(new_user)
                return User.model_validate(new_user)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error registering user: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error registering user: {str(e)}"
            )

    @cached(USER_CACHE)
    def get_user_details(self, user_id: UUID) -> UserDetails:
        """
        Get user details from the database with LRU caching to avoid frequent database hits, falls back to search by bubble id to help transition

        Args:
            user_id (UUID | str): The user ID to look up

        Returns:
            User: The user details

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            # Convert string to UUID if needed
            if isinstance(user_id, str):
                user_id = UUID(user_id)

            with get_db_session(commit=False) as db:
                user_orm = db.query(UserORM).filter(UserORM.id == user_id).first()
                if not user_orm:
                    raise HTTPException(
                        status_code=404,
                        detail=f"User not found with ID {user_id}"
                    )

                user = User.model_validate(user_orm)
                user_details = self.__get_user_details_with_profile(user)
                self.logger.info(f"User {user_id} details {user_details} retrieved from database and cached")
                return user_details
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching user details for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user details: {str(e)}"
            )

    def clear_user_cache(self, user_id: Optional[UUID] = None) -> None:
        """
        Clear the user details cache

        Args:
            user_id (Optional[UUID]): If provided, clear cache for specific user.
                                    If None, clear entire cache.
        """
        if user_id:
            # Clear specific user from cache if it exists
            USER_CACHE.pop(hashkey(user_id), None)
            self.logger.info(f"Cache cleared for user {user_id}")
        else:
            # Clear entire cache
            USER_CACHE.clear()
            self.logger.info("Entire user cache cleared")

    def __get_user_details_with_profile(self, user: User) -> UserDetails:
        """
        Get user details with profile information from the User Pydantic model

        Args:
            user (User): The user Pydantic model

        Returns:
            UserDetails: The user details
        """
        with get_db_session(commit=False) as db:
            # Fetch user profile using user.id - ensure proper UUID handling
            user_profile = db.query(UserProfileORM).filter(UserProfileORM.user_id == str(user.id)).first()
            return UserDetails(**user.model_dump(exclude_none=True), profile=user_profile)

    def get_user_by_email(self, email: str, develop_mode=False) -> UserDetails:
        """
        Get user details by email address

        Args:
            email (str): The email address to look up

        Returns:
            User: The user details

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            with get_db_session(commit=False) as db:
                self.logger.info(f"Looking up user by email: {email} with develop_mode: {develop_mode}")
                # Find the user by email and develop_mode
                user_orm = db.query(UserORM).filter(UserORM.email == email, UserORM.develop_mode == develop_mode).first()

                if not user_orm:
                    raise HTTPException(
                        status_code=404,
                        detail=f"User not found with email {email} and develop mode {develop_mode}"
                    )

                # Convert ORM object to Pydantic model
                user = User.model_validate(user_orm)
                user_details = self.__get_user_details_with_profile(user)
                self.logger.info(f"User with email {email} found details {user_details}")
                return user_details
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching user details for email {email}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user details: {str(e)}"
            )

    async def cancel_batch(self, user_id: UUID, batch_id: str) -> Dict[str, any]:
        """
        Cancel a batch of records by deleting all records associated with the batch

        Args:
            user_id (UUID): The user ID
            batch_id (str): The batch ID

        Returns:
            Dict[str, any]: Dictionary containing status and counts of deleted records

        Raises:
            HTTPException: If an error occurs during batch cancellation
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                # Find all records for the user and batch_id
                records = db.query(UserRecordRequestORM).filter(
                    UserRecordRequestORM.user_id == str(user_details.id),
                    UserRecordRequestORM.batch_id == batch_id
                ).all()

                if not records:
                    return {
                        "status": "success",
                        "message": f"No records found for batch {batch_id}",
                        "records_deleted": 0
                    }

                record_ids: list[str] = [str(record.id) for record in records]
                self.logger.info(f"Found {len(record_ids)} records to cancel for user {user_details.id} and batch {batch_id}")

            # Delete each record
            deleted_count = 0
            failed_records = []

            for record_id in record_ids:
                try:
                    self.delete_record(str(user_details.id), record_id)
                    deleted_count += 1
                except Exception as e:
                    self.logger.error(f"Error deleting record {record_id}: {str(e)}")
                    failed_records.append({"record_id": record_id, "error": str(e)})

            return {
                "status": "success",
                "message": f"Batch {batch_id} cancelled successfully",
                "total_records": len(record_ids),
                "records_deleted": deleted_count,
                "failed_records": failed_records
            }
        except Exception as e:
            self.logger.error(f"Error cancelling batch {batch_id} for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error cancelling batch: {str(e)}"
            )

    async def get_user_stats(self, user_id: UUID) -> UserStats:
        """
        Get counts of report requests, record requests, biomarkers, medications, procedures, diagnoses, and genetics for a user

        Args:
            user_id (UUID): The user ID

        Returns:
            Dict[str, int]: Dictionary containing counts of report requests, record requests, biomarkers, medications, procedures, diagnoses, and genetics

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                # Count report requests
                report_count = db.query(UserReportGenRequestORM).filter_by(user_id=str(user_details.id)).count()

                # Count record requests
                record_count = db.query(UserRecordRequestORM).filter_by(user_id=str(user_details.id)).count()

                # Count biomarkers (clinical data)
                biomarker_count = db.query(UserBiomarkerORM).filter_by(
                    user_id=str(user_details.id),
                    excluded=False  # Only count non-excluded biomarkers
                ).count()

                # Count medications
                medication_count = db.query(UserMedicationORM).filter_by(user_id=str(user_details.id)).count()

                # Count procedures
                procedure_count = db.query(UserProcedureORM).filter_by(user_id=str(user_details.id)).count()

                # Count diagnoses
                diagnosis_count = db.query(UserDiagnosisORM).filter_by(user_id=str(user_details.id)).count()

                # Count genetics
                genetics_count = db.query(UserGeneticsORM).filter_by(user_id=str(user_details.id)).count()

                return UserStats(
                    report_count = report_count,
                    record_count = record_count,
                    biomarker_count = biomarker_count,
                    medication_count = medication_count,
                    procedure_count = procedure_count,
                    diagnosis_count = diagnosis_count,
                    genetics_count = genetics_count
                )
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error getting user stats for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error getting user stats: {str(e)}"
            )


    async def save_question_response(self, question: QuestionResponse) -> QuestionResponse:
        """
        Save a health question-response from the user

        Args:
            question (QuestionRequest): The question data to save

        Returns:
            QuestionResponse: The saved question response with ID and timestamps

        Raises:
            HTTPException: If an error occurs during saving
        """
        try:
            if isinstance(question.created_date, int):
                question.created_date = datetime.fromtimestamp(question.created_date / 1000, tz = timezone.utc)
            if question.related_date is not None and isinstance(question.related_date, int):
                question.related_date = datetime.fromtimestamp(question.related_date / 1000, tz = timezone.utc)

            user_details = self.get_user_details(question.user_id)
            with get_db_session() as db:
                # Create new question-answer record
                question_orm = UserQuestionResponseORM(**question.model_dump(exclude_unset=True))

                # Add to database and commit
                db.add(question_orm)
                db.commit()
                db.refresh(question_orm)

                # Return as Pydantic model
            return QuestionResponse.model_validate(question_orm)

        except Exception as e:
            self.logger.error(f"Error saving question for user {question.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error saving question: {str(e)}"
            )

    async def update_user_profile(self, profile: UserProfile) -> UserProfile:
        """
        Update user profile details - creates new profile if none exists, updates existing one otherwise

        Args:
            profile (UserProfile): The user profile data to update

        Returns:
            UserProfile: The updated user profile

        Raises:
            HTTPException: If an error occurs during updating
        """
        try:
            user_details = self.get_user_details(profile.user_id)
            with get_db_session() as db:
                # Check if user profile already exists
                existing_profile = db.query(UserProfileORM).filter(
                    UserProfileORM.user_id == str(user_details.id)
                ).first()

                if existing_profile:
                    # Update existing profile
                    update_data = profile.model_dump(exclude_unset=True)
                    for key, value in update_data.items():
                        if hasattr(existing_profile, key) and value is not None:
                            setattr(existing_profile, key, value)

                    # Update the updated_at timestamp
                    setattr(existing_profile, 'updated_at', datetime.now(timezone.utc))

                    db.add(existing_profile)
                    db.commit()
                    db.refresh(existing_profile)
                    user_profile_orm = existing_profile
                else:
                    # Create new user profile
                    user_profile_orm = UserProfileORM(**profile.model_dump(exclude_unset=True))
                    db.add(user_profile_orm)
                    db.commit()
                    db.refresh(user_profile_orm)

                # Clear user cache after successful profile update
                self.clear_user_cache(profile.user_id)

                return UserProfile.model_validate(user_profile_orm)

        except Exception as e:
            self.logger.error(f"Error updating profile for user id:{profile.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating profile : {str(e)}"
            )

    def delete_bucket(self, bucket_name: str) -> bool:
        """
        Delete a storage bucket

        Args:
            bucket_name (str): The name of the bucket to delete

        Returns:
            bool: True if the bucket was deleted successfully, False otherwise
        """
        try:
            # Call the storage service to delete the bucket
            return self.storage_service.delete_bucket(bucket_name)
        except Exception as e:
            self.logger.error(f"Error deleting bucket {bucket_name}: {str(e)}")
            return False

    def delete_user(self, user_id) -> bool:
        """Delete the user from the database"""
        self.__delete_user_data_from_table(user_id, UserProfileORM.__table__.name)
        with get_db_session() as db:
            user = db.query(UserORM).filter(UserORM.id == user_id).first()
            if user:
                db.delete(user)
                db.commit()
                return True
            else:
                return False

    async def create_medication(self, medication: UserMedicationCreate) -> UserMedication:
        """
        Create a new medication record for a user

        Args:
            medication (UserMedicationCreate): The medication data to create

        Returns:
            UserUserMedication: The created medication record

        Raises:
            HTTPException: If an error occurs during creation
        """
        try:
            user_details = self.get_user_details(medication.user_id)
            with get_db_session() as db:
                # Create new medication record
                medication_orm = UserMedicationORM(**medication.model_dump(exclude_unset=True))

                # Add to database and commit
                db.add(medication_orm)
                db.commit()
                db.refresh(medication_orm)

                # Return as Pydantic model
                return UserMedication.model_validate(medication_orm)
        except Exception as e:
            self.logger.error(f"Error creating medication for user {medication.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating medication: {str(e)}"
            )

    async def get_user_medications(self, user_id: UUID) -> List[UserMedication]:
        """
        Get all medications for a specific user

        Args:
            user_id (UUID): The user ID

        Returns:
            List[UserUserMedication]: List of all medications for the user

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                medications = db.query(UserMedicationORM).filter_by(user_id=str(user_details.id)).all()

                if not medications:
                    # Return empty list if no medications found
                    return []

                # Convert ORM objects to Medication objects
                return [UserMedication.model_validate(med) for med in medications]
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching medications for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user medications: {str(e)}"
            )

    async def get_user_medications_paginated(self, query_params: MedicationQueryParams) -> PaginatedMedicationResponse:
        """
        Get paginated, sorted, and filtered medications for a specific user

        Args:
            user_id (str): The user ID
            query_params (MedicationQueryParams): Query parameters for pagination, sorting, and filtering

        Returns:
            PaginatedMedicationResponse: Paginated response with medications data

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(query_params.user_id)
            config = get_medication_pagination_config()
            return await PaginationHelper.get_paginated_results(user_details, query_params, config)

        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching paginated medications for user {query_params.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated user medications: {str(e)}"
            )

    async def get_medication(self, user_id: UUID, medication_id: str) -> UserMedication:
        """
        Get a specific medication for a user

        Args:
            user_id (UUID): The user ID
            medication_id (UUID): The medication ID

        Returns:
            UserUserMedication: The medication record

        Raises:
            HTTPException: If the medication is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                medication = db.query(UserMedicationORM).filter_by(
                    user_id=str(user_details.id),
                    id=medication_id
                ).first()

                if not medication:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Medication not found for user {str(user_details.id)} and medication ID {medication_id}"
                    )

                # Convert ORM object to Pydantic model
                return UserMedication.model_validate(medication)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching medication for user {user_id} and medication ID {medication_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching medication: {str(e)}"
            )

    async def update_medication(self, user_id: UUID, medication_id: UUID, medication_update: MedicationUpdate) -> UserMedication:
        """
        Update a medication record for a user

        Args:
            user_id (UUID): The user ID
            medication_id (UUID): The medication ID
            medication_update (MedicationUpdate): The medication data to update

        Returns:
            UserUserMedication: The updated medication record

        Raises:
            HTTPException: If the medication is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the medication
                medication = db.query(UserMedicationORM).filter_by(
                    user_id=str(user_details.id),
                    id=medication_id
                ).first()

                if not medication:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Medication not found for user {str(user_details.id)} and medication ID {medication_id}"
                    )

                # Update the fields
                update_data = medication_update.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    if hasattr(medication, key) and value is not None:
                        setattr(medication, key, value)

                # Update the updated_at timestamp
                setattr(medication, 'updated_at', datetime.now(timezone.utc))

                # Commit the changes
                db.add(medication)
                db.commit()
                db.refresh(medication)

                # Convert ORM object to Pydantic model
                return UserMedication.model_validate(medication)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating medication for user {user_id} and medication ID {medication_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating medication: {str(e)}"
            )

    async def delete_medication(self, user_id: UUID, medication_id: UUID) -> dict:
        """
        Delete a medication record for a user

        Args:
            user_id (UUID): The user ID
            medication_id (UUID): The medication ID

        Returns:
            dict: A dictionary with the status and message

        Raises:
            HTTPException: If the medication is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the medication
                medication = db.query(UserMedicationORM).filter_by(
                    user_id=str(user_details.id),
                    id=medication_id
                ).first()

                if not medication:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Medication not found for user {str(user_details.id)} and medication ID {medication_id}"
                    )

                # Delete the medication
                db.delete(medication)
                db.commit()

                self.logger.info(f"Successfully deleted medication {medication_id} for user {user_details.id}")

                return {
                    "status": "success",
                    "message": f"Medication {medication_id} deleted successfully"
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting medication for user {user_id} and medication ID {medication_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting medication: {str(e)}"
            )

    async def create_diagnosis(self, diagnosis: UserDiagnosisCreate) -> UserDiagnosis:
        """
        Create a new diagnosis record for a user

        Args:
            diagnosis (UserDiagnosisCreate): The diagnosis data to create

        Returns:
            UserUserDiagnosis: The created diagnosis record

        Raises:
            HTTPException: If an error occurs during creation
        """
        try:
            user_details = self.get_user_details(diagnosis.user_id)
            with get_db_session() as db:
                # Create new diagnosis record
                diagnosis_orm = UserDiagnosisORM(**diagnosis.model_dump(exclude_unset=True))

                # Add to database and commit
                db.add(diagnosis_orm)
                db.commit()
                db.refresh(diagnosis_orm)

                # Return as Pydantic model
                return UserDiagnosis.model_validate(diagnosis_orm)
        except Exception as e:
            self.logger.error(f"Error creating diagnosis for user {diagnosis.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating diagnosis: {str(e)}"
            )

    async def _create_bulk_records(self, bulk_request, items_field_name: str, create_class, orm_class, pydantic_class, entity_name: str, additional_fields: Optional[Dict[str, Any]] = None) -> List:
        """
        Generic helper method to create multiple records for a user in bulk.
        This method reduces code duplication across all bulk create methods.

        Args:
            bulk_request: The bulk request object containing user_id, record_id, and items
            items_field_name (str): The field name in bulk_request containing the items (e.g., 'diagnoses', 'procedures')
            create_class: The Pydantic Create class (e.g., UserDiagnosisCreate)
            orm_class: The ORM class (e.g., UserDiagnosisORM)
            pydantic_class: The response Pydantic class (e.g., UserDiagnosis)
            entity_name (str): The entity name for logging (e.g., 'diagnoses', 'procedures')
            additional_fields (dict): Optional additional fields to include in create objects

        Returns:
            List: A list of created records as Pydantic models

        Raises:
            HTTPException: If any error occurs during the creation process
        """
        try:
            user_details = self.get_user_details(bulk_request.user_id)
            items_to_create = []
            created_items = []

            # Get the items from the bulk request using the field name
            items = getattr(bulk_request, items_field_name, [])

            for item_data in items:
                # Create the individual Create object

                create_data = {
                    "user_id": bulk_request.user_id,
                    # "record_id": bulk_request.record_id,
                    **item_data.model_dump(exclude_unset=True)
                }

                if(hasattr(bulk_request, "record_id")):
                    create_data["record_id"] = bulk_request.record_id

                if(hasattr(bulk_request, "record_id")):
                    create_data["record_id"] = bulk_request.record_id

                if(hasattr(bulk_request, "record_id")):
                    create_data["record_id"] = bulk_request.record_id

                # Add any additional fields if provided
                if additional_fields:
                    create_data.update(additional_fields)

                item_create = create_class(**create_data)
                items_to_create.append(orm_class(**item_create.model_dump(exclude_unset=True)))

            with get_db_session() as db:
                if not items_to_create:
                    self.logger.warning(f"No {entity_name} provided in bulk request for user {user_details.id}")
                    return []

                db.add_all(items_to_create)
                self.logger.info(f"Creating {len(items_to_create)} {entity_name} for user {user_details.id}")
                db.commit()

                # Refresh all objects and convert to Pydantic models
                for item_orm in items_to_create:
                    db.refresh(item_orm)
                    created_items.append(pydantic_class.model_validate(item_orm))

                return created_items

        except Exception as e:
            self.logger.error(f"Error creating bulk {entity_name} for user {bulk_request.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating bulk {entity_name}: {str(e)}"
            )

    async def create_diagnoses_bulk(self, bulk_request: UserDiagnosisBulkCreate) -> List[UserDiagnosis]:
        """
        Create multiple diagnosis records for a user in bulk.
        This method processes a batch of diagnoses for a single user, creating multiple diagnosis
        records in a single database transaction. It ensures all diagnoses are created atomically
        and handles any potential errors during the process.

        Args:
            bulk_request (UserDiagnosisBulkCreate): A bulk request object containing:
                - user_id: ID of the user for whom diagnoses are being created
                - record_id: ID of the medical record associated with these diagnoses
                - diagnoses: List of diagnosis data to be created

        Returns:
            List[UserDiagnosis]: A list of created diagnosis records, each converted to a Pydantic model.
            Returns an empty list if no diagnoses were provided in the bulk request.

        Raises:
            HTTPException: With status code 500 if any error occurs during the creation process.
        """
        return await self._create_bulk_records(
            bulk_request=bulk_request,
            items_field_name="diagnoses",
            create_class=UserDiagnosisCreate,
            orm_class=UserDiagnosisORM,
            pydantic_class=UserDiagnosis,
            entity_name="diagnoses"
        )

    async def create_procedures_bulk(self, bulk_request: UserProcedureBulkCreate) -> List[UserProcedure]:
        """
        Create multiple procedure records for a user in bulk.

        This method processes a batch of procedures for a single user, creating multiple
        procedure records in a single database transaction. It ensures all procedures are
        created atomically and handles any potential errors during the process.

        Args:
            bulk_request (UserProcedureBulkCreate): A bulk request object containing:
                - user_id: ID of the user for whom procedures are being created
                - record_id: ID of the medical record associated with these procedures
                - procedures: List of procedure data to be created

        Returns:
            List[UserProcedure]: A list of created procedure records, each converted to a Pydantic model.
            Returns an empty list if no procedures were provided in the bulk request.

        Raises:
            HTTPException: With status code 500 if any error occurs during the creation process.
        """
        return await self._create_bulk_records(
            bulk_request=bulk_request,
            items_field_name="procedures",
            create_class=UserProcedureCreate,
            orm_class=UserProcedureORM,
            pydantic_class=UserProcedure,
            entity_name="procedures"
        )

    async def get_user_diagnoses(self, user_id: UUID) -> List[UserDiagnosis]:
        """
        Get all diagnoses for a specific user

        Args:
            user_id (UUID): The user ID

        Returns:
            List[UserUserDiagnosis]: List of all diagnoses for the user

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                diagnoses = db.query(UserDiagnosisORM).filter_by(user_id=str(user_details.id)).all()

                if not diagnoses:
                    # Return empty list if no diagnoses found
                    return []

                # Convert ORM objects to Diagnosis objects
                return [UserDiagnosis.model_validate(diag) for diag in diagnoses]
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching diagnoses for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user diagnoses: {str(e)}"
            )

    async def get_user_diagnoses_paginated(self, query_params: DiagnosisQueryParams) -> PaginatedDiagnosisResponse:
        """
        Get paginated, sorted, and filtered diagnoses for a specific user

        Args:
            query_params (DiagnosisQueryParams): Query parameters for pagination, sorting, and filtering

        Returns:
            PaginatedDiagnosisResponse: Paginated response with diagnoses data

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(query_params.user_id)
            config = get_diagnosis_pagination_config()
            return await PaginationHelper.get_paginated_results(user_details, query_params, config)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching paginated diagnoses for user {query_params.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated user diagnoses: {str(e)}"
            )

    async def get_user_genetics_paginated(self, query_params: GeneticsQueryParams) -> PaginatedGeneticsResponse:
        """
        Get paginated, sorted, and filtered genetics for a specific user

        Args:
            query_params (GeneticsQueryParams): Query parameters for pagination, sorting, and filtering

        Returns:
            PaginatedGeneticsResponse: Paginated response with genetics data

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(query_params.user_id)

            config = get_genetics_pagination_config()
            return await PaginationHelper.get_paginated_results(user_details, query_params, config)

        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching paginated genetics for user {query_params.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated user genetics: {str(e)}"
            )

    async def get_diagnosis(self, user_id: UUID, diagnosis_id: UUID) -> UserDiagnosis:
        """
        Get a specific diagnosis for a user

        Args:
            user_id (UUID): The user ID
            diagnosis_id (UUID): The diagnosis ID

        Returns:
            UserUserDiagnosis: The diagnosis record

        Raises:
            HTTPException: If the diagnosis is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                diagnosis = db.query(UserDiagnosisORM).filter_by(
                    user_id=str(user_details.id),
                    id=diagnosis_id
                ).first()

                if not diagnosis:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Diagnosis not found for user {str(user_details.id)} and diagnosis ID {diagnosis_id}"
                    )

                # Convert ORM object to Pydantic model
                return UserDiagnosis.model_validate(diagnosis)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching diagnosis for user {user_id} and diagnosis ID {diagnosis_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching diagnosis: {str(e)}"
            )

    async def update_diagnosis(self, user_id: UUID, diagnosis_id: UUID, diagnosis_update: UserDiagnosisBase) -> UserDiagnosis:
        """
        Update a diagnosis record for a user

        Args:
            user_id (UUID): The user ID
            diagnosis_id (UUID): The diagnosis ID
            diagnosis_update (DiagnosisUpdate): The diagnosis data to update

        Returns:
            UserUserDiagnosis: The updated diagnosis record

        Raises:
            HTTPException: If the diagnosis is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the diagnosis
                diagnosis = db.query(UserDiagnosisORM).filter_by(
                    user_id=str(user_details.id),
                    id=diagnosis_id
                ).first()

                if not diagnosis:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Diagnosis not found for user {str(user_details.id)} and diagnosis ID {diagnosis_id}"
                    )

                # Update the fields
                update_data = diagnosis_update.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    if hasattr(diagnosis, key) and value is not None:
                        setattr(diagnosis, key, value)

                # Update the updated_at timestamp
                setattr(diagnosis, 'updated_at', datetime.now(timezone.utc))

                # Commit the changes
                db.add(diagnosis)
                db.commit()
                db.refresh(diagnosis)

                # Convert ORM object to Pydantic model
                return UserDiagnosis.model_validate(diagnosis)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating diagnosis for user {user_id} and diagnosis ID {diagnosis_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating diagnosis: {str(e)}"
            )

    async def delete_diagnosis(self, user_id: UUID, diagnosis_id: UUID) -> dict:
        """
        Delete a diagnosis record for a user

        Args:
            user_id (UUID): The user ID
            diagnosis_id (UUID): The diagnosis ID

        Returns:
            dict: A dictionary with the status and message

        Raises:
            HTTPException: If the diagnosis is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the diagnosis
                diagnosis = db.query(UserDiagnosisORM).filter_by(
                    user_id=str(user_details.id),
                    id=diagnosis_id
                ).first()

                if not diagnosis:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Diagnosis not found for user {str(user_details.id)} and diagnosis ID {diagnosis_id}"
                    )

                # Delete the diagnosis
                db.delete(diagnosis)
                db.commit()

                self.logger.info(f"Successfully deleted diagnosis {diagnosis_id} for user {user_details.id}")

                return {
                    "status": "success",
                    "message": f"Diagnosis {diagnosis_id} deleted successfully"
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting diagnosis for user {user_id} and diagnosis ID {diagnosis_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting diagnosis: {str(e)}"
            )

    async def create_procedure(self, procedure: UserProcedureCreate) -> UserProcedure:
        """
        Create a new procedure record for a user

        Args:
            procedure (UserProcedureCreate): The procedure data to create

        Returns:
            UserProcedure: The created procedure record

        Raises:
            HTTPException: If an error occurs during creation
        """
        try:
            user_details = self.get_user_details(procedure.user_id)
            with get_db_session() as db:
                # Create new procedure record

                procedure_orm = UserProcedureORM(**procedure.model_dump(exclude_unset=True))
                # Add to database and commit
                db.add(procedure_orm)
                db.commit()
                db.refresh(procedure_orm)

                # Return as Pydantic model
                return UserProcedure.model_validate(procedure_orm)
        except Exception as e:
            self.logger.error(f"Error creating procedure for user {procedure.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating procedure: {str(e)}"
            )

    async def get_user_procedures(self, user_id: UUID) -> List[UserProcedure]:
        """
        Get all procedures for a specific user

        Args:
            user_id (UUID): The user ID

        Returns:
            List[UserProcedure]: List of all procedures for the user

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                procedures = db.query(UserProcedureORM).filter_by(user_id=user_details.id).all()

                if not procedures:
                    # Return empty list if no procedures found
                    return []

                # Convert ORM objects to Procedure objects
                return [UserProcedure.model_validate(proc) for proc in procedures]
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching procedures for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user procedures: {str(e)}"
            )

    async def get_user_procedures_paginated(self, query_params: ProcedureQueryParams) -> PaginatedProcedureResponse:
        """
        Get paginated, sorted, and filtered procedures for a specific user

        Args:
            user_id (UUID): The user ID
            query_params (ProcedureQueryParams): Query parameters for pagination, sorting, and filtering

        Returns:
            PaginatedProcedureResponse: Paginated response with procedures data

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(query_params.user_id)
            config = get_procedure_pagination_config()
            return await PaginationHelper.get_paginated_results(user_details, query_params, config)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching paginated procedures for user {query_params.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated user procedures: {str(e)}"
            )

    async def get_procedure(self, user_id: UUID, procedure_id: UUID) -> UserProcedure:
        """
        Get a specific procedure for a user

        Args:
            user_id (UUID): The user ID
            procedure_id (UUID): The procedure ID

        Returns:
            UserProcedure: The procedure record

        Raises:
            HTTPException: If the procedure is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                procedure = db.query(UserProcedureORM).filter_by(
                    user_id=str(user_details.id),
                    id=procedure_id
                ).first()

                if not procedure:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Procedure not found for user {str(user_details.id)} and procedure ID {procedure_id}"
                    )

                # Convert ORM object to Pydantic model
                return UserProcedure.model_validate(procedure)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching procedure for user {user_id} and procedure ID {procedure_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching procedure: {str(e)}"
            )

    async def update_procedure(self, user_id: UUID, procedure_id: UUID, procedure_update: UserProcedureBase) -> UserProcedure:
        """
        Update a procedure record for a user

        Args:
            user_id (UUID): The user ID
            procedure_id (UUID): The procedure ID
            procedure_update (UserProcedureBase): The procedure data to update

        Returns:
            UserProcedure: The updated procedure record

        Raises:
            HTTPException: If the procedure is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the procedure
                procedure = db.query(UserProcedureORM).filter_by(
                    user_id=str(user_details.id),
                    id=procedure_id
                ).first()

                if not procedure:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Procedure not found for user {str(user_details.id)} and procedure ID {procedure_id}"
                    )

                # Update the fields
                update_data = procedure_update.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    if hasattr(procedure, key) and value is not None:
                        setattr(procedure, key, value)

                # Update the updated_at timestamp
                setattr(procedure, 'updated_at', datetime.now(timezone.utc))

                # Commit the changes
                db.add(procedure)
                db.commit()
                db.refresh(procedure)

                # Convert ORM object to Pydantic model
                return UserProcedure.model_validate(procedure)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating procedure for user {user_id} and procedure ID {procedure_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating procedure: {str(e)}"
            )

    async def delete_procedure(self, user_id: UUID, procedure_id: UUID) -> dict:
        """
        Delete a procedure record for a user

        Args:
            user_id (UUID): The user ID
            procedure_id (UUID): The procedure ID

        Returns:
            dict: A dictionary with the status and message

        Raises:
            HTTPException: If the procedure is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the procedure
                procedure = db.query(UserProcedureORM).filter_by(
                    user_id=str(user_details.id),
                    id=procedure_id
                ).first()

                if not procedure:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Procedure not found for user {str(user_details.id)} and procedure ID {procedure_id}"
                    )

                # Delete the procedure
                db.delete(procedure)
                db.commit()

                self.logger.info(f"Successfully deleted procedure {procedure_id} for user {user_details.id}")

                return {
                    "status": "success",
                    "message": f"Procedure {procedure_id} deleted successfully"
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting procedure for user {user_id} and procedure ID {procedure_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting procedure: {str(e)}"
            )

    async def create_genetics_bulk(self, bulk_request: UserGeneticsBulkCreate) -> List[UserGenetics]:
        """
        Create multiple genetics records for a user in bulk.

        This method processes a batch of genetics for a single user, creating multiple
        genetics records in a single database transaction. It ensures all genetics are
        created atomically and handles any potential errors during the process.

        Args:
            bulk_request (UserGeneticsBulkCreate): A bulk request object containing:
                - user_id: ID of the user for whom genetics are being created
                - record_id: ID of the medical record associated with these genetics
                - genetics: List of genetics data to be created

        Returns:
            List[UserGenetics]: A list of created genetics records, each converted to a Pydantic model.
            Returns an empty list if no genetics were provided in the bulk request.

        Raises:
            HTTPException: With status code 500 if any error occurs during the creation process.
        """
        return await self._create_bulk_records(
            bulk_request=bulk_request,
            items_field_name="genetics",
            create_class=UserGeneticsCreate,
            orm_class=UserGeneticsORM,
            pydantic_class=UserGenetics,
            entity_name="genetics"
        )

    async def create_genetics(self, genetics: UserGeneticsCreate) -> UserGenetics:
        """
        Create a new genetics record for a user

        Args:
            genetics (UserGeneticsCreate): The genetics data to create

        Returns:
            UserGenetics: The created genetics record

        Raises:
            HTTPException: If an error occurs during creation
        """
        try:
            user_details = self.get_user_details(genetics.user_id)
            with get_db_session() as db:
                # Create new genetics record
                genetics_orm = UserGeneticsORM(**genetics.model_dump(exclude_unset=True))
                # Add to database and commit
                db.add(genetics_orm)
                db.commit()
                db.refresh(genetics_orm)

                # Return as Pydantic model
                return UserGenetics.model_validate(genetics_orm)
        except Exception as e:
            self.logger.error(f"Error creating genetics for user {genetics.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating genetics: {str(e)}"
            )

    async def get_user_genetics(self, user_id: UUID) -> List[UserGenetics]:
        """
        Get all genetics for a specific user

        Args:
            user_id (UUID): The user ID

        Returns:
            List[UserGenetics]: List of all genetics for the user

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                geneticss = db.query(UserGeneticsORM).filter_by(user_id=user_details.id).all()

                if not geneticss:
                    # Return empty list if no geneticss found
                    return []

                # Convert ORM objects to Genetics objects
                return [UserGenetics.model_validate(proc) for proc in geneticss]
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching geneticss for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user geneticss: {str(e)}"
            )

    async def get_genetics(self, user_id: UUID, genetics_id: UUID) -> UserGenetics:
        """
        Get a specific genetics for a user

        Args:
            user_id (UUID): The user ID
            genetics_id (UUID): The genetics ID

        Returns:
            UserGenetics: The genetics record

        Raises:
            HTTPException: If the genetics is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                genetics = db.query(UserGeneticsORM).filter_by(
                    user_id=str(user_details.id),
                    id=genetics_id
                ).first()

                if not genetics:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Genetics not found for user {str(user_details.id)} and genetics ID {genetics_id}"
                    )

                # Convert ORM object to Pydantic model
                return UserGenetics.model_validate(genetics)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching genetics for user {user_id} and genetics ID {genetics_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching genetics: {str(e)}"
            )

    async def update_genetics(self, user_id: UUID, genetics_id: UUID, genetics_update: UserGeneticsBase) -> UserGenetics:
        """
        Update a genetics record for a user

        Args:
            user_id (UUID): The user ID
            genetics_id (UUID): The genetics ID
            genetics_update (UserGeneticsBase): The genetics data to update

        Returns:
            UserGenetics: The updated genetics record

        Raises:
            HTTPException: If the genetics is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the genetics
                genetics = db.query(UserGeneticsORM).filter_by(
                    user_id=str(user_details.id),
                    id=genetics_id
                ).first()

                if not genetics:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Genetics not found for user {str(user_details.id)} and genetics ID {genetics_id}"
                    )

                # Update the fields
                update_data = genetics_update.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    if hasattr(genetics, key) and value is not None:
                        setattr(genetics, key, value)

                # Update the updated_at timestamp
                setattr(genetics, 'updated_at', datetime.now(timezone.utc))

                # Commit the changes
                db.add(genetics)
                db.commit()
                db.refresh(genetics)

                # Convert ORM object to Pydantic model
                return UserGenetics.model_validate(genetics)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating genetics for user {user_id} and genetics ID {genetics_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating genetics: {str(e)}"
            )

    async def delete_genetics(self, user_id: UUID, genetics_id: UUID) -> dict:
        """
        Delete a genetics record for a user

        Args:
            user_id (UUID): The user ID
            genetics_id (str): The genetics ID

        Returns:
            dict: A dictionary with the status and message

        Raises:
            HTTPException: If the genetics is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the genetics
                genetics = db.query(UserGeneticsORM).filter_by(
                    user_id=str(user_details.id),
                    id=genetics_id
                ).first()

                if not genetics:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Genetics not found for user {str(user_details.id)} and genetics ID {genetics_id}"
                    )

                # Delete the genetics
                db.delete(genetics)
                db.commit()

                self.logger.info(f"Successfully deleted genetics {genetics_id} for user {user_details.id}")

                return {
                    "status": "success",
                    "message": f"Genetics {genetics_id} deleted successfully"
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting genetics for user {user_id} and genetics ID {genetics_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting genetics: {str(e)}"
            )

    async def __post_data_to_api(self, url: str, data: Dict[str, Any], headers:Dict[str,str]) -> Dict[str, Any]:
        """
        Helper method to post data to an external API

        Args:
            url (str): The API endpoint URL
            data (Dict[str, any]): The data to post

        Returns:
            Dict[str, any]: The response from the API

        Raises:
            HTTPException: If an error occurs during the request
        """
        payload = json.dumps(data)
        self.logger.info(f"Payload: {payload} posted to URL: {url} with headers: {headers}")
        async with aiohttp.ClientSession() as session:
            async with session.post(url, data=payload, headers=headers) as response:
                resp_data = await response.text()
                self.logger.info(f"Response received from {url} : {resp_data}")
                status_code = response.status
                self.logger.info(f"API {url} response status: {status_code}")

                if status_code >= 400:
                    self.logger.error(f"Error (code {status_code} posting to API {url} : {data}")
                    return {"status": "error", "message": "Error from API"}

                try:
                    json_data = json.loads(resp_data) if resp_data else {}
                    return {"status": "success", "response": json_data}
                except json.JSONDecodeError:
                    return {"status": "success", "response": resp_data}
                except Exception as e:
                    self.logger.error(f"__post_data_to_api encountered an error for URL: {url}. Exception: {str(e)}")
                    raise HTTPException(
                        status_code=500,
                        detail=f"API post operation failed unexpectedly. Error: {str(e)}"
                    )

    async def get_n1_id(self, bubble_id: str) -> str:
        """
        Get the N1 ID for a user
        Args:
            bubble_id (str): The user ID
        Returns:
            str: The N1 ID
        Raises:
            HTTPException: If the N1 ID is not found or an error occurs
        """
        try:
            with get_db_session(commit=False) as db:
                user_orm = db.query(UserORM).filter(UserORM.bubble_id == bubble_id).first()

                if not user_orm:
                    raise HTTPException(
                        status_code=404,
                        detail=f"User with Bubble ID {bubble_id} not found"
                    )

                return str(user_orm.id)
        except HTTPException:
                raise
        except Exception as e:
                self.logger.error(f"Error fetching N1 ID for Bubble ID {bubble_id}: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error fetching N1 ID: {str(e)}"
                )

    async def create_biomarker(self, biomarker: UserBiomarkerCreate) -> UserBiomarker:
        """
        Create a new biomarker record for a user

        Args:
            biomarker (UserBiomarkerCreate): The biomarker data to create

        Returns:
            UserBiomarker: The created biomarker record

        Raises:
            HTTPException: If an error occurs during creation
        """
        try:
            user_details = self.get_user_details(biomarker.user_id)
            with get_db_session() as db:
                # Create new biomarker record using UserBiomarkerORM
                biomarker_orm = UserBiomarkerORM(**biomarker.model_dump(exclude_unset=True))

                # Add to database and commit
                db.add(biomarker_orm)
                db.commit()
                db.refresh(biomarker_orm)

                # Return as UserBiomarker model
                return UserBiomarker.model_validate(biomarker_orm)
        except Exception as e:
            self.logger.error(f"Error creating biomarker for user {biomarker.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating biomarker: {str(e)}"
            )

    async def create_biomarkers_bulk(self, bulk_request: UserBiomarkerBulkCreate) -> List[UserBiomarker]:
        """
        Create multiple biomarker records for a user in bulk.

        Args:
            bulk_request (UserBiomarkerBulkCreate): A bulk request object containing:
                - user_id: ID of the user for whom biomarkers are being created
                - record_id: ID of the medical record associated with these biomarkers
                - biomarkers: List of biomarker data to be created

        Returns:
            List[UserBiomarker]: A list of created biomarker records

        Raises:
            HTTPException: If any error occurs during the creation process
        """
        return await self._create_bulk_records(
            bulk_request=bulk_request,
            items_field_name="biomarkers",
            create_class=UserBiomarkerCreate,
            orm_class=UserBiomarkerORM,  # Note: biomarkers use UserBiomarkerORM, not UserBiomarkerORM
            pydantic_class=UserBiomarker,
            entity_name="biomarkers",
            #additional_fields={"filename": bulk_request.filename}  # biomarkers need filename field
        )

    async def get_user_biomarkers(self, user_id: UUID) -> List[UserBiomarker]:
        """
        Get all biomarkers for a specific user

        Args:
            user_id (str): The user ID

        Returns:
            List[UserBiomarker]: List of all biomarkers for the user

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                biomarkers = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.user_id == str(user_details.id),
                    UserBiomarkerORM.excluded == False  # Only include records that are not excluded
                ).all()

                if not biomarkers:
                    # Return empty list if no biomarkers found
                    return []

                # Convert ORM objects to UserBiomarker objects
                return [UserBiomarker.model_validate(biomarker) for biomarker in biomarkers]
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching biomarkers for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user biomarkers: {str(e)}"
            )

    async def get_biomarker(self, user_id: UUID, biomarker_id: UUID) -> UserBiomarker:
        """
        Get a specific biomarker for a user

        Args:
            user_id (UUID): The user ID
            biomarker_id (UUID): The biomarker ID

        Returns:
            UserBiomarker: The biomarker record

        Raises:
            HTTPException: If the biomarker is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                biomarker = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.user_id == str(user_details.id),
                    UserBiomarkerORM.id == biomarker_id,
                    UserBiomarkerORM.excluded == False  # Only include records that are not excluded
                ).first()

                if not biomarker:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Biomarker not found for user {str(user_details.id)} and biomarker ID {biomarker_id}"
                    )

                # Convert ORM object to Pydantic model
                return UserBiomarker.model_validate(biomarker)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching biomarker for user {user_id} and biomarker ID {biomarker_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching biomarker: {str(e)}"
            )

    async def update_biomarker(self, user_id: UUID, biomarker_id: UUID, biomarker_update: UserBiomarkerBase) -> UserBiomarker:
        """
        Update a biomarker record for a user

        Args:
            user_id (UUID): The user ID
            biomarker_id (UUID): The biomarker ID
            biomarker_update (UserBiomarkerBase): The biomarker data to update

        Returns:
            UserBiomarker: The updated biomarker record

        Raises:
            HTTPException: If the biomarker is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the biomarker
                biomarker = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.user_id == str(user_details.id),
                    UserBiomarkerORM.id == biomarker_id
                ).first()

                if not biomarker:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Biomarker not found for user {str(user_details.id)} and biomarker ID {biomarker_id}"
                    )

                # Update the fields
                update_data = biomarker_update.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    if hasattr(biomarker, key) and value is not None:
                        setattr(biomarker, key, value)

                # Set the edited flag to true
                setattr(biomarker, 'edited', True)

                # Update the updated_at timestamp
                setattr(biomarker, 'updated_at', datetime.now(timezone.utc))

                # Commit the changes
                db.add(biomarker)
                db.commit()
                db.refresh(biomarker)

                # Convert ORM object to Pydantic model
                return UserBiomarker.model_validate(biomarker)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating biomarker for user {user_id} and biomarker ID {biomarker_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating biomarker: {str(e)}"
            )

    async def delete_biomarker_record(self, user_id: UUID, biomarker_id: UUID) -> dict:
        """
        Delete a biomarker record for a user (soft delete by setting excluded=True)

        Args:
            user_id (UUID): The user ID
            biomarker_id (UUID): The biomarker ID

        Returns:
            dict: A dictionary with the status and message

        Raises:
            HTTPException: If the biomarker is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session() as db:
                # Find the biomarker
                biomarker = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.user_id == str(user_details.id),
                    UserBiomarkerORM.id == biomarker_id
                ).first()

                if not biomarker:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Biomarker not found for user {str(user_details.id)} and biomarker ID {biomarker_id}"
                    )

                # Set the excluded flag to true (soft delete)
                setattr(biomarker, 'excluded', True)
                setattr(biomarker, 'updated_at', datetime.now(timezone.utc))

                # Commit the changes
                db.add(biomarker)
                db.commit()

                self.logger.info(f"Successfully deleted biomarker {biomarker_id} for user {user_details.id}")

                return {
                    "status": "success",
                    "message": f"Biomarker {biomarker_id} deleted successfully"
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting biomarker for user {user_id} and biomarker ID {biomarker_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting biomarker: {str(e)}"
            )

    async def get_user_biomarkers_paginated(self, query_params: BiomarkerQueryParams) -> 'PaginatedBiomarkerResponse':
        """
        Get paginated, sorted, and filtered biomarkers for a specific user

        Args:
            user_id (str): The user ID
            query_params (BiomarkerQueryParams): Query parameters for pagination, sorting, and filtering

        Returns:
            PaginatedBiomarkerResponse: Paginated response with biomarkers data

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(query_params.user_id)
            config = get_biomarker_pagination_config()
            return await PaginationHelper.get_paginated_results(user_details, query_params, config)
        except Exception as e:
            self.logger.error(f"Error fetching paginated biomarkers for user {query_params.user_id}: {str(e)}")

            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated user biomarkers: {str(e)}"
            )

    async def get_user_records_paginated(self, query_params: RecordQueryParams) -> PaginatedRecordResponse:
        """
        Get paginated, sorted, and filtered records for a specific user

        Args:
            query_params (RecordQueryParams): Query parameters for pagination, sorting, and filtering

        Returns:
            PaginatedRecordResponse: Paginated response with records data

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(query_params.user_id)
            config = get_record_pagination_config()
            paginated_response = await PaginationHelper.get_paginated_results(user_details, query_params, config)

            if paginated_response.data:
                await self._add_counters_to_records(paginated_response.data)

            return paginated_response
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching paginated records for user {query_params.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated user records: {str(e)}"
            )

    async def get_user_reports_paginated(self, query_params: ReportQueryParams) -> PaginatedReportResponse:
        """
        Get paginated, sorted, and filtered reports for a specific user

        Args:
            query_params (ReportQueryParams): Query parameters for pagination, sorting, and filtering

        Returns:
            PaginatedReportResponse: Paginated response with reports data

        Raises:
            HTTPException: If the user is not found or an error occurs
        """
        try:
            user_details = self.get_user_details(query_params.user_id)
            config = get_report_pagination_config()
            return await PaginationHelper.get_paginated_results(user_details, query_params, config)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching paginated reports for user {query_params.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated user reports: {str(e)}"
            )


    async def parser_results(self):
        """
        Returns a JSON context with averages of Accuracy, Recall, Precision, F1_Score from
        the latest date-based folder in the GCS bucket, and signed URLs stripped of query parameters.
        """
        # Connect to GCS
        try:
            storage = FileStorageService()
            bucket = storage.storage_client.bucket(config.DEV_BUCKET_NAME)
        except json.JSONDecodeError as e:
            return JSONResponse(content={"error": f"Error parsing GCP credentials: {str(e)}"}, status_code=500)
        except Exception as e:
            return JSONResponse(content={"error": f"Error connecting to GCS bucket `{config.DEV_BUCKET_NAME}`: {str(e)}"}, status_code=500)

        # Regex for date-based aggregate summaries
        summary_re = re.compile(
            r"^Biomarkers/Langroid/"
            r"(?P<date>\d{4}-\d{2}-\d{2})/"
            r"aggregate_summary_\d{8}_\d{6}\.json$"
        )

        # Find latest blob per date
        found = {}
        for blob in bucket.list_blobs(prefix="Biomarkers/Langroid/"):
            match = summary_re.match(blob.name)
            if not match:
                continue
            date = match.group("date")
            prev = found.get(date)
            if prev is None or blob.name > prev.name:
                found[date] = blob

        if not found:
            return JSONResponse(
                content={"error": "No aggregate_summary JSONs found under Biomarkers/Langroid/"},
                status_code=404
            )

        # Download and parse the JSON
        latest_date = sorted(found.keys())[-1]
        summary_blob = found[latest_date]
        try:
            summary_json = json.loads(summary_blob.download_as_text())
        except json.JSONDecodeError as e:
            return JSONResponse(content={"error": f"Failed to parse JSON from '{summary_blob.name}': {str(e)}"}, status_code=500)
        except Exception as e:
            return JSONResponse(content={"error": f"Failed to download summary file '{summary_blob.name}': {str(e)}"}, status_code=500)

        if not isinstance(summary_json, list):
            return JSONResponse(
                content={"error": "Summary JSON is not a list."},
                status_code=400
            )

        count = len(summary_json)
        if count == 0:
            return JSONResponse(
                content={"error": "Summary JSON is empty."},
                status_code=400
            )

        # Compute averages
        sum_acc = sum(item.get("Accuracy", 0) * item.get("Ground_Truth_Count") for item in summary_json)
        sum_rec = sum(item.get("Recall", 0) * item.get("Ground_Truth_Count") for item in summary_json)
        sum_prec = sum(item.get("Precision", 0) * item.get("Extracted_Count") for item in summary_json)

        total_extracted = sum(item.get("Extracted_Count", 0) for item in summary_json)

        avg_precision = sum_prec / total_extracted
        avg_recall = sum_rec / total_extracted
        avg_accuracy = sum_acc / total_extracted

        if avg_precision + avg_recall == 0:
            avg_f1 = 0.0
        else:
            avg_f1 = 2 * (avg_precision * avg_recall) / (avg_precision + avg_recall)

        avg = {
            "Accuracy":  round(avg_accuracy, 2),
            "Recall":    round(avg_recall, 2),
            "Precision": round(avg_precision, 2),
            "F1_Score":  round(avg_f1, 2),
        }

        # Prepare signed URLs without query params
        base_path = f"Biomarkers/Langroid/{latest_date}/"
        blob_map = {}
        for blob in bucket.list_blobs(prefix=base_path):
            suffix = blob.name[len(base_path):]
            if "_analysis_report" in suffix:
                base_name = suffix.split("_analysis_report")[0]
            else:
                base_name = suffix.rsplit(".", 1)[0]
            blob_map[base_name] = blob


        signed_url = ""
        enhanced = []
        for item in summary_json:
            name = item.get("File", "")
            new_item = item.copy()
            short_url = None
            if name in blob_map:
                blob = blob_map[name]
                try:
                    signed_url = blob.generate_signed_url(
                        expiration=timedelta(hours=1)
                    )
                except Exception as e:
                    short_url = None
                new_item["File"] = signed_url
            enhanced.append(new_item)

        # Final structured response
        return JSONResponse(
            content={
                "result_summary": {
                    "date": latest_date,
                    "averages": avg
                },
                "file_details": enhanced
            }
        )

    async def add_canonical_biomarkers_bulk(self, bulk_request: BulkCanonicalBiomarkerCreate) -> List[CanonicalBiomarker]:
        """
        Create multiple canonical biomarker records in bulk (only new canonical names will be created).
        Args:
            bulk_request (BulkCanonicalBiomarkerCreate): List of canonical biomarker definitions
        Returns:
            List[CanonicalBiomarker]: List of created canonical biomarker records
        Raises:
            HTTPException: If any error occurs during the creation process
        """
        return await self._create_bulk_records(
            bulk_request=bulk_request,
            items_field_name="canonical_biomarkers",
            create_class=CanonicalBiomarkerCreate,
            orm_class=CanonicalRecordORM,
            pydantic_class=CanonicalBiomarker,
            entity_name="canonical_biomarkers"
        )

    async def update_user_biomarkers_canonical_id_bulk(self, request):
        """
        Bulk update canonical_id for user biomarkers after enrichment mapping.
        Args:
            request: List of objects with 'canonical_id' and 'user_biomarker_ids' fields
        Returns:
            dict: Summary of updates
        """
        try:
            updated = []
            failed = []
            with get_db_session() as db:
                for mapping in request.updates:
                    canonical_id = getattr(mapping, "canonical_id", None)
                    biomarker_ids = getattr(mapping, "user_biomarker_ids", [])
                    for biomarker_id in biomarker_ids:
                        biomarker = db.query(UserBiomarkerORM).filter_by(id=biomarker_id).first()
                        if biomarker:
                            biomarker.canonical_id = canonical_id
                            db.add(biomarker)
                            updated.append(str(biomarker.id))
                        else:
                            failed.append(str(biomarker_id))
                db.commit()
            return {"updated": updated, "failed": failed}
        except Exception as e:
            self.logger.error(f"Error in update_user_biomarkers_canonical_id_bulk: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error in bulk user biomarker canonical_id update: {str(e)}")

    async def get_user_canonical_biomarkers(self, user_id: UUID) -> List[CanonicalBiomarker]:
        """
        Get all canonical biomarkers for a given user (i.e., those referenced by their biomarkers).
        Args:
            user_id: UUID
        Returns:
            List of canonical biomarker dicts
        """
        try:
            with get_db_session(commit=False) as db:
                # Find all canonical_ids referenced by this user's biomarkers with count of records they were present in
                # Optimized single query with JOIN to get canonical records and their counts
                canonical_records_with_count = db.query(
                    CanonicalRecordORM,
                    func.count(UserBiomarkerORM.canonical_id).label('record_count')
                ).join(
                    UserBiomarkerORM,
                    CanonicalRecordORM.id == UserBiomarkerORM.canonical_id
                ).filter(
                    UserBiomarkerORM.user_id == user_id,
                    UserBiomarkerORM.canonical_id.isnot(None)
                ).group_by(CanonicalRecordORM.id).all()

                if not canonical_records_with_count:
                    return []

                # Create result list with record counts in a single pass
                result_list = []
                for canonical_obj, count in canonical_records_with_count:
                    canonical_biomarker = CanonicalBiomarker.model_validate(canonical_obj)
                    canonical_biomarker.member_count = count
                    result_list.append(canonical_biomarker)

                return result_list
        except Exception as e:
            self.logger.error(f"Error in get_user_canonical_biomarkers: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error fetching user's canonical biomarkers: {str(e)}")

    async def get_user_biomarkers_for_canonical(self, user_id: UUID, canonical_id: UUID) -> List[UserBiomarker]:
        """
        Get all biomarkers for a given user for a given canonical biomarker.
        Args:
            user_id: UUID - The user ID
            canonical_id: UUID - The canonical biomarker ID
        Returns:
            List[UserBiomarker]: List of user biomarkers that belong to the specified canonical biomarker
        """
        try:
            user_details = self.get_user_details(user_id)
            with get_db_session(commit=False) as db:
                biomarkers = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.user_id == user_details.id,
                    UserBiomarkerORM.canonical_id == canonical_id
                ).all()

                if not biomarkers:
                    # Return empty list if no biomarkers found
                    return []

                # Convert ORM objects to UserBiomarker objects
                return [UserBiomarker.model_validate(biomarker) for biomarker in biomarkers]
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching biomarkers for user {user_id} and canonical {canonical_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user biomarkers for canonical: {str(e)}"
            )

    # CRUD Operations for Canonical Biomarkers
    async def create_canonical_biomarker(self, canonical_biomarker: CanonicalBiomarkerCreate) -> CanonicalBiomarker:
        """
        Create a new canonical biomarker record.

        Args:
            canonical_biomarker (CanonicalBiomarkerCreate): The canonical biomarker data to create

        Returns:
            CanonicalBiomarker: The created canonical biomarker record

        Raises:
            HTTPException: If an error occurs during creation
        """
        try:
            with get_db_session() as db:
                # Check if canonical biomarker with same name already exists
                existing = db.query(CanonicalRecordORM).filter(
                    CanonicalRecordORM.canonical_name == canonical_biomarker.canonical_name
                ).first()

                if existing:
                    raise HTTPException(
                        status_code=409,
                        detail=f"Canonical biomarker with name '{canonical_biomarker.canonical_name}' already exists"
                    )

                # Create new canonical biomarker record
                canonical_orm = CanonicalRecordORM(**canonical_biomarker.model_dump(exclude_unset=True, exclude={'id'}))

                # Add to database and commit
                db.add(canonical_orm)
                db.commit()
                db.refresh(canonical_orm)

                # Return as CanonicalBiomarker model
                return CanonicalBiomarker.model_validate(canonical_orm)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error creating canonical biomarker: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating canonical biomarker: {str(e)}"
            )

    async def get_canonical_biomarker(self, canonical_id: UUID) -> CanonicalBiomarker:
        """
        Get a specific canonical biomarker by ID.

        Args:
            canonical_id (UUID): The canonical biomarker ID

        Returns:
            CanonicalBiomarker: The canonical biomarker record

        Raises:
            HTTPException: If the canonical biomarker is not found or an error occurs
        """
        try:
            with get_db_session(commit=False) as db:
                canonical_orm = db.query(CanonicalRecordORM).filter(
                    CanonicalRecordORM.id == canonical_id
                ).first()

                if not canonical_orm:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Canonical biomarker with ID {canonical_id} not found"
                    )

                return CanonicalBiomarker.model_validate(canonical_orm)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error fetching canonical biomarker {canonical_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching canonical biomarker: {str(e)}"
            )

    async def get_canonical_biomarkers_paginated(self, query_params: CanonicalBiomarkerQueryParams) -> PaginatedCanonicalBiomarkerResponse:
        """
        Get paginated, sorted, and filtered canonical biomarkers.

        Args:
            query_params (CanonicalBiomarkerQueryParams): Query parameters for pagination and filtering

        Returns:
            PaginatedBiomarkerRecordResponse: Paginated response with canonical biomarkers

        Raises:
            HTTPException: If an error occurs during the query
        """
        try:
            # Get user details from query params (canonical biomarkers are user-specific)
            user_details = self.get_user_details(query_params.user_id)
            config = get_canonical_biomarker_pagination_config()
            paginated_response = await PaginationHelper.get_paginated_results(user_details, query_params, config)

            if paginated_response.data:
                await self._add_member_counts_to_canonical_biomarkers(paginated_response.data, user_details.id)

            return paginated_response
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error in get_canonical_biomarkers_paginated: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching paginated canonical biomarkers: {str(e)}"
            )

    async def update_canonical_biomarker(self, canonical_id: UUID, canonical_update: CanonicalBiomarkerUpdate) -> CanonicalBiomarker:
        """
        Update a canonical biomarker record.

        Args:
            canonical_id (UUID): The canonical biomarker ID
            canonical_update (CanonicalBiomarkerUpdate): The update data

        Returns:
            CanonicalBiomarker: The updated canonical biomarker record

        Raises:
            HTTPException: If the canonical biomarker is not found or an error occurs
        """
        try:
            with get_db_session() as db:
                canonical_orm = db.query(CanonicalRecordORM).filter(
                    CanonicalRecordORM.id == canonical_id
                ).first()

                if not canonical_orm:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Canonical biomarker with ID {canonical_id} not found"
                    )

                # Check if updating canonical_name would create a duplicate
                if canonical_update.canonical_name:
                    existing = db.query(CanonicalRecordORM).filter(
                        CanonicalRecordORM.canonical_name == canonical_update.canonical_name,
                        CanonicalRecordORM.id != canonical_id
                    ).first()

                    if existing:
                        raise HTTPException(
                            status_code=409,
                            detail=f"Canonical biomarker with name '{canonical_update.canonical_name}' already exists"
                        )

                # Update fields
                update_data = canonical_update.model_dump(exclude_unset=True)
                for field, value in update_data.items():
                    setattr(canonical_orm, field, value)

                db.commit()
                db.refresh(canonical_orm)

                return CanonicalBiomarker.model_validate(canonical_orm)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error updating canonical biomarker {canonical_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating canonical biomarker: {str(e)}"
            )

    async def delete_canonical_biomarker(self, canonical_id: UUID) -> dict:
        """
        Delete a canonical biomarker record.

        Args:
            canonical_id (UUID): The canonical biomarker ID

        Returns:
            dict: Success message

        Raises:
            HTTPException: If the canonical biomarker is not found, is in use, or an error occurs
        """
        try:
            with get_db_session() as db:
                canonical_orm = db.query(CanonicalRecordORM).filter(
                    CanonicalRecordORM.id == canonical_id
                ).first()

                if not canonical_orm:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Canonical biomarker with ID {canonical_id} not found"
                    )

                # Check if canonical biomarker is referenced by any user biomarkers
                referenced_count = db.query(UserBiomarkerORM).filter(
                    UserBiomarkerORM.canonical_id == canonical_id
                ).count()

                if referenced_count > 0:
                    raise HTTPException(
                        status_code=409,
                        detail=f"Cannot delete canonical biomarker: it is referenced by {referenced_count} user biomarker(s)"
                    )

                # Delete the canonical biomarker
                db.delete(canonical_orm)
                db.commit()

                return {
                    "status": "success",
                    "message": f"Canonical biomarker {canonical_id} deleted successfully"
                }
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting canonical biomarker {canonical_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting canonical biomarker: {str(e)}"
            )

    async def get_record_counts(self, record_id: str) -> RecordCounts:
        """
        Get counts of biomarkers, diagnosis, genetics, and procedures for a specific record

        Args:
            record_id (str): The record ID

        Returns:
            RecordCounts: Object containing counts for each data type

        Raises:
            HTTPException: If the record is not found or another error occurs
        """
        try:
            with get_db_session(commit=False) as db:

                found = (
                    db.query(UserRecordRequestORM).filter_by(id=record_id).first()
                )
                if not found:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Record {record_id} not found"
                    )

                biomarker_count = db.query(UserBiomarkerORM).filter_by(
                    record_id=record_id,
                    excluded=False
                ).count()

                diagnosis_count = db.query(UserDiagnosisORM).filter_by(
                    record_id=record_id
                ).count()

                genetics_count = db.query(UserGeneticsORM).filter_by(
                    record_id=record_id
                ).count()

                procedures_count = db.query(UserProcedureORM).filter_by(
                    record_id=record_id
                ).count()

            return RecordCounts(
                record_id=record_id,
                biomarkers=biomarker_count,
                diagnosis=diagnosis_count,
                genetics=genetics_count,
                procedures=procedures_count
            )

        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Error getting record counts for {record_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error getting record counts: {e}"
            )

    async def _add_counters_to_records(self, records: List[N1RecordUpdateRequest]) -> None:
        """
        Add counters to a list of records by fetching counts for each record

        Args:
            records (List[N1RecordUpdateRequest]): List of records to add counters to
        """
        try:
            with get_db_session(commit=False) as db:
                # Get all record IDs
                record_ids = [record.id for record in records if record.id]

                if not record_ids:
                    return

                # Batch query for biomarkers counts
                biomarker_counts = db.query(
                    UserBiomarkerORM.record_id,
                    func.count(UserBiomarkerORM.id).label('count')
                ).filter(
                    UserBiomarkerORM.record_id.in_(record_ids),
                    UserBiomarkerORM.excluded == False
                ).group_by(UserBiomarkerORM.record_id).all()

                # Batch query for diagnosis counts
                diagnosis_counts = db.query(
                    UserDiagnosisORM.record_id,
                    func.count(UserDiagnosisORM.id).label('count')
                ).filter(
                    UserDiagnosisORM.record_id.in_(record_ids)
                ).group_by(UserDiagnosisORM.record_id).all()

                # Batch query for genetics counts
                genetics_counts = db.query(
                    UserGeneticsORM.record_id,
                    func.count(UserGeneticsORM.id).label('count')
                ).filter(
                    UserGeneticsORM.record_id.in_(record_ids)
                ).group_by(UserGeneticsORM.record_id).all()

                # Batch query for procedures counts
                procedures_counts = db.query(
                    UserProcedureORM.record_id,
                    func.count(UserProcedureORM.id).label('count')
                ).filter(
                    UserProcedureORM.record_id.in_(record_ids)
                ).group_by(UserProcedureORM.record_id).all()

                # Convert to dictionaries for fast lookup
                biomarker_dict = {record_id: count for record_id, count in biomarker_counts}
                diagnosis_dict = {record_id: count for record_id, count in diagnosis_counts}
                genetics_dict = {record_id: count for record_id, count in genetics_counts}
                procedures_dict = {record_id: count for record_id, count in procedures_counts}

                # Add counters to each record
                for record in records:
                    if record.id:
                        record.biomarkers_count = biomarker_dict.get(record.id, 0)
                        record.diagnosis_count = diagnosis_dict.get(record.id, 0)
                        record.genetics_count = genetics_dict.get(record.id, 0)
                        record.procedures_count = procedures_dict.get(record.id, 0)

        except Exception as e:
            self.logger.error(f"Error adding counters to records: {str(e)}")
            # Don't raise exception here, just log the error and continue without counters

    async def _add_member_counts_to_canonical_biomarkers(self, canonical_biomarkers: List[CanonicalBiomarker], user_id: UUID) -> None:
        """
        Add accurate member counts to a list of canonical biomarkers

        Args:
            canonical_biomarkers (List[CanonicalBiomarker]): List of canonical biomarkers to add member counts to
            user_id (UUID): User ID to filter biomarkers by
        """
        try:
            with get_db_session(commit=False) as db:
                # Get all canonical IDs
                canonical_ids = [cb.id for cb in canonical_biomarkers if cb.id]

                if not canonical_ids:
                    return

                # Batch query for member counts
                member_counts = db.query(
                    UserBiomarkerORM.canonical_id,
                    func.count(UserBiomarkerORM.id).label('count')
                ).filter(
                    UserBiomarkerORM.canonical_id.in_(canonical_ids),
                    UserBiomarkerORM.user_id == user_id,
                    UserBiomarkerORM.excluded == False
                ).group_by(UserBiomarkerORM.canonical_id).all()

                # Convert to dictionary for fast lookup
                member_count_dict = {canonical_id: count for canonical_id, count in member_counts}

                # Add member counts to each canonical biomarker
                for canonical_biomarker in canonical_biomarkers:
                    if canonical_biomarker.id:
                        canonical_biomarker.member_count = member_count_dict.get(canonical_biomarker.id, 0)

        except Exception as e:
            self.logger.error(f"Error adding member counts to canonical biomarkers: {str(e)}")
            # Don't raise exception here, just log the error and continue without member counts

    async def complete_batches(self, request: BatchCompletionRequest) -> BatchCompletionResponse:
        """
        Complete multiple batches by updating all records in each batch to COMPLETED status

        Args:
            request (BatchCompletionRequest): The batch completion request containing user_id and batch_ids

        Returns:
            BatchCompletionResponse: Response containing completion status and details

        Raises:
            HTTPException: If an error occurs during batch completion
        """
        try:
            user_details = self.get_user_details(request.user_id)
            completed_batches = []
            failed_batches = []
            total_records_updated = 0

            if not request.batch_ids:
                return BatchCompletionResponse(
                    status="failed",
                    user_id=request.user_id,
                    completed_batches=[],
                    failed_batches=[],
                    total_records_updated=0,
                )

            try:
                with get_db_session() as db:
                    # First, check which batches actually have records
                    batch_record_counts = db.query(
                        UserRecordRequestORM.batch_id,
                        func.count(UserRecordRequestORM.id).label('record_count')
                    ).filter(
                        UserRecordRequestORM.user_id == str(user_details.id),
                        UserRecordRequestORM.batch_id.in_(request.batch_ids)
                    ).group_by(UserRecordRequestORM.batch_id).all()

                    # Create a mapping of batch_id to record count
                    batch_counts = {batch_id: count for batch_id, count in batch_record_counts}

                    # Identify batches with no records
                    for batch_id in request.batch_ids:
                        if batch_id not in batch_counts:
                            self.logger.warning(f"No records found for batch {batch_id}")
                            failed_batches.append({
                                "batch_id": batch_id,
                                "error": f"No records found for batch {batch_id}"
                            })

                    # Get list of batches that have records
                    batches_with_records = list(batch_counts.keys())

                    if batches_with_records:
                        # Update all records in all batches with a single query
                        updated_count = db.query(UserRecordRequestORM).filter(
                            UserRecordRequestORM.user_id == str(user_details.id),
                            UserRecordRequestORM.batch_id.in_(batches_with_records)
                        ).update({
                            UserRecordRequestORM.status: "COMPLETED",
                            UserRecordRequestORM.message: "Enrichment completed",
                            UserRecordRequestORM.progress: 100,
                            UserRecordRequestORM.updated_at: datetime.now(timezone.utc)
                        }, synchronize_session=False)

                        db.commit()

                        # Add successfully updated batches to completed list
                        completed_batches.extend(batches_with_records)
                        total_records_updated = updated_count

            except Exception as e:
                self.logger.error(f"Error completing batches: {str(e)}")
                # If the bulk update fails, all batches are considered failed
                for batch_id in request.batch_ids:
                    if batch_id not in [fb['batch_id'] for fb in failed_batches]:
                        failed_batches.append({
                            "batch_id": batch_id,
                            "error": str(e)
                        })
                completed_batches = []
                total_records_updated = 0

            # Determine overall status
            if len(completed_batches) == len(request.batch_ids):
                status = "success"
            elif len(completed_batches) > 0:
                status = "partial_success"
            else:
                status = "failed"

            return BatchCompletionResponse(
                status=status,
                user_id=request.user_id,
                completed_batches=completed_batches,
                failed_batches=failed_batches,
                total_records_updated=total_records_updated,
            )

        except Exception as e:
            self.logger.error(f"Error completing batches for user {request.user_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error completing batches: {str(e)}"
            )
