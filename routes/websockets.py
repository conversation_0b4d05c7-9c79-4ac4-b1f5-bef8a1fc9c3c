from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
import json
from datetime import datetime
from dependencies import verify_api_key_websocket, get_connection_manager
from ws_service import ConnectionManager
import logging


logger = logging.getLogger(__name__)

router = APIRouter()

@router.websocket("/{user_id}")
async def websocket_user_endpoint(websocket: WebSocket, user_id: str, connection_manager: ConnectionManager = Depends(get_connection_manager)):
    await verify_api_key_websocket(websocket)
    await connection_manager.connect(websocket, user_id)
    try:
        while True:
            data = await websocket.receive_text()
            try:
                parsed_data = json.loads(data)
                if parsed_data.get("type") == "ping":
                    await connection_manager.send_personal_message(
                        json.dumps({"type": "pong", "timestamp": datetime.now().isoformat()}),
                        websocket
                    )
            except json.JSONDecodeError:
                logger.warning(f"Received invalid JSO<PERSON> from user {user_id}: {data}")
            except Exception as e:
                logger.error(f"Error processing message from user {user_id}: {e}")
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket, user_id)
    except Exception as e:
        logger.info(f"WebSocket error for user {user_id}: {e}")
        connection_manager.disconnect(websocket, user_id)
