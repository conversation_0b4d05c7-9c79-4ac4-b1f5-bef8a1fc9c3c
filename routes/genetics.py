
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List

from schemas import UserGenetics, UserGeneticsCreate, UserGeneticsBase, UserGeneticsBulkCreate, GeneticsQueryParams, PaginatedGeneticsResponse
from services import N1ProcessPipeline
from dependencies import get_pipeline
from uuid import UUID


router = APIRouter()

@router.post("/", response_model=UserGenetics, status_code=status.HTTP_201_CREATED)
async def create_user_genetics(
    genetics_create: UserGeneticsCreate,
    service: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Create a new genetics record for a user.
    """
    try:
        return await service.create_genetics(genetics_create)
    except HTTPException as http_exc:
        raise http_exc  # Re-raise HTTPException to ensure proper FastAPI error handling
    except Exception as e:
        # Log the exception for debugging
        # logger.error(f"Error creating genetics: {str(e)}") # Assuming logger is configured
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )

@router.post("/bulk", response_model=List[UserGenetics])
async def create_user_genetics_bulk(
    bulk_request: UserGeneticsBulkCreate, pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Create multiple genetics records for a user in bulk.
    """
    return await pipeline.create_genetics_bulk(bulk_request)

@router.get("/", response_model=List[UserGenetics], deprecated=True)
async def get_user_geneticss(
    user_id: UUID,
    service: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Get all geneticss for a specific user.
    """
    try:
        return await service.get_user_genetics(user_id)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )

@router.get("/paginated", response_model=PaginatedGeneticsResponse)
async def get_user_genetics_paginated(
    query_params: GeneticsQueryParams = Depends(),
    pipeline: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Get paginated, sorted, and filtered genetics for a specific user.

    This endpoint provides advanced querying capabilities including:
    - Pagination with configurable page sizes
    - Sorting by any genetics field (gene, test_date, created_at, etc.)
    - Filtering by record ID, gene, allele, variant, and test date ranges

    Example usage:
    - Get first 10 genetics: ?page=1&page_size=10
    - Sort by test date ascending: ?sort_by=test_date&is_descending=false
    - Sort by test date descending: ?sort_by=test_date&is_descending=true
    - Filter by record ID: ?record_id=REC123
    - Filter by gene: ?gene=BRCA1
    - Filter by date range: ?test_date_from=2023-01-01&test_date_to=2023-12-31
    - Search by variant: ?variant=rs1234567
    """
    # Create query parameters object

    return await pipeline.get_user_genetics_paginated(query_params)

@router.get("/{genetics_id}", response_model=UserGenetics)
async def get_user_genetics_by_id(
    user_id: UUID,
    genetics_id: UUID,
    service: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Get a specific genetics for a user.
    """
    try:
        return await service.get_genetics(user_id, genetics_id)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )

@router.patch("/{genetics_id}", response_model=UserGenetics)
async def update_user_genetics_by_id(
    user_id: UUID,
    genetics_id: UUID,
    genetics_update: UserGeneticsBase,
    service: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Update a genetics record for a user.
    """
    try:
        return await service.update_genetics(user_id, genetics_id, genetics_update)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )

@router.delete("/{genetics_id}", status_code=status.HTTP_200_OK)
async def delete_user_genetics_by_id(
    user_id: UUID,
    genetics_id: UUID,
    service: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Delete a genetics record for a user.
    """
    try:
        result = await service.delete_genetics(user_id, genetics_id)
        return result
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )
