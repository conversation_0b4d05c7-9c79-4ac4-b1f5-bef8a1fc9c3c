from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import HTMLResponse
from dependencies import verify_credentials, verify_api_key
from chart_service import (
    generate_biomarker_line_chart,
    generate_biomarker_bar_chart,
    get_biomarker_line_chart_data,
    get_biomarker_bar_chart_data,
    create_biomarker_comparison_chart
)
from schemas import ChartData
from services import N1ProcessPipeline
from ws_service import ConnectionManager
from typing import List, Optional
from uuid import UUID
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize the pipeline
connection_manager = ConnectionManager()
pipeline = N1ProcessPipeline(connection_manager)


@router.get("/biomarker")
async def biomarker_chart(
    user_id: UUID,
    canonical_id: Optional[UUID] = None,
    chart_type: str = Query("line", pattern="^(line|bar)$"),
    username: str = Depends(verify_credentials)
):
    """
    Serves biomarker charts in HTML format.

    Args:
        user_id: UUID of the user
        canonical_id: Optional UUID of canonical biomarker to filter by
        chart_type: Type of chart - "line" or "bar"

    Returns:
        Chart in HTML format
    """
    try:
        if canonical_id:
            # Get biomarkers for specific canonical biomarker
            biomarkers = await pipeline.get_user_biomarkers_for_canonical(user_id, canonical_id)
        else:
            # Get all biomarkers for user
            biomarkers = await pipeline.get_user_biomarkers(user_id)

        # Generate chart content based on type
        if chart_type == "line":
            content = generate_biomarker_line_chart(biomarkers)
        elif chart_type == "bar":
            content = generate_biomarker_bar_chart(biomarkers)
        else:
            return HTMLResponse(
                content="<html><body><h1>Error</h1><p>Invalid chart type. Supported types: line, bar</p></body></html>",
                status_code=400
            )

        return HTMLResponse(content=content)

    except Exception as e:
        logger.error(f"Error generating biomarker chart: {str(e)}")
        return HTMLResponse(
            content=f"<html><body><h1>Error</h1><p>Failed to generate chart: {str(e)}</p></body></html>",
            status_code=500
        )


@router.get("/biomarker/json", response_model=ChartData)
async def biomarker_chart_json(
    user_id: UUID,
    canonical_id: Optional[UUID] = None,
    chart_type: str = Query("line", pattern="^(line|bar)$"),
    username: str = Depends(verify_api_key)
):
    """
    Serves biomarker chart data in JSON format with chart_id and javascript.

    Args:
        user_id: UUID of the user
        canonical_id: Optional UUID of canonical biomarker to filter by
        chart_type: Type of chart - "line" or "bar"

    Returns:
        Dictionary containing chart_id and javascript
    """
    try:
        if canonical_id:
            # Get biomarkers for specific canonical biomarker
            biomarkers = await pipeline.get_user_biomarkers_for_canonical(user_id, canonical_id)
        else:
            # Get all biomarkers for user
            biomarkers = await pipeline.get_user_biomarkers(user_id)

        # Generate chart data based on type
        if chart_type == "line":
            return get_biomarker_line_chart_data(biomarkers)
        elif chart_type == "bar":
            return get_biomarker_bar_chart_data(biomarkers)
        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid chart type. Supported types: line, bar"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating biomarker chart data: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate chart data: {str(e)}"
        )


@router.get("/biomarker/comparison")
async def biomarker_comparison_chart(
    user_id: UUID,
    canonical_ids: List[UUID] = Query(..., description="List of canonical biomarker IDs to compare"),
    username: str = Depends(verify_credentials)
):
    """
    Serves a comparison chart for multiple canonical biomarkers in HTML format.

    Args:
        user_id: UUID of the user
        canonical_ids: List of canonical biomarker UUIDs to compare

    Returns:
        Comparison chart in HTML format
    """
    try:
        if not canonical_ids:
            return HTMLResponse(
                content="<html><body><h1>Error</h1><p>At least one canonical_id is required for comparison</p></body></html>",
                status_code=400
            )

        # Get biomarkers for each canonical biomarker
        biomarkers_dict = {}
        for canonical_id in canonical_ids:
            biomarkers = await pipeline.get_user_biomarkers_for_canonical(user_id, canonical_id)
            if biomarkers:
                # Use the first biomarker's test name or canonical name as the group name
                group_name = (
                    biomarkers[0].canonical_record.get("canonical_name", f"Canonical {canonical_id}")
                    if biomarkers[0].canonical_record
                    else f"Canonical {canonical_id}"
                )
                biomarkers_dict[group_name] = biomarkers

        if not biomarkers_dict:
            return HTMLResponse(
                content="<html><body><h1>No Data</h1><p>No biomarker data found for the specified canonical biomarkers</p></body></html>",
                status_code=404
            )

        # Generate comparison chart
        comparison_chart = create_biomarker_comparison_chart(biomarkers_dict)
        content = comparison_chart.render_embed()

        return HTMLResponse(content=content)

    except Exception as e:
        logger.error(f"Error generating biomarker comparison chart: {str(e)}")
        return HTMLResponse(
            content=f"<html><body><h1>Error</h1><p>Failed to generate comparison chart: {str(e)}</p></body></html>",
            status_code=500
        )
