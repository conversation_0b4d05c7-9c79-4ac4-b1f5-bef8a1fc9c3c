from datetime import datetime
from fastapi import APIRouter, Depends, Path, Query
from typing import List, Optional

from uuid import UUID

from services import N1ProcessPipeline
from schemas import UserMedication, UserMedicationCreate, MedicationUpdate, MedicationQueryParams, PaginatedMedicationResponse
from dependencies import get_pipeline

router = APIRouter(
    tags=["medications"],
    responses={404: {"description": "Not found"}},
)


@router.post("", response_model=UserMedication)
async def create_user_medication(
    medication: UserMedicationCreate,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Create a new medication record for a user.
    """
    return await pipeline.create_medication(medication)


@router.get("", response_model=List[UserMedication])
async def get_user_medications(
    user_id: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get all medications for a specific user.
    """
    return await pipeline.get_user_medications(user_id)


@router.get("/paginated", response_model=PaginatedMedicationResponse)
async def get_user_medications_paginated(
    query_params: MedicationQueryParams = Depends(),
    pipeline: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Get paginated, sorted, and filtered medications for a specific user.

    This endpoint provides advanced querying capabilities including:
    - Pagination with configurable page sizes
    - Sorting by any medication field (name, started_from, created_at, etc.)
    - Filtering by medication name, brand name, type, unit, frequency, reason
    - Date range filtering for start and stop dates

    Example usage:
    - Get first 10 medications: ?page=1&page_size=10
    - Sort by start date descending: ?sort_by=started_from&is_descending=true
    - Filter by medication name: ?name=Aspirin
    - Filter by brand name: ?brand_name=Bayer
    - Filter by type: ?type=pain_reliever
    - Filter by frequency: ?frequency=daily
    - Filter by start date range: ?started_from_date=2023-01-01&started_to_date=2023-12-31
    - Filter by stop date range: ?stopped_from_date=2023-01-01&stopped_to_date=2023-12-31
    - Search by reason: ?reason=headache
    """
    return await pipeline.get_user_medications_paginated(query_params)


@router.get("/{medication_id}", response_model=UserMedication)
async def get_user_medication_by_id(
    user_id: UUID,
    medication_id: str = Path(..., description="The ID of the medication to get"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get a specific medication for a user.
    """
    return await pipeline.get_medication(user_id, medication_id)


@router.patch("/{medication_id}", response_model=UserMedication)
async def update_user_medication_by_id(
    medication_update: MedicationUpdate,
    user_id: UUID,
    medication_id: UUID = Path(..., description="The ID of the medication to update"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Update a medication record for a user.
    """
    return await pipeline.update_medication(user_id, medication_id, medication_update)


@router.delete("/{medication_id}")
async def delete_user_medication_by_id(
    user_id: UUID,
    medication_id: UUID = Path(..., description="The ID of the medication to delete"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Delete a medication record for a user.
    """
    return await pipeline.delete_medication(user_id, medication_id)
