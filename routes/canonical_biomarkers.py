from fastapi import APIRouter, Depends, status, Path
from typing import List
from uuid import UUID
from schemas import (
    BulkCanonicalBiomarkerCreate,
    BulkUserBiomarkerCanonicalUpdate,
    CanonicalBiomarker,
    CanonicalBiomarkerCreate,
    CanonicalBiomarkerUpdate,
    CanonicalBiomarkerQueryParams,
    PaginatedCanonicalBiomarkerResponse,
    UserBiomarker
)
from services import N1ProcessPipeline
from dependencies import get_pipeline

router = APIRouter(
    tags=["canonical_biomarkers"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=CanonicalBiomarker, status_code=status.HTTP_201_CREATED)
async def create_canonical_biomarker(
    canonical_biomarker: CanonicalBiomarkerCreate,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Create a new canonical biomarker record.
    """
    return await pipeline.create_canonical_biomarker(canonical_biomarker)

@router.get("/paginated", response_model=PaginatedCanonicalBiomarkerResponse)
async def get_canonical_biomarkers_paginated(
    query_params: CanonicalBiomarkerQueryParams = Depends(),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get paginated, sorted, and filtered canonical biomarkers with accurate member counts.

    This endpoint provides advanced querying capabilities including:
    - Pagination with configurable page sizes
    - Sorting by any canonical biomarker field (canonical_name, created_at, etc.)
    - Filtering by canonical name, standard unit, group name, health areas, and date ranges
    - **Accurate member counts**: Each canonical biomarker includes the correct count of associated user biomarkers

    **Fixed**: Member counts are now accurately calculated for each canonical biomarker.

    Example usage:
    - Get first 10 canonical biomarkers: ?page=1&page_size=10
    - Sort by canonical name ascending: ?sort_by=canonical_name&is_descending=false
    - Sort by creation date descending: ?sort_by=created_at&is_descending=true
    - Filter by canonical name: ?canonical_name=glucose
    - Filter by group name: ?group_name=metabolic
    - Filter by date range: ?created_from=2023-01-01&created_to=2023-12-31
    """
    return await pipeline.get_canonical_biomarkers_paginated(query_params)

@router.get("/{canonical_id}", response_model=CanonicalBiomarker)
async def get_canonical_biomarker_by_id(
    canonical_id: UUID = Path(..., description="The ID of the canonical biomarker to get"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get a specific canonical biomarker by ID.
    """
    return await pipeline.get_canonical_biomarker(canonical_id)

@router.patch("/{canonical_id}", response_model=CanonicalBiomarker)
async def update_canonical_biomarker(
    canonical_update: CanonicalBiomarkerUpdate,
    canonical_id: UUID = Path(..., description="The ID of the canonical biomarker to update"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Update a canonical biomarker record.
    """
    return await pipeline.update_canonical_biomarker(canonical_id, canonical_update)

@router.delete("/{canonical_id}")
async def delete_canonical_biomarker(
    canonical_id: UUID = Path(..., description="The ID of the canonical biomarker to delete"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Delete a canonical biomarker record.

    Note: Cannot delete canonical biomarkers that are referenced by user biomarkers.
    """
    return await pipeline.delete_canonical_biomarker(canonical_id)

# Existing bulk and user-related endpoints

@router.post("/bulk", summary="Bulk add canonical biomarkers")
async def add_canonical_biomarkers_bulk(
    request: BulkCanonicalBiomarkerCreate,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Add canonical biomarkers in bulk. Only new canonical names will be created.
    """
    return await pipeline.add_canonical_biomarkers_bulk(request)

@router.patch("/user-biomarkers/canonical-id-bulk", summary="Bulk update user biomarker canonical_id")
async def update_user_biomarkers_canonical_id_bulk(
    request: BulkUserBiomarkerCanonicalUpdate,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Bulk update canonical_id for user biomarkers after enrichment mapping.
    Payload structure:
    [
      {"canonical_id": "c1", "biomarker_ids": ["b1", "b2"]},
      {"canonical_id": "c2", "biomarker_ids": ["b3", "b4"]}
    ]
    """
    # Accepts a list of mappings directly
    return await pipeline.update_user_biomarkers_canonical_id_bulk(request)

@router.get("/user/{user_id}", summary="Get all canonical biomarkers for a given user with member counts", response_model=List[CanonicalBiomarker])
async def get_user_canonical_biomarkers(user_id: UUID, pipeline: N1ProcessPipeline = Depends(get_pipeline)):
    """
    Get all canonical biomarkers for a given user with the counts of assigned individual member biomarkers.
    """
    return await pipeline.get_user_canonical_biomarkers(user_id)

@router.get("/user/{user_id}/canonical/{canonical_id}", summary="Get all biomarkers for a user for a given canonical biomarker", response_model=List[UserBiomarker])
async def get_user_biomarkers_for_canonical(
    user_id: UUID,
    canonical_id: UUID = Path(..., description="The ID of the canonical biomarker"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get all biomarkers for a given user for a given canonical biomarker.
    """
    return await pipeline.get_user_biomarkers_for_canonical(user_id, canonical_id)
