from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from dependencies import verify_credentials
from services import N1ProcessPipeline
from dependencies import get_pipeline

router = APIRouter()

@router.get("/")
async def get_parser_results(service : N1ProcessPipeline = Depends(get_pipeline)):
    """
    Returns a JSON context with averages of Accuracy, Recall, Precision, F1_Score from
    the latest date-based folder in the GCS bucket, and signed URLs stripped of query parameters.
    """
    try:
        return await service.parser_results()
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )
