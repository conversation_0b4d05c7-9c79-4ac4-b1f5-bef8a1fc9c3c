from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import HTMLResponse
from dependencies import verify_credentials, verify_api_key
from demo_chart_service import (
    generate_bar_chart,
    generate_line_chart,
    generate_pie_chart,
    generate_dag_diagram,
    generate_human_organ_chart,
    get_bar_chart_data,
    get_line_chart_data,
    get_pie_chart_data,
    get_dag_diagram_data,
)
from schemas import ChartData


router = APIRouter()

@router.get("/test")
async def chart(username: str = Depends(verify_credentials), chart_type: str = "bar"):
    """
    Serves the chart in HTML format.

    Args:
        chart_type: Type of chart - "bar", "line", "pie", or "organ"

    Returns:
        Chart in HTML format
    """
    # Generate chart content based on type
    if chart_type == "bar":
        content = generate_bar_chart()
    elif chart_type == "line":
        content = generate_line_chart()
    elif chart_type == "pie":
        content = generate_pie_chart()
    elif chart_type == "dag":
        content = generate_dag_diagram()
    elif chart_type == "medical":
        content = generate_human_organ_chart()
    else:
        return HTMLResponse(
            content="<html><body><h1>Error</h1><p>Invalid chart type. Supported types: bar, line, pie, organ</p></body></html>",
            status_code=400
        )

    return HTMLResponse(content=content)

@router.get("/json", response_model=ChartData)
async def chart_json(username: str = Depends(verify_api_key), chart_type: str = "bar"):
    """
    Serves the chart data in JSON format with chart_id and javascript.

    Args:
        chart_type: Type of chart - "bar", "line", "pie", "dag", or "medical"

    Returns:
        Dictionary containing chart_id and javascript
    """
    # Generate chart data based on type
    if chart_type == "bar":
        return get_bar_chart_data()
    elif chart_type == "line":
        return get_line_chart_data()
    elif chart_type == "pie":
        return get_pie_chart_data()
    elif chart_type == "dag":
        return get_dag_diagram_data()
    else:
        raise HTTPException(
            status_code=400,
            detail="Invalid chart type. Supported types: bar, line, pie, dag, medical"
        )

