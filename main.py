import base64
import binascii
import logging
import secrets
import sys

from fastapi import FastAPI, Depends, status, Request, Response
from fastapi.responses import HTMLResponse
from fastapi.routing import APIRoute
from routes import records, reports, users, medications, diagnoses, procedures, genetics, biomarkers, charts, websockets, parser_results, canonical_biomarkers, biomarker_charts
from database import engine, Base
from dependencies import verify_api_key, verify_credentials, USERNAME, PASSWORD

# Configure root logger with a stream handler
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
if not root_logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    handler.setFormatter(formatter)
    handler.setLevel(logging.INFO)
    root_logger.addHandler(handler)


__version__ = "0.1.8"


# Configure root logger with a stream handler
root_logger = logging.getLogger(__name__)
root_logger.setLevel(logging.INFO)
if not root_logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    handler.setFormatter(formatter)
    handler.setLevel(logging.INFO)
    root_logger.addHandler(handler)


# Configure root logger with a stream handler
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
if not root_logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    handler.setFormatter(formatter)
    handler.setLevel(logging.INFO)
    root_logger.addHandler(handler)


def generate_unique_id(route: APIRoute):
    """
    Generate a unique operation ID for the OpenAPI specification. Help generate useful client operation names
    Args:
        route: The FastAPI route object

    Returns:
        str: A operation ID as name of the implementing endpoint method
    """
    return route.name


app = FastAPI(
    title="N1 API",
    summary="API for N1 Data Processing",
    openapi_tags=[
        {"name": "records", "description": "Operations with medical records"},
        {"name": "reports", "description": "Operations with reports"},
        {"name": "users", "description": "User management operations"},
        {"name": "medications", "description": "Medication management operations"},
        {"name": "diagnoses", "description": "Diagnosis management operations"},
        {"name": "procedures", "description": "Procedure management operations"},
        {"name": "genetics", "description": "Genetics management operations"},
        {"name": "biomarkers", "description": "Biomarker management operations"},
        {"name": "charts", "description": "Chart visualization operations"},
        {"name": "parser_results", "description": "Operations with parsing result"},
        {"name": "websockets", "description": "Realtime updates via WebSockets"},
    ],
    version=__version__,
    generate_unique_id_function=generate_unique_id,
)

# Paths to protect
PROTECTED_PATHS = ["/docs", "/redoc", "/openapi.json"]
logger = logging.getLogger(__name__)


# --- Basic Auth Middleware ---
@app.middleware("http")
async def basic_auth_middleware(request: Request, call_next):
    # Check if the request path is one we want to protect
    if request.url.path in PROTECTED_PATHS:
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            # If no Authorization header, challenge the client
            return Response(
                content="Not authenticated",
                status_code=status.HTTP_401_UNAUTHORIZED,
                headers={"WWW-Authenticate": 'Basic realm="Protected Docs"'},
            )

        # Check if it's Basic auth
        try:
            scheme, credentials = auth_header.split(" ", 1)
            if scheme.lower() != "basic":
                raise ValueError("Invalid authentication scheme")

            # Decode the Base64 credentials
            decoded = base64.b64decode(credentials).decode("utf-8")
            username, _, password = decoded.partition(":")  # Use partition for safety

            # Securely compare credentials
            correct_username = secrets.compare_digest(username, USERNAME)
            correct_password = secrets.compare_digest(password, PASSWORD)

            if not (correct_username and correct_password):
                raise ValueError("Invalid username or password")

        except (ValueError, UnicodeDecodeError, binascii.Error) as e:
            logger.warning("Error %s", str(e))
            return Response(
                content="Authentication failed",
                status_code=status.HTTP_401_UNAUTHORIZED,
                headers={"WWW-Authenticate": 'Basic realm="Protected Docs"'},
            )

    # If path is not protected or authentication was successful, continue to the next middleware
    response = await call_next(request)
    return response


@app.get("/", response_class=HTMLResponse)
async def root(username: str = Depends(verify_credentials)):
    """
    Serves the analyse_record.html page at the root path.

    Returns:
    - HTML page for record analysis
    """
    try:
        # Read the analyse_record.html template
        with open("analyse_record.html", "r") as f:
            template = f.read()
        return HTMLResponse(content=template)
    except Exception as e:
        logger.error(f"Error reading template: {str(e)}")
        return HTMLResponse(
            content=f"<html><body><h1>Error</h1><p>{str(e)}</p></body></html>",
            status_code=500,
        )


# Include the routers (defined in your routes modules) with API key authentication
app.include_router(
    records.router,
    prefix="/records",
    tags=["records"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    reports.router,
    prefix="/reports",
    tags=["reports"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    users.router,
    prefix="/users",
    tags=["users"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    medications.router,
    prefix="/medications",
    tags=["medications"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    diagnoses.router,
    prefix="/diagnoses",
    tags=["diagnoses"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    procedures.router,
    prefix="/procedures",
    tags=["procedures"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    genetics.router,
    prefix="/genetics",
    tags=["genetics"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    biomarkers.router,
    prefix="/biomarkers",
    tags=["biomarkers"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    canonical_biomarkers.router,
    prefix="/canonical_biomarkers",
    tags=["canonical_biomarkers"],
    dependencies=[Depends(verify_api_key)],
)
app.include_router(
    charts.router,
    prefix="/charts",
    tags=["charts"],
    dependencies=[Depends(verify_credentials)],
)
app.include_router(
    biomarker_charts.router,
    prefix="/charts",
    tags=["charts"],
    dependencies=[Depends(verify_credentials)],
)
app.include_router(
    websockets.router,
    prefix="/ws",
    tags=["websockets"],
)
app.include_router(
    parser_results.router,
    prefix="/parser_results",
    tags=["parser_results"],
    dependencies=[Depends(verify_credentials)],   
)
