*.exe
*.pyc
*.txt
*PKG-INFO
.env
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.env
.venv
env/
venv/
ENV/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific files
.DS_Store
.AppleDouble
.LSOverride
._*

# Logs and databases
*.log
*.sqlite
*.db

# Test coverage
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Local development configuration
config.local.py
.env.local
env.sh

# AWS credentials
.aws/
aws.config

# Project specific
uploads/
temp/
*.pdf
node_modules/
