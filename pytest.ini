[pytest]
markers =
    unit: marks tests as unit tests
    integration: marks tests as integration tests
    e2e: marks tests as end-to-end tests

asyncio_mode = auto

testpaths = tests

python_files = test_*.py
python_classes = Test*
python_functions = test_*

addopts = --verbose

filterwarnings =
    ignore:builtin type SwigPyPacked has no __module__ attribute:DeprecationWarning
    ignore:builtin type SwigPyObject has no __module__ attribute:DeprecationWarning
    ignore:builtin type swigvarlink has no __module__ attribute:DeprecationWarning
