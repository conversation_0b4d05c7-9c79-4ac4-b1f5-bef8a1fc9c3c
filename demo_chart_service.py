from pyecharts import options as opts
from pyecharts.charts import Bar, Pie, Line, Graph, Geo
from bs4 import BeautifulSoup
from schemas import ChartData


def create_bar_chart():
    """Creates a bar chart comparing sales data of fruits from two different stores.

    Returns:
        Bar: Bar chart instance
    """
    # Sample data
    categories = ["Apples", "Bananas", "Oranges", "Grapes", "Pears"]
    data1 = [120, 200, 150, 80, 70]
    data2 = [60, 180, 170, 120, 50]

    # Create a Bar chart instance
    bar = (
        Bar()
        .add_xaxis(categories)
        .add_yaxis("Store A", data1)
        .add_yaxis("Store B", data2)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="Fruit Sales Comparison", subtitle="Weekly Sales Data"),
            tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="shadow"),
            xaxis_opts=opts.AxisOpts(
                name="Fruit",
                name_location="middle",
                name_gap=30,
                axislabel_opts=opts.LabelOpts(
                    rotate=0,
                    interval=0,
                    font_size=12,
                    margin=8
                )
            ),
            yaxis_opts=opts.AxisOpts(
                name="Sales (in kg)",
                name_location="middle",
                name_gap=50,
                axislabel_opts=opts.LabelOpts(font_size=12)
            ),
            legend_opts=opts.LegendOpts(
                orient="horizontal",
                pos_top="8%",
                pos_left="center"
            ),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=False,
                    type_="inside",
                    xaxis_index=[0],
                    range_start=0,
                    range_end=100,
                )
            ],
        )
        .set_series_opts(
            label_opts=opts.LabelOpts(is_show=True, position="top", font_size=10)
        )
    )
    return bar


def generate_bar_chart():
    """Generates a bar chart comparing sales data of fruits from two different stores.

    Returns:
        str: Rendered chart in HTML format
    """
    bar = create_bar_chart()
    return bar.render_embed()


def create_dag_diagram():
    """Creates an organ diagram showing relationships between body organs and systems.

    Returns:
        Graph: Graph chart instance
    """
    # Define organ nodes with categories for different body systems
    organ_nodes = [
        {"name": "Brain", "symbolSize": 50, "category": 0, "x": 300, "y": 50},
        {"name": "Heart", "symbolSize": 45, "category": 1, "x": 300, "y": 200},
        {"name": "Lungs", "symbolSize": 40, "category": 2, "x": 200, "y": 180},
        {"name": "Liver", "symbolSize": 35, "category": 3, "x": 400, "y": 250},
        {"name": "Kidneys", "symbolSize": 30, "category": 4, "x": 350, "y": 320},
        {"name": "Stomach", "symbolSize": 30, "category": 3, "x": 250, "y": 280},
        {"name": "Intestines", "symbolSize": 25, "category": 3, "x": 300, "y": 350},
        {"name": "Pancreas", "symbolSize": 20, "category": 3, "x": 320, "y": 290},
        {"name": "Spleen", "symbolSize": 18, "category": 4, "x": 180, "y": 260},
        {"name": "Bladder", "symbolSize": 15, "category": 4, "x": 300, "y": 380}
    ]

    # Define connections between organs (representing blood flow, neural connections, etc.)
    organ_links = [
        {"source": "Brain", "target": "Heart", "value": 10},
        {"source": "Heart", "target": "Lungs", "value": 15},
        {"source": "Heart", "target": "Liver", "value": 12},
        {"source": "Heart", "target": "Kidneys", "value": 10},
        {"source": "Heart", "target": "Brain", "value": 8},
        {"source": "Liver", "target": "Stomach", "value": 6},
        {"source": "Liver", "target": "Intestines", "value": 8},
        {"source": "Stomach", "target": "Intestines", "value": 10},
        {"source": "Stomach", "target": "Pancreas", "value": 5},
        {"source": "Kidneys", "target": "Bladder", "value": 8},
        {"source": "Spleen", "target": "Stomach", "value": 4},
        {"source": "Pancreas", "target": "Intestines", "value": 6}
    ]

    # Define categories for different organ systems
    organ_categories = [
        {"name": "Nervous System"},
        {"name": "Cardiovascular System"},
        {"name": "Respiratory System"},
        {"name": "Digestive System"},
        {"name": "Urinary System"}
    ]

    # Create a Graph chart instance
    graph = (
        Graph(init_opts=opts.InitOpts(width="800px", height="600px"))
        .add(
            series_name="Human Organ Systems",
            nodes=organ_nodes,
            links=organ_links,
            categories=organ_categories,
            layout="none",  # Use fixed positions
            is_roam=True,  # Allow zooming and panning
            is_focusnode=True,  # Highlight connected nodes on hover
            is_draggable=True,  # Allow dragging nodes
            symbol="circle",
            edge_symbol=["none", "arrow"],
            edge_symbol_size=8,
            repulsion=1000,
        )
        .set_global_opts(
            title_opts=opts.TitleOpts(
                title="Human Organ System Diagram",
                subtitle="Interactive visualization of organ relationships"
            ),
            tooltip_opts=opts.TooltipOpts(
                formatter="{b}: {c}"
            ),
            legend_opts=opts.LegendOpts(
                orient="vertical",
                pos_left="2%",
                pos_top="20%"
            )
        )
        .set_series_opts(
            label_opts=opts.LabelOpts(
                is_show=True,
                position="inside",
                font_size=10
            ),
            linestyle_opts=opts.LineStyleOpts(
                width=2,
                curve=0.2
            )
        )
    )
    return graph


def generate_dag_diagram():
    """Generates an organ diagram showing relationships between body organs and systems.

    Returns:
        str: Rendered chart in HTML format
    """
    graph = create_dag_diagram()
    return graph.render_embed()


def create_pie_chart():
    """Creates a pie chart showing market share distribution.

    Returns:
        Pie: Pie chart instance
    """
    # Sample data
    data = [
        ["Technology", 35],
        ["Healthcare", 25],
        ["Finance", 20],
        ["Education", 12],
        ["Others", 8]
    ]

    # Create a Pie chart instance
    pie = (
        Pie()
        .add("Market Share", data)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="Market Share Distribution", subtitle="By Industry Sector"),
            tooltip_opts=opts.TooltipOpts(trigger="item", formatter="{a} <br/>{b}: {c} ({d}%)"),
        )
        .set_series_opts(
            label_opts=opts.LabelOpts(formatter="{b}: {c} ({d}%)")
        )
    )
    return pie


def generate_pie_chart():
    """Generates a pie chart showing market share distribution.

    Returns:
        str: Rendered chart in HTML format
    """
    pie = create_pie_chart()
    return pie.render_embed()


def create_line_chart():
    """Creates a line chart showing sales trends over time.

    Returns:
        Line: Line chart instance
    """
    # Sample data
    months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
    sales_data = [120, 132, 101, 134, 90, 230]

    # Create a Line chart instance
    line = (
        Line()
        .add_xaxis(months)
        .add_yaxis("Monthly Sales", sales_data, is_smooth=True)  # type: ignore
        .set_global_opts(
            title_opts=opts.TitleOpts(title="Sales Trends", subtitle="Monthly Sales Performance"),
            tooltip_opts=opts.TooltipOpts(trigger="axis"),
            xaxis_opts=opts.AxisOpts(
                name="Month",
                name_location="middle",
                name_gap=30,
                axislabel_opts=opts.LabelOpts(
                    rotate=0,
                    interval=0,
                    font_size=12,
                    margin=8
                )
            ),
            yaxis_opts=opts.AxisOpts(
                name="Sales (in thousands)",
                name_location="middle",
                name_gap=50,
                axislabel_opts=opts.LabelOpts(font_size=12)
            ),
            legend_opts=opts.LegendOpts(
                orient="horizontal",
                pos_top="8%",
                pos_left="center"
            ),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=False,
                    type_="inside",
                    xaxis_index=[0],
                    range_start=0,
                    range_end=100,
                )
            ],
        )
        .set_series_opts(
            label_opts=opts.LabelOpts(is_show=True, position="top", font_size=10),
            markpoint_opts=opts.MarkPointOpts(
                data=[
                    opts.MarkPointItem(type_="max", name="Maximum"),
                    opts.MarkPointItem(type_="min", name="Minimum")
                ]
            ),
            linestyle_opts=opts.LineStyleOpts(width=3)
        )
    )
    return line


def generate_line_chart():
    """Generates a line chart showing sales trends over time.

    Returns:
        str: Rendered chart in HTML format
    """
    line = create_line_chart()

    return line.render_embed()


def generate_human_organ_chart():
    """Generates a human organ visualization using the provided medical SVG diagram.

    Creates an interactive chart with the actual medical SVG diagram and synchronized
    bar chart showing organ data values, with clickable organ elements.

    Returns:
        str: Rendered chart in HTML format
    """

    # Create the complete interactive visualization
    combined_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Human Organ Chart</title>
        <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                display: flex;
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .svg-container {
                flex: 1;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
            }
            .chart-container {
                flex: 1;
                padding: 20px;
            }
            .svg-title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .medical-svg {
                max-width: 100%;
                height: auto;
                background: rgba(255,255,255,0.9);
                border-radius: 10px;
                padding: 10px;
            }
            .medical-svg g[name] {
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .medical-svg g[name]:hover {
                filter: drop-shadow(0 0 10px #ff6b6b);
            }
            .medical-svg g[name].highlighted {
                filter: drop-shadow(0 0 15px #ff3030);
            }
            #chart-container {
                width: 100%;
                height: 500px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="svg-container">
                <div class="svg-title">Human Anatomy Diagram</div>
                <div class="medical-svg" id="svg-diagram">
                    <!-- SVG will be loaded here -->
                </div>
            </div>
            <div class="chart-container">
                <div id="chart-container"></div>
            </div>
        </div>

        <script type="text/javascript">
            // Initialize the chart
            var chart = echarts.init(document.getElementById('chart-container'));

            // Organ data
            var organ_data = {
                'heart': 121,
                'large-intestine': 321,
                'small-intestine': 141,
                'spleen': 52,
                'kidney': 198,
                'lung': 289,
                'liver': 139
            };

            var organ_names = Object.keys(organ_data);
            var organ_values = Object.values(organ_data);

            // Chart configuration
            var option = {
                title: {
                    text: 'Organ Health Metrics',
                    left: 'center',
                    textStyle: {
                        fontSize: 20,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        return params[0].name + '<br/>Value: ' + params[0].value;
                    }
                },
                grid: {
                    left: '15%',
                    right: '10%',
                    top: '20%',
                    bottom: '15%'
                },
                xAxis: {
                    type: 'value',
                    name: 'Health Value',
                    nameLocation: 'middle',
                    nameGap: 30,
                    axisLabel: {
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'category',
                    data: organ_names,
                    axisLabel: {
                        fontSize: 12,
                        interval: 0
                    }
                },
                series: [{
                    type: 'bar',
                    data: organ_values.map((value, index) => ({
                        value: value,
                        itemStyle: {
                            color: getOrganColor(organ_names[index])
                        }
                    })),
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0,0,0,0.3)'
                        }
                    },
                    animationDuration: 1000,
                    animationEasing: 'cubicOut'
                }]
            };

            // Set the chart option
            chart.setOption(option);

            // Color mapping for organs
            function getOrganColor(organ) {
                var colors = {
                    'heart': '#ff6b6b',
                    'lung': '#4ecdc4',
                    'liver': '#45b7d1',
                    'spleen': '#96ceb4',
                    'kidney': '#feca57',
                    'small-intestine': '#ff9ff3',
                    'large-intestine': '#54a0ff'
                };
                return colors[organ] || '#95a5a6';
            }

            // Load and display the SVG
            var svgContent = `<svg width="382.622" height="461.325" viewBox="0 0 382.622 461.325" style="max-width: 100%; height: auto;">
                <path fill="#D8CEBF" d="M181.702,5.555c-6.823,4.188-12.562,14.267-13.337,23.263c-1.24,10.389,4.342,6.823-0.465,15.817c-3.566,9.615-10.08,15.197-3.876,17.214c3.876,0.774,3.721,3.101,3.721,3.101s-1.086,4.343,0.311,9.461c2.016,6.203-0.776,10.7,1.706,12.561c3.411,3.412,9.615,1.085,14.421,1.085c1.552,4.962-3.412,21.865-3.412,21.865c-11.01,2.791-22.175,5.584-33.341,8.22c0,0-16.438-0.93-25.433,11.631c-13.647,14.732-10.855,48.538-10.855,48.538s-12.251,36.133-12.251,44.974c0,8.684-6.513,22.33-6.513,22.33c-12.562,8.839-12.095,27.449-16.904,42.492c10.855,6.513,19.384,8.839,30.55,10.698c8.064-17.059,19.695-34.579,19.695-53.813c3.878-10.854,7.444-21.554,11.166-32.41c0,0,9.305,32.72,8.684,45.437c-0.465,7.446-1.085,43.578-3.566,47.92c0,0-2.791,11.786-2.791,20.781c0,8.838-3.102,26.052-11.166,52.104c-8.219,25.896-12.561,81.724-12.561,81.724c22.021,0,44.042,0,66.218,0c5.738-24.967,11.32-49.778,17.058-74.589c-0.621,24.966-1.395,50.09-1.861,75.367c20.314-0.312,40.784-0.623,61.41-0.778c8.063-30.082,20.781-78.775,16.438-109.172c-2.17-14.732-6.047-37.685-6.047-37.685s1.705-6.356,2.014-15.817c-8.372-20.78,4.964-36.131,2.016-74.745c0,0,10.547,26.982,11.167,32.253c3.411,24.968,2.481,26.209,16.282,57.999c8.22-3.563,16.439-6.975,24.659-10.387c-1.396-13.646-4.343-46.678-14.27-56.605c-4.961-22.331-4.806-45.126-12.869-67.923c0,0,5.272-15.041-3.877-35.046c-5.582-13.647-16.439-17.99-27.449-21.557c-11.32-2.792-33.961-16.438-33.961-16.438c-2.017-8.374-3.566-16.902-1.24-27.293c0,0,4.652-11.011,10.234-18.764c5.583-7.909,6.668-31.946-2.326-44.663C228.379-1.578,196.743-3.904,181.702,5.555z"/>
                <g name="large-intestine" style="cursor: pointer;">
                    <path fill="#FDCC8A" d="M163.868,264.998l-2.481,2.638c0,0,2.171,8.994,2.171,12.868c0,3.723,0.931,6.205,3.102,9.306c2.172,3.256,1.862,7.135,1.862,7.135s0.931,2.944,4.032,6.049c3.256,3.101,6.978,7.597,6.978,7.597l14.577-10.701l-2.947-6.201c0,0-5.426-6.204-6.513-12.561c-1.086-6.36-3.877-16.902-3.877-16.902l-2.016-0.623L163.868,264.998z"/>
                </g>
                <g name="small-intestine" style="cursor: pointer;">
                    <path fill="#FDCC8A" d="M255.982,258.949c0,0-1.859-2.945-4.342-2.945c-2.638,0-4.652,1.241-4.652,3.256c0,2.016,0.62,8.22-0.931,11.632c-1.396,3.412-8.687,10.698-8.687,10.698s-3.564,2.637-4.497,5.43c-1.238,2.79-1.394,6.513-0.463,8.994c0.931,2.482,2.79,5.271,4.96,6.356c2.018,1.241,5.43,0.156,5.43,0.156l4.188-3.412l-4.188,7.291l0.931,3.256c0,0,0.619,1.703,2.171,1.859c1.859,0.467,3.721-1.242,3.721-1.242l6.359-12.867c0,0,1.861-8.688,1.24-11.17c-0.62-2.481-0.93-3.719-2.638-4.341c-1.703-0.463-5.892,0.622-5.892,0.622s6.048-8.063,6.048-12.716C254.741,264.842,255.982,258.949,255.982,258.949z"/>
                </g>
                <g name="spleen" style="cursor: pointer;">
                    <path fill="#CA7B41" d="M251.021,226.074c0,0-0.466,0.309-1.242,2.326c-0.464,1.859,0.31,3.878,0.933,5.893c0.774,2.171,2.015,7.442,2.015,7.442l0.619,3.566c0,0,3.104-0.462,3.723-4.341c0.62-3.723-0.156-10.235-1.55-12.251C254.275,226.695,251.021,226.074,251.021,226.074z"/>
                </g>
                <g name="kidney" style="cursor: pointer;">
                    <path fill="#CA7B41" d="M234.737,222.042c0,0-2.793-1.241-5.893,0.465c-3.104,1.861-3.567,5.118-3.567,6.822c0,1.864,1.396,7.135,2.791,8.376c1.395,1.396-0.153,6.201-0.153,6.201l0.153,2.793c0,0-2.327,0.155-2.327,3.256c0,2.793-0.929,4.186-0.309,6.512c0.776,2.482,2.946,5.275,5.115,6.049c2.173,0.931,7.135,0.467,8.841-0.462c1.705-0.778,6.823-10.084,6.823-17.992c0-8.064-3.721-17.522-5.736-19.228C238.459,222.972,234.737,222.042,234.737,222.042z"/>
                </g>
                <g name="lung" style="cursor: pointer;">
                    <path fill="#E6E6E6" d="M178.29,129.617c-17.058,2.015-27.448,28.378-29.465,42.49c-1.86,13.493-2.791,31.946-0.775,42.027c2.016,10.235,10.079,20.004,10.079,20.004s24.037-29.621,29.775-33.341c5.426-3.723,8.375-8.84,8.375-13.491c0-4.808,1.704-37.375,1.704-37.375C194.417,144.04,187.44,128.531,178.29,129.617z"/>
                    <path fill="#E6E6E6" d="M209.46,134.89c0,0,3.256-10.39,10.699-9.926c21.091,1.241,39.546,44.816,41.872,62.03c0.153,1.55,3.101,40.94-7.6,36.754c0,0-6.203-8.376-13.802-11.476C211.942,201.417,213.181,160.786,209.46,134.89z"/>
                </g>
                <g name="liver" style="cursor: pointer;">
                    <path fill="#FE9929" d="M235.356,206.07c0,0-10.854,8.373-15.353,12.094c-8.062,6.67-4.962,13.029-19.383,12.096c0,0-0.467,4.964-4.808,7.446c-4.497,2.482-19.539,11.787-22.021,14.731c-2.481,3.412-6.667,11.632-9.925,9.617c-3.256-2.02-10.39-17.063-6.667-33.964c4.341-20.626,21.555-35.667,44.039-31.17c3.412,0.774,9.15,1.55,9.15,1.55C219.074,197.694,230.238,197.694,235.356,206.07z"/>
                </g>
                <g name="heart" style="cursor: pointer;">
                    <path fill="#FD8646" d="M198.14,150.863l-6.668,8.529c0,0-1.551,11.165,0.31,17.058c2.326,6.203,2.326,10.388,8.529,13.026c6.358,2.792,17.058,7.133,23.261,7.133c6.358,0,9.303-0.31,9.926-3.412c0.618-3.1,0.773-13.956-2.171-18.299c-2.947-4.341-11.322-18.299-11.322-18.299l-0.465-5.271l-13.803-3.412L198.14,150.863z"/>
                </g>
            </svg>`;

            document.getElementById('svg-diagram').innerHTML = svgContent;

            // Interactive functionality
            var active_organ = null;

            // Handle SVG organ clicks
            document.querySelectorAll('g[name]').forEach(function(element) {
                element.addEventListener('click', function() {
                    var organ = this.getAttribute('name');
                    highlightOrgan(organ);
                });

                element.addEventListener('mouseenter', function() {
                    var organ = this.getAttribute('name');
                    highlightOrgan(organ, true);
                });

                element.addEventListener('mouseleave', function() {
                    if (active_organ) {
                        highlightOrgan(active_organ);
                    } else {
                        clearHighlight();
                    }
                });
            });

            // Handle chart interactions
            chart.on('mouseover', function(params) {
                highlightOrgan(params.name, true);
            });

            chart.on('mouseout', function(params) {
                if (active_organ) {
                    highlightOrgan(active_organ);
                } else {
                    clearHighlight();
                }
            });

            chart.on('click', function(params) {
                highlightOrgan(params.name);
            });

            function highlightOrgan(organ, is_hover = false) {
                if (!is_hover) {
                    active_organ = active_organ === organ ? null : organ;
                }

                // Update chart highlight
                chart.dispatchAction({
                    type: 'highlight',
                    seriesIndex: 0,
                    name: organ
                });

                // Update SVG highlight
                document.querySelectorAll('g[name]').forEach(function(element) {
                    element.classList.remove('highlighted');
                    if (element.getAttribute('name') === organ) {
                        element.classList.add('highlighted');
                    }
                });
            }

            function clearHighlight() {
                chart.dispatchAction({
                    type: 'downplay',
                    seriesIndex: 0
                });

                document.querySelectorAll('g[name]').forEach(function(element) {
                    element.classList.remove('highlighted');
                });
            }

            // Make chart responsive
            window.addEventListener('resize', function() {
                chart.resize();
            });
        </script>
    </body>
    </html>
    """

    return combined_html


def _extract_javascript_from_html(html_content):
    """Extracts JavaScript content from HTML using BeautifulSoup.

    Args:
        html_content: HTML string containing script tags

    Returns:
        str: JavaScript content without script tags
    """
    
    # Parse the HTML content
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find the body tag first
    body_tag = soup.find('body')
    if body_tag is not None:
        # Find only the first script tag in the body
        first_script = body_tag.find('script')
        if first_script is not None and hasattr(first_script, 'string') and first_script.string:
            return first_script.string.strip()
    
    # Fallback: if no body or script in body, find first script anywhere
    first_script = soup.find('script')
    if first_script is not None and hasattr(first_script, 'string') and first_script.string:
        return first_script.string.strip()
    
    return ""


def get_bar_chart_data():
    """Gets bar chart data with chart_id and javascript.

    Returns:
        ChartData: ChartData object containing chart_id and javascript
    """
    bar = create_bar_chart()
    html_content = bar.render_embed()
    return ChartData(
        chart_id=bar.get_chart_id(),
        javascript=_extract_javascript_from_html(html_content),
        chart_type="bar"
    )


def get_dag_diagram_data():
    """Gets DAG diagram data with chart_id and javascript.

    Returns:
        ChartData: ChartData object containing chart_id and javascript
    """
    graph = create_dag_diagram()
    html_content = graph.render_embed()
    return ChartData(
        chart_id=graph.get_chart_id(),
        javascript=_extract_javascript_from_html(html_content),
        chart_type="dag"
    )


def get_pie_chart_data():
    """Gets pie chart data with chart_id and javascript.

    Returns:
        ChartData: ChartData object containing chart_id and javascript
    """
    pie = create_pie_chart()
    html_content = pie.render_embed()
    return ChartData(
        chart_id=pie.get_chart_id(),
        javascript=_extract_javascript_from_html(html_content),
        chart_type="pie"
    )


def get_line_chart_data():
    """Gets line chart data with chart_id and javascript.

    Returns:
        ChartData: ChartData object containing chart_id and javascript
    """
    line = create_line_chart()
    html_content = line.render_embed()
    return ChartData(
        chart_id=line.get_chart_id(),
        javascript=_extract_javascript_from_html(html_content),
        chart_type="line"
    )

  
