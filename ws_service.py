from fastapi import WebSocket, WebSocketDisconnect
from typing import List, Dict, Any
import logging
import json
from datetime import datetime


logger = logging.getLogger(__name__)


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str = ""):
        await websocket.accept()
        self.active_connections.append(websocket)

        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(websocket)

        logger.info(
            f"WebSocket connected. Total connections: {len(self.active_connections)}"
        )

    def disconnect(self, websocket: WebSocket, user_id: str = ""):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

        if user_id and user_id in self.user_connections:
            if websocket in self.user_connections[user_id]:
                self.user_connections[user_id].remove(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]

        logger.info(
            f"WebSocket disconnected. Total connections: {len(self.active_connections)}"
        )

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")

    async def send_to_user(self, message: str, user_id: str):
        if user_id in self.user_connections:
            disconnected_sockets = []
            for websocket in self.user_connections[user_id]:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected_sockets.append(websocket)

            for socket in disconnected_sockets:
                self.disconnect(socket, user_id)

    async def broadcast(self, message: str):
        disconnected_sockets = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected_sockets.append(connection)

        for socket in disconnected_sockets:
            self.disconnect(socket)

    async def broadcast_update(self, update_type: str, data: Dict[str, Any], user_id: str):
        message = {
            "type": update_type,
            "data": data,
            "timestamp": datetime.now().isoformat(),
        }

        json_message = json.dumps(message)

        if user_id:
            await self.send_to_user(json_message, user_id)
        else:
            logger.warning(f"canot send message without user_id: {json_message}")
