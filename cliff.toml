[changelog]
# changelog header
header = """
# Changelog

All notable changes to this project will be documented in this file.
"""

# template for the changelog body
# https://tera.netlify.app/docs/#introduction
body = """
{% for commit in commits %}
{% if commit.message %}
- {{ commit.message | upper_first | trim_end }}{% if commit.id %} ({{ commit.id | truncate(length=7, end="") }}){% endif %}
{% endif %}
{% endfor %}
"""

# remove the leading and trailing whitespace from the template
trim = true

# changelog footer
footer = ""

[git]
# parse the commits based on https://www.conventionalcommits.org
conventional_commits = false
# filter out the commits that are not conventional
filter_unconventional = false
# process each line of a commit as an individual commit
split_commits = false
# regex for preprocessing the commit messages
commit_preprocessors = [
    { pattern = '\((\w+\s)?#([0-9]+)\)', replace = "([#${2}]({{ repoUrl }}/issues/${2}))" },
]
# regex for parsing and grouping commits
commit_parsers = [
    { message = ".*", group = "default" }
]
# protect breaking changes from being skipped due to matching a skipping commit_parser
protect_breaking_commits = false
# filter out the commits that are not matched by commit parsers
filter_commits = false
# glob pattern for matching git tags
tag_pattern = ".*"  # Match all tags with proper regex
# regex for skipping tags
skip_tags = ""
# regex for ignoring tags
ignore_tags = ""
# sort the tags topologically
topo_order = false
# sort the commits inside sections by oldest/newest order
sort_commits = "newest"
# limit the number of commits included in the changelog
limit_commits = false 