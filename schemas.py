from pydantic import BaseModel, Field, ConfigDict, field_validator
from datetime import datetime
from typing import List, Optional, Any, Dict, ClassVar

from fastapi import Form, File, UploadFile, Query
from uuid import UUID


class UserStats(BaseModel):
    report_count: int
    record_count: int
    biomarker_count: int
    medication_count: int
    procedure_count: int
    diagnosis_count: int
    genetics_count: int

class RecordCounts(BaseModel):
    """Response model for record counts by user_id and record_id"""
    
    record_id: str
    biomarkers: int
    diagnosis: int
    genetics: int
    procedures: int

class UserRegistration(BaseModel):
    bubble_id: str
    email: str
    develop_mode: Optional[bool] = False
    is_admin: Optional[bool] = False


class User(BaseModel):
    id: UUID
    bubble_id: str
    email: str
    develop_mode: bool
    bucket_name: str
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class LoincRecord(BaseModel):
    loinc_num: str
    component: str
    property: str
    time_aspct: str
    system: str
    scale_typ: Optional[str]
    method_typ: Optional[str]
    class_: str
    classtype: int
    long_common_name: str
    shortname: Optional[str]
    external_copyright_notice: Optional[str]
    status: Optional[str]
    version_first_released: Optional[str]
    version_last_changed: Optional[str]
    similarity_score: Optional[float] = None  # Added for trigram search results

    model_config = ConfigDict(from_attributes=True)

class CanonicalBiomarkerBase(BaseModel):
    """Base schema for canonical biomarkers"""
    user_id: UUID
    canonical_name: str
    standard_unit: Optional[str] = None
    reference_range_min: Optional[float] = None
    reference_range_max: Optional[float] = None
    health_areas: Optional[List[str]] = None
    group_name: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

class CanonicalBiomarkerCreate(CanonicalBiomarkerBase):
    """Schema for creating canonical biomarkers"""

class CanonicalBiomarkerUpdate(BaseModel):
    """Schema for updating canonical biomarkers"""
    canonical_name: Optional[str] = None
    standard_unit: Optional[str] = None
    reference_range_min: Optional[float] = None
    reference_range_max: Optional[float] = None
    health_areas: Optional[List[str]] = None
    group_name: Optional[str] = None

class CanonicalBiomarker(CanonicalBiomarkerBase):
    """Schema for canonical biomarker responses"""
    id: UUID
    member_count: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class BulkCanonicalBiomarkerCreate(BaseModel):
    user_id: UUID
    canonical_biomarkers: List[CanonicalBiomarkerCreate]  

class BiomarkerCanonicalUpdate(BaseModel):
    user_biomarker_ids: list[UUID]
    canonical_id: UUID

class BulkUserBiomarkerCanonicalUpdate(BaseModel):
    updates: List[BiomarkerCanonicalUpdate]  # e.g. [{"user_biomarker_id": ..., "canonical_id": ...}]
# Canonical Biomarker Response Schema (Global entities - no user_id required)

class UserFileResponse(BaseModel):
    id: str
    status: str = "accepted"
    updated_at: datetime = datetime.now()


class AcceptRequestResponse(BaseModel):
    id: str
    status: str = "accepted"
    user_id: UUID
    updated_at: datetime = datetime.now()


class N1Request(BaseModel):
    id: Optional[UUID | str] = None
    user_id: UUID
    url: Optional[str] = None
    progress: Optional[int] = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    error: Optional[str] = None
    message: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)


class ReportProcessingStatus(BaseModel):
    id: str
    status: str
    error: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)


class MedicalRecordConfiguration(BaseModel):
    parser_flow: Optional[str] = Field(
        default="Sequential",
        description="Parser flow type, e.g., Functional or Langroid",
    )
    model: str
    verbosity: str
    version: str
    ref_unit_system: str
    model_config = ConfigDict(from_attributes=True)


class ReportConfiguration(BaseModel):
    report_name: str
    report_format: Optional[str] = "pdf"
    report_style: Optional[str] = "casual"
    model_name: Optional[str] = "o3-mini"
    report_language: Optional[str] = "english"
    temperature: Optional[float] = Field(default=1.0, ge=0.0, le=2.0)
    custom_prompt: Optional[str] = ""
    report_flow: Optional[str] = "Template"
    model_config = ConfigDict(from_attributes=True)


class ReportPrompts(BaseModel):
    technical_name: str
    prompt_function_name: str
    prompt_text: str
    selected_models: str
    arn: str
    version: str
    temperature: float = Field(default=0.2, ge=0.0, le=1.0)
    placeholder_count: int = 1
    text_updated: datetime


class N1RecordUpdateRequest(N1Request):
    status: Optional[str] = "PENDING"
    type: Optional[str] = "RECORD"  # or "QUESTIONNAIRE"
    version: Optional[str] = None
    file_name: Optional[str] = None
    message: Optional[str] = None
    config: Optional[MedicalRecordConfiguration] = None
    # Counter fields for integrated counts
    biomarkers_count: Optional[int] = None
    diagnosis_count: Optional[int] = None
    genetics_count: Optional[int] = None
    procedures_count: Optional[int] = None


class N1ReportGenRequest(N1Request):
    workflow_name: str = "Langroid"
    workflow_id: str = "1"
    version: str = "Langroid-GCP/4dbbcah"
    report_language: str = "english"
    config: Optional[ReportConfiguration] = None
    prompts: List[ReportPrompts] = Field(default_factory=list, description="List of report prompts")
    status: Optional[str] = None


class ClinicalDataDeleted(BaseModel):
    biomarker_id: UUID
    record_id: str
    user_id: UUID
    excluded: bool = True


class ClinicalData(BaseModel):
    id: UUID
    record_id: str
    user_id: UUID
    year: int
    month: int
    day: int
    test_name: str
    result: str
    reference_range: Optional[str]
    sample_source: Optional[str]
    method: Optional[str]
    unit: Optional[str]
    filename: str
    context: Optional[str]
    #ToDo : Remove Loinc
    loinc_standard_name: Optional[str]
    loinc_code: Optional[str]
    canonical_name: Optional[str]
    health_areas: Optional[List[str]] = None
    edited: bool = False
    created_at: datetime
    updated_at: datetime
    page_number: int
    additional_data: Optional[Dict[str, Any]] = None
    model_config = ConfigDict(from_attributes=True)


class ClinicalDataUpdateRequest(BaseModel):
    year: Optional[int] = None
    month: Optional[int] = None
    day: Optional[int] = None
    test_name: Optional[str] = None
    result: Optional[str] = None
    reference_range: Optional[str] = None
    unit: Optional[str] = None
    context: Optional[str] = None
    #ToDo : remove loinc
    loinc_standard_name: Optional[str] = None
    loinc_code: Optional[str] = None
    canonical_name: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
    
    
class DBRowDeletionStatus(BaseModel):
    records_deleted: bool
    reports_deleted: bool


class UserDeletionResponse(BaseModel):
    status: str
    user_id: str
    database: Optional[DBRowDeletionStatus] = None
    files_deleted: Optional[int] = 0  # For the storage service result
    bucket_deleted: bool = False
    user_deleted: bool = False
    message: Optional[str] = None


class ProgressUpdateRequest(BaseModel):
    user_id: UUID = Field(description="User identifier")
    progress: int = Field(ge=0, le=100, description="Progress value between 0 and 100")
    status: str = Field(description="Status of the record")
    error: Optional[str] = Field(None, description="Error message if any")
    message: Optional[str] = Field(
        None, description="Additional message or information"
    )
    version: Optional[str] = Field(None, description="Version of Workflow")


class RecordProgressUpdateRequest(ProgressUpdateRequest):
    record_id: str = Field(description="Record identifier")


class ReportProgressUpdateRequest(ProgressUpdateRequest):
    report_id: str = Field(description="Report identifier")

class BatchCompletionRequest(BaseModel):
    """Schema for batch completion request"""
    user_id: UUID = Field(description="User identifier")
    batch_ids: List[str] = Field(description="List of batch identifiers to complete")

    model_config = ConfigDict(from_attributes=True)


class BatchCompletionResponse(BaseModel):
    """Schema for batch completion response"""
    status: str = Field(description="Overall operation status")
    user_id: UUID = Field(description="User identifier")
    completed_batches: List[str] = Field(description="List of successfully completed batch IDs")
    failed_batches: List[Dict[str, str]] = Field(description="List of failed batches with error messages")
    total_records_updated: int = Field(description="Total number of records updated")

    model_config = ConfigDict(from_attributes=True)


class QuestionResponse(BaseModel):
    id: Optional[UUID] = None
    question: str
    answer: str
    created_date: int|datetime  # Unix timestamp in milliseconds
    related_date: Optional[int|datetime] = None  # Optional Unix timestamp in milliseconds
    user_id: UUID
    category: str

    model_config = ConfigDict(from_attributes=True)


class UserProfile(BaseModel):
    user_id: UUID
    first_name: str
    last_name: str
    dob: Optional[datetime] = None
    weight_in_kg: Optional[float] = None
    height_in_cm: Optional[float] = None
    gender: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

class UserDetails(User):
    profile: Optional[UserProfile] = None

class UserMedicationBase(BaseModel):
    name: str
    brand_name: Optional[str] = None
    url: Optional[str] = None
    dosage: float
    unit: str
    type: str
    started_from: datetime
    stopped_on: Optional[datetime] = None
    frequency: str
    additional_data: Optional[Dict[str, Any]] = None
    reason: Optional[str] = None


class UserMedicationCreate(UserMedicationBase):
    user_id: UUID


class MedicationUpdate(BaseModel):
    name: Optional[str] = None
    brand_name: Optional[str] = None
    url: Optional[str] = None
    dosage: Optional[float] = None
    unit: Optional[str] = None
    type: Optional[str] = None
    started_from: Optional[datetime] = None
    stopped_on: Optional[datetime] = None
    frequency: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None
    reason: Optional[str] = None


class UserMedication(UserMedicationBase):
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class UserDiagnosisBase(BaseModel):
    name: Optional[str] = None
    icd_code: Optional[str] = None
    snomed_code: Optional[str] = None
    page_number: Optional[int] = None
    date_diagnosed: Optional[datetime] = None
    date_resolved: Optional[datetime] = None
    status: Optional[str] = None
    explanation: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)


class UserDiagnosisCreate(UserDiagnosisBase):
    user_id: UUID
    record_id: Optional[str] = None


class UserDiagnosisBulkCreate(BaseModel):
    user_id: UUID
    record_id: Optional[str] = None
    diagnoses: List[UserDiagnosisBase]


class UserDiagnosis(UserDiagnosisCreate):
    id: UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


class UserProcedureBase(BaseModel):
    name: Optional[str] = None
    cpt_code: Optional[str] = None
    page_number: Optional[int] = None
    date_performed: Optional[datetime] = None
    outcome: Optional[str] = None
    explanation: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None
    model_config = ConfigDict(from_attributes=True)


class UserProcedureCreate(UserProcedureBase):
    user_id: UUID
    record_id: Optional[str] = None


class UserProcedure(UserProcedureBase):
    id: UUID
    user_id: (
        UUID  # Added user_id as it's in the ORM and typically present in the full model
    )
    record_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


class UserProcedureBulkCreate(BaseModel):
    user_id: UUID
    record_id: Optional[str] = None
    procedures: List[UserProcedureBase]


class UserGeneticsBase(BaseModel):
    gene: Optional[str] = None
    allele: Optional[str] = None
    variant: Optional[str] = None
    prevalence: Optional[float] = None
    test_date: Optional[datetime] = None
    page_number: Optional[int] = None
    additional_data: Optional[Dict[str, Any]] = None
    model_config = ConfigDict(from_attributes=True)


class UserGeneticsCreate(UserGeneticsBase):
    user_id: UUID
    record_id: str


class UserGenetics(UserGeneticsBase):
    id: UUID
    user_id: UUID
    record_id: str
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


class UserGeneticsBulkCreate(BaseModel):
    user_id: UUID
    record_id: str
    genetics: List[UserGeneticsBase]
# Base class for common query parameters
class BaseQueryParams(BaseModel):
    """Base class for common pagination and sorting parameters"""
    # mandatory requesting user id
    user_id: UUID = Field(..., description="Filter by user ID")

    # Pagination
    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    page_size: int = Field(default=20, ge=1, le=1000, description="Number of items per page")

    # Sorting
    sort_by: Optional[str] = Field(default="created_at", description="Field to sort by")
    is_descending: bool = Field(default=False, description="Sort order: True for descending, False for ascending (default)")

    # Common filtering
    record_id: Optional[str] = Field(default=None, description="Filter by record ID")


# Specialized QueryParams classes inheriting from BaseQueryParams
class DiagnosisQueryParams(BaseQueryParams):
    # Diagnosis-specific filtering
    status: Optional[str] = Field(default=None, description="Filter by diagnosis status")
    date_diagnosed_from: Optional[datetime] = Field(default=None, description="Filter diagnoses from this date (ISO format)")
    date_diagnosed_to: Optional[datetime] = Field(default=None, description="Filter diagnoses up to this date (ISO format)")
    date_resolved_from: Optional[datetime] = Field(default=None, description="Filter resolved diagnoses from this date (ISO format)")
    date_resolved_to: Optional[datetime] = Field(default=None, description="Filter resolved diagnoses up to this date (ISO format)")
    name: Optional[str] = Field(default=None, description="Filter by diagnosis name (partial match)")
    icd_code: Optional[str] = Field(default=None, description="Filter by ICD code")
    snomed_code: Optional[str] = Field(default=None, description="Filter by SNOMED code")


class GeneticsQueryParams(BaseQueryParams):
    # Genetics-specific filtering
    gene: Optional[str] = Field(default=None, description="Filter by gene name (partial match)")
    allele: Optional[str] = Field(default=None, description="Filter by allele")
    variant: Optional[str] = Field(default=None, description="Filter by variant")
    test_date_from: Optional[datetime] = Field(default=None, description="Filter genetics from this test date (ISO format)")
    test_date_to: Optional[datetime] = Field(default=None, description="Filter genetics up to this test date (ISO format)")


class ProcedureQueryParams(BaseQueryParams):
    # Procedure-specific filtering
    name: Optional[str] = Field(default=None, description="Filter by procedure name (partial match)")
    cpt_code: Optional[str] = Field(default=None, description="Filter by CPT code")
    outcome: Optional[str] = Field(default=None, description="Filter by outcome (partial match)")
    date_performed_from: Optional[datetime] = Field(
        default=None, description="Filter procedures from this date (ISO format)"
    )
    date_performed_to: Optional[datetime] = Field(
        default=None, description="Filter procedures up to this date (ISO format)"
    )


class MedicationQueryParams(BaseQueryParams):
    # Medication-specific filtering
    name: Optional[str] = Field(default=None, description="Filter by medication name (partial match)")
    brand_name: Optional[str] = Field(default=None, description="Filter by brand name (partial match)")
    type: Optional[str] = Field(default=None, description="Filter by medication type")
    unit: Optional[str] = Field(default=None, description="Filter by dosage unit")
    frequency: Optional[str] = Field(default=None, description="Filter by frequency (partial match)")
    reason: Optional[str] = Field(default=None, description="Filter by reason (partial match)")
    started_from_date: Optional[datetime] = Field(
        default=None, description="Filter medications started from this date (ISO format)"
    )
    started_to_date: Optional[datetime] = Field(
        default=None, description="Filter medications started up to this date (ISO format)"
    )
    stopped_from_date: Optional[datetime] = Field(
        default=None, description="Filter medications stopped from this date (ISO format)"
    )
    stopped_to_date: Optional[datetime] = Field(
        default=None, description="Filter medications stopped up to this date (ISO format)"
    )

class BiomarkerQueryParams(BaseQueryParams):
    model_config = {"extra": "forbid"}

    # Biomarker-specific filtering
    test_name: Optional[str] = Field(default=None, description="Filter by test name (partial match)")
    result: Optional[str] = Field(default=None, description="Filter by result (partial match)")
    unit: Optional[str] = Field(default=None, description="Filter by unit")
    sample_source: Optional[str] = Field(default=None, description="Filter by sample source")
    method: Optional[str] = Field(default=None, description="Filter by method")
    canonical_id: Optional[UUID] = Field(default=None, description="Filter by canonical ID")
    test_date_from: Optional[datetime] = Field(default=None, description="Filter biomarkers from this test date (ISO format)")
    test_date_to: Optional[datetime] = Field(default=None, description="Filter biomarkers up to this test date (ISO format)")
    health_areas: Optional[str] = Field(default=None, description="Filter by health areas (single)")


class ChatInitRequest(BaseModel):
    user_id: UUID = Field(..., description="User identifier")
    chr_id: str = Field(..., description="CHR identifier")

class ChatRequest(ChatInitRequest):
    """
    Request model for chat requests
    """

    message: str = Field(..., description="Message to send to the chat")
    stream: bool = Field(True, description="Whether to stream responses")
    model_config = ConfigDict(from_attributes=True)


class ChatInitResponse(ChatInitRequest):
    """
    Response model for chat responses
    """

    id: str = Field(..., description="Unique identifier for the response")
    chunks: int = Field(..., description="Number of chunks processed")
    collection_name: str = Field(..., description="Name of collection")
    url: str = Field(..., description="URL used for processing")
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp of when the message was created",
    )
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp of when the message was last updated",
    )

class ProcedureResponse(BaseModel):
    status: str
    user_id: str
    fields: List[str]
    data: List[UserProcedure]

    model_config = ConfigDict(from_attributes=True)

class DiagnosisResponse(BaseModel):
    status: str
    user_id: str
    fields: List[str]
    data: List[UserDiagnosis]

    model_config = ConfigDict(from_attributes=True)


class UserBiomarkerBase(BaseModel):
    test_name: Optional[str] = None
    expanded_test_name: Optional[str] = None
    result: Optional[str] = None
    reference_range: Optional[str] = None
    unit: Optional[str] = None
    context: Optional[str] = None
    canonical_id: Optional[UUID] = None  # Changed from str to UUID for type safety
    edited: bool = False
    excluded: bool = False
    test_date: Optional[datetime] = None
    sample_source: Optional[str] = None
    method: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None
    page_number: Optional[int] = None
    result_numeric: Optional[float] = None
    reference_range_min: Optional[float] = None
    reference_range_max: Optional[float] = None
    converted_result_numeric: Optional[float] = None
    converted_reference_range_max: Optional[float] = None
    converted_reference_range_min: Optional[float] = None
    out_of_range: Optional[bool] = None
    model_config = ConfigDict(from_attributes=True)

    @field_validator("canonical_id", mode="before")
    @classmethod
    def canonical_id_to_uuid(cls, value):
        if value is None:
            return value
        if isinstance(value, UUID):
            return value
        if isinstance(value, str):
            # Defensive: treat empty string, 'string', or 'null' as None
            if value.strip() == "" or value.strip().lower() in {"string", "null"}:
                return None
            try:
                return UUID(value)
            except Exception:
                return None  # fallback: treat as None if not a valid UUID
        return value

class UserBiomarkerCreate(UserBiomarkerBase):
    user_id: UUID
    record_id: str

class UserBiomarkerDeleted(UserBiomarkerBase):
    biomarker_id: UUID
    record_id: str
    user_id: UUID
    excluded: bool = True

class UserBiomarkerBulkCreate(BaseModel):
    user_id: UUID
    record_id: str
    biomarkers: List[UserBiomarkerBase]
    
class BiomarkerResponse(BaseModel):
    status: str
    user_id: UUID
    fields: List[str]
    data: List[UserBiomarkerBase]

    model_config = ConfigDict(from_attributes=True)


class UserBiomarker(UserBiomarkerBase):
    id: UUID
    user_id: UUID
    record_id: str
    created_at: datetime
    updated_at: datetime
    canonical_record: Optional[dict[str, Any]] = None

    @field_validator("canonical_id", mode="before")
    @classmethod
    def canonical_id_to_uuid(cls, value):
        if value is None:
            return None
        if isinstance(value, UUID):
            return value
        if isinstance(value, str):
            # Defensive: treat empty string, 'string', or 'null' as None
            if value.strip() == "" or value.strip().lower() in {"string", "null"}:
                return None
            try:
                return UUID(value)
            except Exception:
                return None
        return value

    model_config = ConfigDict(from_attributes=True)

class ChartData(BaseModel):
    chart_id: str
    javascript: str
    chart_type: str = Field(..., description="Type of chart - bar, line, pie, dag, or organ")
    data: Optional[Dict[str, Any]] = None  # Additional data for the chart if needed

    model_config = ConfigDict(from_attributes=True)


# Base class for paginated responses
class BasePaginatedResponse(BaseModel):
    """Base class for paginated response structure"""
    status: str
    user_id: UUID
    total_count: int
    page: int
    page_size: int
    total_pages: int
    model_config = ConfigDict(from_attributes=True)


class PaginatedDiagnosisResponse(BasePaginatedResponse):
    data: List[UserDiagnosis]


class PaginatedGeneticsResponse(BasePaginatedResponse):
    data: List[UserGenetics]


class PaginatedProcedureResponse(BasePaginatedResponse):
    data: List[UserProcedure]


class PaginatedMedicationResponse(BasePaginatedResponse):
    data: List[UserMedication]


class PaginatedBiomarkerResponse(BasePaginatedResponse):
    data: List[UserBiomarker]


class RecordQueryParams(BaseQueryParams):
    # Record-specific filtering
    status: Optional[str] = Field(default=None, description="Filter by record status (PENDING, PROCESSING, COMPLETED, etc.)")
    type: Optional[str] = Field(default=None, description="Filter by record type (RECORD, QUESTIONNAIRE)")
    file_name: Optional[str] = Field(default=None, description="Filter by filename (partial match)")
    batch_id: Optional[str] = Field(default=None, description="Filter by batch ID")
    progress_from: Optional[int] = Field(default=None, ge=0, le=100, description="Filter records with progress >= this value")
    progress_to: Optional[int] = Field(default=None, ge=0, le=100, description="Filter records with progress <= this value")
    created_at: Optional[datetime] = Field(default=None, description="Filter records created from this date (ISO format)")
    created_to: Optional[datetime] = Field(default=None, description="Filter records created up to this date (ISO format)")
    updated_at: Optional[datetime] = Field(default=None, description="Filter records updated from this date (ISO format)")
    updated_to: Optional[datetime] = Field(default=None, description="Filter records updated up to this date (ISO format)")


class PaginatedRecordResponse(BasePaginatedResponse):
    data: List[N1RecordUpdateRequest]

class ReportQueryParams(BaseQueryParams):
    status: Optional[str] = Field(default=None, description="Filter by report status")
    progress: Optional[int] = Field(default=None, description="Filter reports with progress between 0 and 100")
    report_id: Optional[str] = Field(default=None, description="Filter by report ID")
    file_name: Optional[str] = Field(default=None, description="Filter by filename (partial match)")
    created_at: Optional[datetime] = Field(default=None, description="Filter reports created from this date (ISO format)")
    created_to: Optional[datetime] = Field(default=None, description="Filter reports created up to this date (ISO format)")
    updated_at: Optional[datetime] = Field(default=None, description="Filter reports updated from this date (ISO format)")
    updated_to: Optional[datetime] = Field(default=None, description="Filter reports updated up to this date (ISO format)")


class PaginatedReportResponse(BasePaginatedResponse):
    data: List[N1ReportGenRequest]

# Canonical Biomarker Query and Response Schemas
class CanonicalBiomarkerQueryParams(BaseQueryParams):
    """Query parameters for canonical biomarker pagination and filtering"""
    # Pagination
    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    page_size: int = Field(default=20, ge=1, le=1000, description="Number of items per page")

    # Sorting
    sort_by: Optional[str] = Field(default="created_at", description="Field to sort by")
    is_descending: bool = Field(default=False, description="Sort order: True for descending, False for ascending (default)")

    # Filtering parameters
    canonical_name: Optional[str] = Field(default=None, description="Filter by canonical name (partial match)")
    standard_unit: Optional[str] = Field(default=None, description="Filter by standard unit")
    group_name: Optional[str] = Field(default=None, description="Filter by group name")
    health_areas: Optional[str] = Field(default=None, description="Filter by health areas (comma-separated)")

    # Date range filtering
    created_from: Optional[datetime] = Field(default=None, description="Filter by creation date from")
    created_to: Optional[datetime] = Field(default=None, description="Filter by creation date to")

class PaginatedCanonicalBiomarkerResponse(BasePaginatedResponse):
    """Paginated response for canonical biomarkers (global entities)"""
    data: List[CanonicalBiomarker]
