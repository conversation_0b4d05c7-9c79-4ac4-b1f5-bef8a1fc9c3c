# .github/workflows/changelog.yml
name: Update Changelog

on:
  pull_request:
    types: [opened]  # Only run when PR is first created
    branches:
      - main
      - develop

permissions:
  contents: write
  pull-requests: write

jobs:
  update-changelog:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.head_ref }}

      - name: Fetch base branch for diff
        run: |
          BASE_BRANCH="${{ github.base_ref }}"
          # Fetch the base branch with full history
          git fetch origin $BASE_BRANCH:refs/remotes/origin/$BASE_BRANCH --unshallow || true
          # Ensure we have the full history of the current branch
          git fetch origin ${{ github.head_ref }} --unshallow || true

      - name: Generate changelog entry
        id: changelog
        run: |
          # Create changelog directory if it doesn't exist
          mkdir -p docs/changelog
          
          # Generate PR-specific changelog file
          PR_NUM=${{ github.event.pull_request.number }}
          CHANGELOG_FILE="docs/changelog/pr-$PR_NUM.md"
          
          {
            echo "## Changes"
            echo ""
            # Get the merge base commit to ensure we only show commits unique to this PR
            MERGE_BASE=$(git merge-base origin/${{ github.base_ref }} HEAD)
            if [ -z "$MERGE_BASE" ]; then
              echo "Warning: Could not find merge base, showing all commits"
              git log --no-merges --pretty=format:"- %s" origin/${{ github.base_ref }}..HEAD
            else
              git log --no-merges --pretty=format:"- %s" $MERGE_BASE..HEAD
            fi
            echo ""
            echo "## PR Details"
            echo "- PR #$PR_NUM"
            echo "- Author: ${{ github.event.pull_request.user.login }}"
            echo "- Target Branch: ${{ github.base_ref }}"
          } > "$CHANGELOG_FILE"

      - name: Commit & push changelog entry
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git add docs/changelog/
          git commit -m "chore: add changelog entry for PR ${{ github.event.pull_request.number }}" || echo "no changes to commit"
          git push origin HEAD:${{ github.head_ref }}

      - name: Export changelog for PR comment
        id: export
        run: |
          echo "CHANGELOG<<EOF" >> $GITHUB_ENV
          cat "docs/changelog/pr-${{ github.event.pull_request.number }}.md" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Comment PR with preview
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const cl = process.env.CHANGELOG.trim();
            const body = ['## 📝 Changelog Preview', '```markdown', cl, '```'].join('\n');
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });
            const prev = comments.find(c =>
              c.user.login === 'github-actions[bot]' &&
              c.body.includes('## 📝 Changelog Preview')
            );
            if (prev) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: prev.id,
                body
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body
              });
            }