FROM python:3.12-slim

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

COPY pyproject.toml .
COPY README.md .
COPY . .

# Install pip tools
RUN pip install --no-cache-dir pip setuptools wheel

# Install project dependencies
RUN pip install --no-cache-dir -e .

# Environment variables
ENV PORT=8080
ENV BUCKET_NAME=""

EXPOSE ${PORT}

CMD ["sh", "-c", "uvicorn main:app --host 0.0.0.0 --port $PORT"]