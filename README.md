# N1 API

FastAPI-based service that acts as the orchestrator of all AI workflows. Mainly two types of workflows

- **Records** : preprocessing of medical records that user uploads as files
- **Reports** : Generate Comprehensive Medical Report for a user based on available medical records

## Table schemas
The following table schemas are used in the application:
[Schemas](./SCHEMAS.md)

## Features

- PDF generation from Markdown content
- Google Cloud Storage integration
- Signed URL generation for secure file access
- Docker containerization
- Background task processing
- User data management (deletion of all user data)
- Clinical data management (update, soft delete)
- Biomarker retrieval and filtering
- Record and report deletion
- Medication management (create, retrieve, update, delete)

## Setup

### Prerequisites

- Python 3.12+
- Google Cloud Storage credentials (shared via Dashlane)
- Postgres db (the app will need access to a postgress db, the tables will be created automatically first time on a fresh db)
- Docker (only required for deployment)
- Python dependencies are managed via uv , please refer section **Development Environment Using uv** and saved in pyproject.toml

### Environment Variables

The application uses the following environment variables:

| Variable | Description | Used By |
|----------|-------------|---------|
| N1_API_HEADER_NAME | Header name for N1 API authentication | Cloud Run, Batch Run, AWS Batch |
| N1_API_KEY | API key for N1 API authentication | Cloud Run, Batch Run, AWS Batch |
| N1_USERNAME | Username for N1 API authentication | Data Debug console |
| N1_PASSWORD | Password for N1 API authentication | Data Debug console |
| N1_API_BASE_URL | Base URL for N1 API to connect back for status updates| Batch Run, AWS Batch |
| BUBBLE_API_BASE_URL | Base URL for Bubble API | services module uses this to sync updates to bubble app |
| BUBBLE_BIOMARKER_SYNC_ENDPOINT | Endpoint for biomarker synchronization | data processing jobs use this to send sync biomarkers request |
| BUBBLE_DIAGNOSIS_SYNC_ENDPOINT | Endpoint for diagnosis synchronization | data processing jobs use this to send sync diagnosis request |
| BUBBLE_PROCEDURE_SYNC_ENDPOINT | Endpoint for procedure synchronization | data processing jobs use this to send sync procedure request |
| BUBBLE_RECORD_STATUS_ENDPOINT | Endpoint for record status updates | data processing jobs use this to send update status of records |
| BUBBLE_REPORT_STATUS_ENDPOINT | Endpoint for report status updates | chr generation jobs use this to send update status of reports |
| DATABASE_URL | Database connection URL | Cloud Run |
| DEV_BUCKET_NAME | Development bucket name for GCS | this is the default bucket used for dev users , dev flag is true|
| GCP_SERVICE_JSON | GCP service account JSON  for accessing cloud storage bucket| Cloud Run, Batch Run |
| PROJECT_ID | GCP project ID used to get cloud run job handles| Cloud Run, Batch Run |
| REGION | GCP region for default cloud run job location | Cloud Run |
| SEQUENTIAL_REPORT_GENERATION_JOB_NAME | Job name for sequential report generation | Cloud Run |
| TEMPLATE_REPORT_GENERATION_JOB_NAME | Job name for template report generation | Cloud Run |
| BIOMARKERS_TABLE_NAME | Table name for biomarkers data | Cloud Run, Batch Run, AWS Batch |
| RECORDS_TABLE_NAME | Table name for record requests | Batch Run, AWS Batch |
| FILE_NAME_COLUMN | Column name for file names | Batch Run, AWS Batch |
| OPENAI_API_KEY | API key for OpenAI | Cloud Run, Batch Run, AWS Batch |
| OPENAI_BASE_URL | Base URL for OpenAI API | Cloud Run, Batch Run, AWS Batch |
| ROUTER_MODEL | Model used for routing | Cloud Run (Langroid) |
| EXA_API_KEY | API key for Exa search | Cloud Run (Langroid) |
| FIRE_CRAWL_API_KEY | API key for Fire Crawl | Cloud Run (Langroid) |
| QDRANT_API_URL | URL for Qdrant vector database | Cloud Run (Langroid) |
| QDRANT_API_KEY | API key for Qdrant | Cloud Run (Langroid) |
| MODEL | Default AI model | Batch Run, AWS Batch |
| DB_HOST | Database host | Batch Run |
| DB_HOST_PUBLIC | Public database host | AWS Batch |
| DB_USERNAME | Database username | Batch Run, AWS Batch |
| DB_PASSWORD | Database password | Batch Run, AWS Batch |
| DATABASE_NAME | Database name | Batch Run, AWS Batch |

### Development Environment Using uv

[uv](https://github.com/astral-sh/uv) is a fast Python package installer and resolver that can be used as an alternative to pip. It's particularly useful for development environments due to its speed and reliability.

#### Installation

If you don't have uv installed, please go through the installation steps for your OS [here](https://github.com/astral-sh/uv)

#### Setup Development Environment

To set up your development environment using uv:

```shell
# Sync all dependencies from pyproject.toml
uv sync
```

### Authentication

The N1 API uses Api Key authentication to protect the endpoints and right now the API key is set in the environment and will need to be rotated during deployments

#### API Key Authentication

For Cloud Run, Batch Run, and AWS Batch services, the API uses header-based authentication:

1. Set the `N1_API_HEADER_NAME` environment variable to define the header name that will contain the API key
2. Set the `N1_API_KEY` environment variable with your API key value
3. Include this header in all API requests:

```python
f"{N1_API_HEADER_NAME}": f"{N1_API_KEY}"
```

#### Username/Password Authentication

For the Data Analysis console:

1. Set the `N1_USERNAME` environment variable with your username
2. Set the `N1_PASSWORD` environment variable with your password
3. Access the data analysis console using these credentials when prompted

Please note this is a testing feature and might be removed in future updates.

#### Running the Application

First rename the env.sh.example to env.sh and update the variables to appropriate values

```shell
# source the ENV variables
source env.sh
# run db migrations
uv run alembic upgrade head
# Run the application using uv
uv run uvicorn main:app --reload --port 8080

```

## API Endpoints

### User Management

DELETE /users/{user_id}/data

Deletes all user data from the system, including records, reports, and files.

Response:

```json
{
  "status": "string",
  "user_id": "string",
  "database": {
    "records_deleted": true,
    "reports_deleted": true
  },
  "files_deleted": 0,
  "bucket_deleted": false,
  "user_deleted": false,
  "message": "string"
}
```

### Generate Report

POST /reports/generate

Starts asynchronous PDF generation process.

Request body:

```json
{
  "user_id": "1739157792130x623229448165603600",
  "config": {
    "report_name": "My XyZ Report",
    "report_format": "pdf",
    "report_style": "casual",
    "model_name": "o1-mini",
    "report_language": "english",
    "custom_prompt": "there is nothing as good as this",
    "verbosity": "normal"
  }
}
```

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "status": "accepted",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

### Report Status

GET /reports/status

Returns the status of the report generation process.

Query parameters:

- user_id: string
- report_id: string

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "progress": 50,
  "status": "in_progress",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

### Get Report URL

GET /reports/url

Retrieves the URL of the generated report.

Query parameters:

- user_id: string
- report_id: string

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "url": "https://example.com/report.pdf",
  "status": "completed",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

### Delete Report

DELETE /reports/{report_id}

Deletes a report and its associated file from cloud storage.

Query parameters:

- user_id: string

Response:

```json
{
  "status": "success",
  "message": "Report {report_id} deleted successfully",
  "report_deleted": true,
  "file_deleted": true
}
```

### Upload Files

POST /records/add

Uploads files for a user.

Request:

```shell
curl -X POST "http://localhost:8080/records/add" \
  -F "user_id=123" \
  -F "file=@file1.pdf"
```

Response:

```json
{
  "record_id": "string",
  "status": "uploaded",
  "file_name": "file1.pdf"
}
```

### Record Status

GET /records/status

Returns the status of the record processing.

Query parameters:

- user_id: string
- record_id: string

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "status": "processed",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

### Get Biomarkers

GET /records/biomarkers

Retrieves all biomarkers for a user.

Query parameters:

- user_id: string

Response:

```json
{
  "status": "success",
  "user_id": "string",
  "fields": ["year", "month", "test_name", "result", "reference_range", "unit", "record_id", "loinc_code"],
  "data": [...]
}
```

### Get User Biomarkers

GET /records/user-biomarkers

Retrieves biomarkers for a user, optionally filtered by record.

Query parameters:

- user_id: string
- record_id: string (optional)

Response:

```json
{
  "status": "success",
  "user_id": "string",
  "fields": [...],
  "data": [...]
}
```

### Update Clinical Data

PUT /records/clinical-data/{user_id}/{record_id}/{clinical_data_id}

Updates a clinical data record for a user.

Request body:

```json
{
  "test_name": "string",
  "result": "string",
  "reference_range": "string",
  "unit": "string"
}
```

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "year": 2023,
  "month": 10,
  "day": 1,
  "test_name": "string",
  "result": "string",
  "reference_range": "string",
  "unit": "string",
  "record_id": "string",
  "loinc_code": "string",
  "loinc_standard_name": "string",
  "filename": "string",
  "context": "string",
  "created_at": "2023-10-10T10:10:10.000Z",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

### Delete Clinical Data

DELETE /records/clinical-data/{user_id}/{record_id}/{clinical_data_id}

Soft deletes a clinical data record (marks as excluded).

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "year": 2023,
  "month": 10,
  "day": 1,
  "test_name": "string",
  "result": "string",
  "reference_range": "string",
  "unit": "string",
  "record_id": "string",
  "loinc_code": "string",
  "loinc_standard_name": "string",
  "filename": "string",
  "context": "string",
  "created_at": "2023-10-10T10:10:10.000Z",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

### Delete Record

DELETE /records/{user_id}/{record_id}

Deletes a record and all associated clinical data.

Response:

```json
{
  "status": "success",
  "message": "Record {record_id} and all associated clinical data deleted successfully",
  "record_deleted": true,
  "clinical_data_deleted_count": 10,
  "file_deleted": true
}
```

### Medication Management

#### Create Medication

POST /medications

Creates a new medication record for a user.

Request body:

```json
{
  "user_id": "string",
  "name": "string",
  "brand_name": "string",
  "url": "string",
  "dosage": 10.0,
  "unit": "mg",
  "type": "pill",
  "started_from": "2023-10-10T10:10:10.000Z",
  "stopped_on": "2023-11-10T10:10:10.000Z",
  "frequency": "daily",
  "reason": "string"
}
```

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "name": "string",
  "brand_name": "string",
  "url": "string",
  "dosage": 10.0,
  "unit": "mg",
  "type": "pill",
  "started_from": "2023-10-10T10:10:10.000Z",
  "stopped_on": "2023-11-10T10:10:10.000Z",
  "frequency": "daily",
  "reason": "string",
  "created_at": "2023-10-10T10:10:10.000Z",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

#### Get User Medications

GET /medications

Retrieves all medications for a specific user.

Query parameters:

- user_id: string

Response:

```json
[
  {
    "id": "string",
    "user_id": "string",
    "name": "string",
    "brand_name": "string",
    "url": "string",
    "dosage": 10.0,
    "unit": "mg",
    "type": "pill",
    "started_from": "2023-10-10T10:10:10.000Z",
    "stopped_on": "2023-11-10T10:10:10.000Z",
    "frequency": "daily",
    "reason": "string",
    "created_at": "2023-10-10T10:10:10.000Z",
    "updated_at": "2023-10-10T10:10:10.000Z"
  }
]
```

#### Get Specific Medication

GET /medications/{medication_id}

Retrieves a specific medication for a user.

Query parameters:

- user_id: string

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "name": "string",
  "brand_name": "string",
  "url": "string",
  "dosage": 10.0,
  "unit": "mg",
  "type": "pill",
  "started_from": "2023-10-10T10:10:10.000Z",
  "stopped_on": "2023-11-10T10:10:10.000Z",
  "frequency": "daily",
  "reason": "string",
  "created_at": "2023-10-10T10:10:10.000Z",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

#### Update Medication

PATCH /medications/{medication_id}

Updates a medication record for a user.

Query parameters:

- user_id: string

Request body:

```json
{
  "name": "string",
  "brand_name": "string",
  "url": "string",
  "dosage": 15.0,
  "unit": "ml",
  "type": "liquid",
  "started_from": "2023-10-10T10:10:10.000Z",
  "stopped_on": "2023-11-10T10:10:10.000Z",
  "frequency": "twice daily",
  "reason": "string"
}
```

Response:

```json
{
  "id": "string",
  "user_id": "string",
  "name": "string",
  "brand_name": "string",
  "url": "string",
  "dosage": 15.0,
  "unit": "ml",
  "type": "liquid",
  "started_from": "2023-10-10T10:10:10.000Z",
  "stopped_on": "2023-11-10T10:10:10.000Z",
  "frequency": "twice daily",
  "reason": "string",
  "created_at": "2023-10-10T10:10:10.000Z",
  "updated_at": "2023-10-10T10:10:10.000Z"
}
```

#### Delete Medication

DELETE /medications/{medication_id}

Deletes a medication record for a user.

Query parameters:

- user_id: string

Response:

```json
{
  "status": "success",
  "message": "Medication {medication_id} deleted successfully"
}
```

## Project Structure

```shell
.
├── app/
│   ├── main.py                # Entry point for the FastAPI application
│   ├── process_pipeline.py    # Logic for PDF generation
│   ├── routes/
│   │   ├── records.py         # Endpoints for handling medical records
│   │   ├── reports.py         # Endpoints for generating reports
│   │   ├── users.py           # Endpoints for user management
│   │   └── medications.py     # Endpoints for medication management
│   ├── models.py              # ORM models for database interactions
│   ├── schemas.py             # Pydantic schemas for request and response validation
│   ├── services.py            # Service classes for processing requests
│   ├── storage.py             # Storage service for handling file operations
│   ├── dependencies.py        # Dependency injection methods
│   ├── database.py            # Database utilities and connections
│   └── config.py              # Configuration settings
├── Dockerfile                 # Docker configuration for containerization
└── pyproject.toml             # Project dependencies and settings

## Development

### Local Development

```shell
pip install -e .
alembic upgrade head
uvicorn main:app --reload --port 8080
```

## Deployment

The application is deployed using Google Cloud Platform services:

### Google Cloud Build

The code is built using Google Cloud Build, which automatically:

1. Pulls the source code from the repository
2. Builds a Docker image based on the Dockerfile
3. Pushes the Docker image to Google Container Registry

The build process is configured in in google and history of builds can be checked [here](https://console.cloud.google.com/cloud-build/builds?invt=Abutrg&project=n1-healthcare&supportedpurview=project) . Please look for builds for n1-api repository.

### Google Cloud Run

After the Docker image is built, it is deployed as a Google Cloud Run service [here](https://console.cloud.google.com/run/detail/us-central1/n1-api/metrics?invt=AbuymA&project=n1-healthcare), which provides:

- Automatic scaling based on traffic
- HTTPS endpoints
- Integration with other Google Cloud services
- Environment variable configuration for secrets and settings

This serverless deployment approach allows the API to scale efficiently based on demand while minimizing infrastructure management overhead.
