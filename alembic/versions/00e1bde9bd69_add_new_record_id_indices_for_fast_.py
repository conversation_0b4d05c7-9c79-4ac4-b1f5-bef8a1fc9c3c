"""add new record_id indices for fast filters

Revision ID: 00e1bde9bd69
Revises: ce8983f93ddf
Create Date: 2025-07-03 15:57:56.892325

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = '00e1bde9bd69'
down_revision: Union[str, None] = 'ce8983f93ddf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Convert user_id columns from VARCHAR to GUID and add foreign keys
    op.alter_column('record_requests', 'user_id',
               existing_type=sa.VARCHAR(),
               type_=models.GUID(),
               existing_nullable=True,
               postgresql_using='user_id::uuid')
    op.create_foreign_key("fk_rr_user_id", 'record_requests', 'users', ['user_id'], ['id'])
    
    op.alter_column('report_gen_requests', 'user_id',
               existing_type=sa.VARCHAR(),
               type_=models.GUID(),
               existing_nullable=True,
               postgresql_using='user_id::uuid')
    op.create_foreign_key("fk_rgr_user_id", 'report_gen_requests', 'users', ['user_id'], ['id'])
    
    # Update clinical_data table
    op.alter_column('clinical_data', 'record_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('clinical_data', 'user_id',
               existing_type=sa.VARCHAR(),
               type_=models.GUID(),
               existing_nullable=True,
               postgresql_using='user_id::uuid')
    op.create_foreign_key("fk_cd_rr_id", 'clinical_data', 'record_requests', ['record_id'], ['id'])
    op.create_foreign_key("fk_cd_user_id", 'clinical_data', 'users', ['user_id'], ['id'])
    
    # Add indices and foreign keys for user tables
    op.create_index(op.f('ix_user_diagnoses_record_id'), 'user_diagnoses', ['record_id'], unique=False)
    op.create_foreign_key("fk_ud_rr_id", 'user_diagnoses', 'record_requests', ['record_id'], ['id'])
    
    op.create_index(op.f('ix_user_genetics_record_id'), 'user_genetics', ['record_id'], unique=False)
    op.create_foreign_key("fk_ug_rr_id", 'user_genetics', 'record_requests', ['record_id'], ['id'])
    
    op.create_index(op.f('ix_user_procedures_record_id'), 'user_procedures', ['record_id'], unique=False)
    op.create_foreign_key("fk_up_rr_id", 'user_procedures', 'record_requests', ['record_id'], ['id'])
    
    # Update user_question_responses table
    op.add_column('user_question_responses', sa.Column('created_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True))
    op.add_column('user_question_responses', sa.Column('updated_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True))
    op.alter_column('user_question_responses', 'user_id',
               existing_type=sa.VARCHAR(),
               type_=models.GUID(),
               existing_nullable=True,
               postgresql_using='user_id::uuid')
    op.create_foreign_key("fk_uqr_user_id", 'user_question_responses', 'users', ['user_id'], ['id'])
    
    # Update user_profiles table
    op.alter_column('user_profiles', 'user_id',
               existing_type=sa.VARCHAR(),
               type_=models.GUID(),
               existing_nullable=False,
               postgresql_using='user_id::uuid')
    op.create_foreign_key("fk_up_user_id", 'user_profiles', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Reverse operations in opposite order of upgrade
    
    # Helper function to safely drop constraints
    def safe_drop_constraint(constraint_name, table_name, constraint_type='foreignkey'):
        """Drop constraint only if it exists"""
        conn = op.get_bind()
        result = conn.execute(sa.text("""
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = :table_name 
            AND constraint_name = :constraint_name
            AND table_schema = 'public';
        """), {'table_name': table_name, 'constraint_name': constraint_name})
        
        if result.fetchone():
            op.drop_constraint(constraint_name, table_name, type_=constraint_type)
    
    # Helper function to safely drop indexes
    def safe_drop_index(index_name, table_name):
        """Drop index only if it exists"""
        conn = op.get_bind()
        result = conn.execute(sa.text("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = :table_name 
            AND indexname = :index_name
            AND schemaname = 'public';
        """), {'table_name': table_name, 'index_name': index_name})
        
        if result.fetchone():
            op.drop_index(index_name, table_name=table_name)
    
    # Revert user_profiles table changes
    safe_drop_constraint("fk_up_user_id", 'user_profiles')
    
    # Check if user_id column is GUID type before reverting
    conn = op.get_bind()
    result = conn.execute(sa.text("""
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'user_id'
        AND table_schema = 'public';
    """))
    column_info = result.fetchone()
    
    if column_info and column_info[0] == 'uuid':
        op.alter_column('user_profiles', 'user_id',
                   existing_type=models.GUID(),
                   type_=sa.VARCHAR(),
                   existing_nullable=False)
    
    # Revert user_question_responses table changes
    safe_drop_constraint("fk_uqr_user_id", 'user_question_responses')
    
    # Check if user_id column is GUID type before reverting
    result = conn.execute(sa.text("""
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'user_question_responses' 
        AND column_name = 'user_id'
        AND table_schema = 'public';
    """))
    column_info = result.fetchone()
    
    if column_info and column_info[0] == 'uuid':
        op.alter_column('user_question_responses', 'user_id',
                   existing_type=models.GUID(),
                   type_=sa.VARCHAR(),
                   existing_nullable=True)
    
    # Check if columns exist before dropping them
    result = conn.execute(sa.text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'user_question_responses' 
        AND column_name IN ('created_at', 'updated_at')
        AND table_schema = 'public';
    """))
    existing_columns = [row[0] for row in result.fetchall()]
    
    if 'updated_at' in existing_columns:
        op.drop_column('user_question_responses', 'updated_at')
    if 'created_at' in existing_columns:
        op.drop_column('user_question_responses', 'created_at')
    
    # Remove indices and foreign keys for user tables
    safe_drop_constraint("fk_up_rr_id", 'user_procedures')
    safe_drop_index('ix_user_procedures_record_id', 'user_procedures')
    
    safe_drop_constraint("fk_ug_rr_id", 'user_genetics')
    safe_drop_index('ix_user_genetics_record_id', 'user_genetics')
    
    safe_drop_constraint("fk_ud_rr_id", 'user_diagnoses')
    safe_drop_index('ix_user_diagnoses_record_id', 'user_diagnoses')
    
    # Revert clinical_data table changes
    safe_drop_constraint("fk_cd_user_id", 'clinical_data')
    safe_drop_constraint("fk_cd_rr_id", 'clinical_data')
    
    # Check column types before reverting
    result = conn.execute(sa.text("""
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'clinical_data' 
        AND column_name = 'user_id'
        AND table_schema = 'public';
    """))
    column_info = result.fetchone()
    
    if column_info and column_info[0] == 'uuid':
        op.alter_column('clinical_data', 'user_id',
                   existing_type=models.GUID(),
                   type_=sa.VARCHAR(),
                   existing_nullable=True)
    
    op.alter_column('clinical_data', 'record_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    
    # Revert report_gen_requests table changes
    safe_drop_constraint("fk_rgr_user_id", 'report_gen_requests')
    
    # Check column type before reverting
    result = conn.execute(sa.text("""
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'report_gen_requests' 
        AND column_name = 'user_id'
        AND table_schema = 'public';
    """))
    column_info = result.fetchone()
    
    if column_info and column_info[0] == 'uuid':
        op.alter_column('report_gen_requests', 'user_id',
                   existing_type=models.GUID(),
                   type_=sa.VARCHAR(),
                   existing_nullable=True)
    
    # Revert record_requests table changes
    safe_drop_constraint("fk_rr_user_id", 'record_requests')
    
    # Check column type before reverting
    result = conn.execute(sa.text("""
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'record_requests' 
        AND column_name = 'user_id'
        AND table_schema = 'public';
    """))
    column_info = result.fetchone()
    
    if column_info and column_info[0] == 'uuid':
        op.alter_column('record_requests', 'user_id',
                   existing_type=models.GUID(),
                   type_=sa.VARCHAR(),
                   existing_nullable=True)
    
    # ### end Alembic commands ###
