"""added support for canonical

Revision ID: d809d16ac357
Revises: 00e1bde9bd69
Create Date: 2025-07-14 16:16:33.294313

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = 'd809d16ac357'
down_revision: Union[str, None] = '00e1bde9bd69'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('canonical_biomarkers',
    sa.Column('id', models.GUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('canonical_name', sa.String(), nullable=False),
    sa.Column('standard_unit', sa.String(), nullable=True),
    sa.Column('reference_range_min', sa.Float(), nullable=True),
    sa.Column('reference_range_max', sa.Float(), nullable=True),
    sa.Column('health_areas', models.ArrayOfText(), nullable=True),
    sa.Column('group_name', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_biomarkers',
    sa.Column('id', models.GUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('record_id', sa.String(), nullable=False),
    sa.Column('user_id', models.GUID(), nullable=True),
    sa.Column('test_name', sa.String(), nullable=True),
    sa.Column('expanded_test_name', sa.String(), nullable=True),
    sa.Column('result', sa.String(), nullable=True),
    sa.Column('reference_range', sa.String(), nullable=True),
    sa.Column('unit', sa.String(), nullable=True),
    sa.Column('context', sa.Text(), nullable=True),
    sa.Column('canonical_id', models.GUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.Column('edited', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('excluded', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('test_date', sa.DateTime(), nullable=True),
    sa.Column('sample_source', sa.String(), nullable=True),
    sa.Column('method', sa.String(), nullable=True),
    sa.Column('additional_data', models.JSONVariant(), nullable=True),
    sa.Column('page_number', sa.Integer(), nullable=True),
    sa.Column('result_numeric', sa.Float(), nullable=True),
    sa.Column('reference_range_min', sa.Float(), nullable=True),
    sa.Column('reference_range_max', sa.Float(), nullable=True),
    sa.Column('out_of_range', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['canonical_id'], ['canonical_biomarkers.id'], ),
    sa.ForeignKeyConstraint(['record_id'], ['record_requests.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_biomarkers_additional_data'), 'user_biomarkers', ['additional_data'], unique=False)
    op.create_index(op.f('ix_user_biomarkers_record_id'), 'user_biomarkers', ['record_id'], unique=False)
    op.create_index(op.f('ix_user_biomarkers_user_id'), 'user_biomarkers', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_biomarkers_user_id'), table_name='user_biomarkers')
    op.drop_index(op.f('ix_user_biomarkers_record_id'), table_name='user_biomarkers')
    op.drop_index(op.f('ix_user_biomarkers_additional_data'), table_name='user_biomarkers')
    op.drop_table('user_biomarkers')
    op.drop_table('canonical_biomarkers')
    # ### end Alembic commands ###
