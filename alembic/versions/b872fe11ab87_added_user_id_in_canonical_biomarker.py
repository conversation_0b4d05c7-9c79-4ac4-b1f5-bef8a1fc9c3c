"""added user_id in canonical_biomarker

Revision ID: b872fe11ab87
Revises: f2e6d5d3feed
Create Date: 2025-07-16 13:00:07.456423

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = 'b872fe11ab87'
down_revision: Union[str, None] = 'f2e6d5d3feed'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('canonical_biomarkers', sa.Column('user_id', models.GUID(), nullable=False))
    op.create_index(op.f('ix_canonical_biomarkers_user_id'), 'canonical_biomarkers', ['user_id'], unique=False)
    op.create_foreign_key(None, 'canonical_biomarkers', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by <PERSON><PERSON>bic - please adjust! ###
    op.drop_constraint(None, 'canonical_biomarkers', type_='foreignkey')
    op.drop_index(op.f('ix_canonical_biomarkers_user_id'), table_name='canonical_biomarkers')
    op.drop_column('canonical_biomarkers', 'user_id')
    # ### end Alembic commands ###
