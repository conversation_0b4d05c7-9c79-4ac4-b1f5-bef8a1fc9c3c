"""added_example_units_to_loinc

Revision ID: 777ac7102ed0
Revises: 7b6b7dd520ab
Create Date: 2025-06-11 20:50:38.073801

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = '777ac7102ed0'
down_revision: Union[str, None] = '7b6b7dd520ab'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('loinc_records', sa.Column('example_units', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('loinc_records', 'example_units')
    # ### end Alembic commands ###
