"""populate_example_units_in_loinc

Revision ID: fd91fbe762a0
Revises: 777ac7102ed0
Create Date: 2025-06-12 14:02:09.034627

"""
from typing import Sequence, Union
import os
from pathlib import Path

from alembic import op
import sqlalchemy as sa
import models
import pandas as pd
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision: str = 'fd91fbe762a0'
down_revision: Union[str, None] = '777ac7102ed0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    """Populate example_units from CSV."""
    connection = op.get_bind()
    
    try:
        # Get the path to the CSV file in the main repo
        # From alembic/versions/ go up two levels to reach the main repo
        csv_path = Path(__file__).parent.parent.parent / "Loinc.csv"
        
        # Alternative approach using os.path
        # current_dir = os.path.dirname(os.path.abspath(__file__))
        # csv_path = os.path.join(current_dir, "..", "..", "Loinc.csv")
        
        if not csv_path.exists():
            raise FileNotFoundError(f"CSV file not found at: {csv_path}")
        
        print(f"Reading CSV from: {csv_path}")
        
        # Read CSV
        df = pd.read_csv(csv_path)
        update_df = df[['loinc_num', 'example_units']].copy()
        update_df['loinc_num'] = update_df['loinc_num'].astype(str)
        
        # Filter out rows where example_units is null/empty
        update_df = update_df.dropna(subset=['example_units'])
        update_df = update_df[update_df['example_units'] != '']
        
        if len(update_df) == 0:
            print("No records with example_units found in CSV")
            return
        
        print(f"Found {len(update_df)} records with example_units to update")
        
        # Bulk update using temporary table
        update_df.to_sql('temp_loinc_updates', connection, if_exists='replace', index=False)
        
        result = connection.execute(text("""
            UPDATE loinc_records 
            SET example_units = temp_updates.example_units 
            FROM temp_loinc_updates temp_updates
            WHERE loinc_records.loinc_num = temp_updates.loinc_num
            AND temp_updates.example_units IS NOT NULL
            AND temp_updates.example_units != ''
        """))
        
        print(f"Updated {result.rowcount} records")
        
        # Cleanup
        connection.execute(text("DROP TABLE temp_loinc_updates"))
        
    except Exception as e:
        print(f"Error: {e}")
        # Ensure cleanup even on error
        try:
            connection.execute(text("DROP TABLE IF EXISTS temp_loinc_updates"))
        except:
            pass
        raise

def downgrade() -> None:
    """Clear example_units data."""
    connection = op.get_bind()
    connection.execute(text("UPDATE loinc_records SET example_units = NULL"))