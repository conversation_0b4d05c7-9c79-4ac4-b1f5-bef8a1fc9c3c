"""Initial migration for N1 API

Revision ID: 90d53b15354d
Revises:
Create Date: 2025-05-06 13:17:32.845223

"""
from typing import Sequence, Union
import uuid

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.sql import expression
from models import JsonText, ArrayOfText, GUID, UtcNow

# revision identifiers, used by Alembic.
revision: str = '90d53b15354d'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# For PostgreSQL, we'll use gen_random_uuid() directly
def uuid_default():
    """Return a UUID default value for PostgreSQL."""
    return sa.text("gen_random_uuid()")

def upgrade() -> None:
    """Create initial schema."""
    op.create_table(
        'users',
        sa.Column('id', GUID, primary_key=True, server_default=uuid_default()),
        sa.Column('bubble_id', sa.String(), nullable=True, unique=True, index=True),
        sa.Column('email', sa.String(), nullable=True, index=True),
        sa.Column('develop_mode', sa.Boolean(), server_default=sa.text('false'), nullable=True),
        sa.Column('bucket_name', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=UtcNow(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=UtcNow(), onupdate=UtcNow(), nullable=True),
    )

    op.create_table(
        'record_requests',
        sa.Column('id', sa.String(), primary_key=True, index=True),
        sa.Column('user_id', sa.String(), nullable=True, index=True),
        sa.Column('url', sa.String(), nullable=True),
        sa.Column('file_name', sa.String(), nullable=True),
        sa.Column('type', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True, default=0),
        sa.Column('config', sa.Text(), nullable=True),
        sa.Column('batch_id', sa.String(), nullable=True),
        sa.Column('error', sa.Text(), nullable=True),
        sa.Column('message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=UtcNow(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=UtcNow(), onupdate=UtcNow(), nullable=True),
    )

    op.create_table(
        'report_gen_requests',
        sa.Column('id', sa.String(), primary_key=True, index=True),
        sa.Column('user_id', sa.String(), nullable=True, index=True),
        sa.Column('progress', sa.Integer(), nullable=True, default=0),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('url', sa.String(), nullable=True),
        sa.Column('file_name', sa.String(), nullable=True),
        sa.Column('config', sa.JSON, nullable=True),
        sa.Column('message', sa.Text(), nullable=True),
        sa.Column('error', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=UtcNow(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=UtcNow(), onupdate=UtcNow(), nullable=True),
    )

    op.create_table(
        'request_stages',
        sa.Column('id', GUID, primary_key=True, server_default=uuid_default()),
        sa.Column('request_id', sa.String(), nullable=True, index=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True, default=0),
        sa.Column('details', sa.Text(), nullable=True),
        sa.Column('error', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=UtcNow(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=UtcNow(), onupdate=UtcNow(), nullable=True),
    )

    op.create_table(
        'clinical_data',
        sa.Column('id', GUID, primary_key=True, server_default=uuid_default()),
        sa.Column('record_id', sa.String(), nullable=True, index=True),
        sa.Column('user_id', sa.String(), nullable=True, index=True),
        sa.Column('day', sa.Integer(), nullable=True),
        sa.Column('year', sa.Integer(), nullable=True),
        sa.Column('month', sa.Integer(), nullable=True),
        sa.Column('test_name', sa.String(), nullable=True),
        sa.Column('result', sa.String(), nullable=True),
        sa.Column('reference_range', sa.String(), nullable=True),
        sa.Column('unit', sa.String(), nullable=True),
        sa.Column('filename', sa.String(), nullable=True),
        sa.Column('context', sa.Text(), nullable=True),
        sa.Column('loinc_standard_name', sa.String(), nullable=True),
        sa.Column('loinc_code', sa.String(), nullable=True),
        sa.Column('health_areas', ArrayOfText(), nullable=True),
        sa.Column('edited', sa.Boolean(), server_default='0', nullable=True),
        sa.Column('excluded', sa.Boolean(), server_default='0', nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=UtcNow(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=UtcNow(), onupdate=UtcNow(), nullable=True),
    )

    op.create_table(
        'loinc_records',
        sa.Column('loinc_num', sa.String(), primary_key=True),
        sa.Column('component', sa.String(), nullable=True),
        sa.Column('property', sa.String(), nullable=True),
        sa.Column('time_aspct', sa.String(), nullable=True),
        sa.Column('system', sa.String(), nullable=True),
        sa.Column('scale_typ', sa.String(), nullable=True),
        sa.Column('method_typ', sa.String(), nullable=True),
        sa.Column('class_', sa.String(), nullable=True),
        sa.Column('classtype', sa.Integer(), nullable=True),
        sa.Column('long_common_name', sa.String(), nullable=True),
        sa.Column('shortname', sa.String(), nullable=True),
        sa.Column('external_copyright_notice', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('version_first_released', sa.String(), nullable=True),
        sa.Column('version_last_changed', sa.String(), nullable=True),
    )

    op.create_table(
        'user_question_responses',
        sa.Column('id', GUID, primary_key=True, server_default=uuid_default()),
        sa.Column('user_id', sa.String(), nullable=True, index=True),
        sa.Column('question', sa.String(), nullable=True, index=True),
        sa.Column('answer', sa.String(), nullable=True),
        sa.Column('created_date', sa.DateTime(), server_default=UtcNow(), nullable=True),
        sa.Column('related_date', sa.DateTime(), server_default=UtcNow(), onupdate=UtcNow(), nullable=True),
        sa.Column('category', sa.String(), nullable=True),
    )

    op.create_table(
        'user_profiles',
        sa.Column('user_id', sa.String(), primary_key=True, index=True),
        sa.Column('first_name', sa.String(), nullable=True),
        sa.Column('last_name', sa.String(), nullable=True),
        sa.Column('dob', sa.DateTime(), nullable=True),
        sa.Column('weight_in_kg', sa.Float(), nullable=True),
        sa.Column('height_in_cm', sa.Float(), nullable=True),
        sa.Column('primary_health_concern', sa.String(), nullable=True),
        sa.Column('gender', sa.String(), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_profiles')
    op.drop_table('user_question_responses')
    op.drop_table('loinc_records')
    op.drop_table('clinical_data')
    op.drop_table('request_stages')
    op.drop_table('report_gen_requests')
    op.drop_table('record_requests')
    op.drop_table('users')
    # ### end Alembic commands ###
