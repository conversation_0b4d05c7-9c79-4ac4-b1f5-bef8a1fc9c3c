"""add_record_version

Revision ID: 24415543ad38
Revises: ec91e2ab267e
Create Date: 2025-05-07 22:04:53.200386

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '24415543ad38'
down_revision: Union[str, None] = 'ec91e2ab267e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column('record_requests', sa.Column('version', sa.String(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""        
    op.drop_column('record_requests', 'version')
