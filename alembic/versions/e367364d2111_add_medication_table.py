"""add_medication_table

Revision ID: e367364d2111
Revises: 24415543ad38
Create Date: 2025-05-12 09:58:35.318063

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models

# revision identifiers, used by Alembic.
revision: str = 'e367364d2111'
down_revision: Union[str, None] = '24415543ad38'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_medications',
    sa.Column('id', models.GUID(), server_default=models.uuid_default(), nullable=False),
    sa.Column('user_id', models.GUID(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('brand_name', sa.String(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('dosage', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('started_from', sa.DateTime(), nullable=False),
    sa.Column('stopped_on', sa.DateTime(), nullable=True),
    sa.Column('frequency', sa.String(), nullable=False),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=models.UtcNow(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=models.UtcNow(), onupdate=models.UtcNow(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_medications_user_id'), 'user_medications', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_medications_user_id'), table_name='user_medications')
    op.drop_table('user_medications')
    # ### end Alembic commands ###
