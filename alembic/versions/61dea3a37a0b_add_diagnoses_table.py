"""add_diagnoses_table

Revision ID: 61dea3a37a0b
Revises: e367364d2111
Create Date: 2025-05-22 22:15:39.998366

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models


# revision identifiers, used by Alembic.
revision: str = '61dea3a37a0b'
down_revision: Union[str, None] = 'e367364d2111'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_diagnoses',
    sa.Column('id', models.GUID(), server_default=sa.text('(gen_random_uuid())'), nullable=False),
    sa.Column('user_id', models.GUID(), nullable=True),
    sa.Column('record_id', sa.String(), nullable=False),
    sa.Column('page_number', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('icd_code', sa.String(), nullable=True),
    sa.Column('snomed_code', sa.String(), nullable=True),
    sa.Column('date_diagnosed', sa.DateTime(), nullable=False),
    sa.Column('date_resolved', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('explanation', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=models.UtcNow(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=models.UtcNow(), onupdate=models.UtcNow(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_diagnoses_user_id'), 'user_diagnoses', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_diagnoses_user_id'), table_name='user_diagnoses')
    op.drop_table('user_diagnoses')
    # ### end Alembic commands ###
