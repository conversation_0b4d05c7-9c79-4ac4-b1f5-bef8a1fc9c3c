"""create cascade delete for user_biomarkers

Revision ID: 9c485a383ee7
Revises: b872fe11ab87
Create Date: 2025-07-17 13:39:19.810975

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = '9c485a383ee7'
down_revision: Union[str, None] = 'b872fe11ab87'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_biomarkers_record_id_fkey', 'user_biomarkers', type_='foreignkey')
    op.create_foreign_key(None, 'user_biomarkers', 'record_requests', ['record_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_biomarkers', type_='foreignkey')
    op.create_foreign_key('user_biomarkers_record_id_fkey', 'user_biomarkers', 'record_requests', ['record_id'], ['id'])
    # ### end Alembic commands ###
