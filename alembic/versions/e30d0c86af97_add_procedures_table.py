"""add_procedures_table

Revision ID: e30d0c86af97
Revises: 61dea3a37a0b
Create Date: 2025-05-27 08:55:37.344649

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e30d0c86af97'
down_revision: Union[str, None] = '61dea3a37a0b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_procedures',
    sa.Column('id', models.GUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('user_id', models.GUID(), nullable=True),
    sa.Column('record_id', sa.String(), nullable=False),
    sa.Column('page_number', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('cpt_code', sa.String(), nullable=True),
    sa.Column('date_performed', sa.DateTime(), nullable=False),
    sa.Column('outcome', sa.Text(), nullable=True),
    sa.Column('explanation', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_procedures_user_id'), 'user_procedures', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f('ix_user_procedures_user_id'), table_name='user_procedures')
    op.drop_table('user_procedures')
    # ### end Alembic commands ###
