"""add_genetics_table

Revision ID: caed3d7e17b3
Revises: e30d0c86af97
Create Date: 2025-05-28 09:24:10.979693

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = 'caed3d7e17b3'
down_revision: Union[str, None] = 'e30d0c86af97'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_genetics',
    sa.Column('id', models.GUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('user_id', models.GUID(), nullable=True),
    sa.Column('record_id', sa.String(), nullable=False),
    sa.Column('page_number', sa.Integer(), nullable=True),
    sa.Column('gene', sa.String(), nullable=False),
    sa.Column('allele', sa.String(), nullable=True),
    sa.Column('variant', sa.String(), nullable=True),
    sa.Column('prevalence', sa.Float(), nullable=True),
    sa.Column('test_date', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_genetics_user_id'), 'user_genetics', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_genetics_user_id'), table_name='user_genetics')
    op.drop_table('user_genetics')
    # ### end Alembic commands ###
