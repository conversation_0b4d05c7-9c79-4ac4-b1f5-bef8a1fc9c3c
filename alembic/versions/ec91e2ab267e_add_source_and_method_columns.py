"""add_source_and_method_columns

Revision ID: ec91e2ab267e
Revises: 90d53b15354d
Create Date: 2025-05-07 06:40:03.774511

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ec91e2ab267e'
down_revision: Union[str, None] = '90d53b15354d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clinical_data', sa.Column('sample_source', sa.String(), nullable=True))
    op.add_column('clinical_data', sa.Column('method', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clinical_data', 'method')
    op.drop_column('clinical_data', 'sample_source')
    # ### end Alembic commands ###
