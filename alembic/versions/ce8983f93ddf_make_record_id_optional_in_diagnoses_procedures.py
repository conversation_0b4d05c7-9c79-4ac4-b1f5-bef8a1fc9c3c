"""make_record_id_optional_in_diagnoses_and_procedures

Revision ID: ce8983f93ddf
Revises: 2195da1d4766
Create Date: 2025-06-27 12:31:04.286291

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = 'ce8983f93ddf'
down_revision: Union[str, None] = '2195da1d4766'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Make record_id nullable in user_diagnoses table
    op.alter_column('user_diagnoses', 'record_id',
                    existing_type=sa.String(),
                    nullable=True)

    # Make record_id nullable in user_procedures table
    op.alter_column('user_procedures', 'record_id',
                    existing_type=sa.String(),
                    nullable=True)


def downgrade() -> None:
    """Downgrade schema."""
    # Set a default value for record_id where it is NULL before making it non-nullable
    op.execute("""
    UPDATE {table_name}
    SET record_id = 'default_value'
    WHERE record_id IS NULL;
    """.format(table_name='user_procedures'))

    # Make record_id non-nullable in user_procedures table
    op.alter_column('user_procedures', 'record_id',
                    existing_type=sa.String(),
                    nullable=False)

    # Set a default value for record_id where it is NULL before making it non-nullable
    op.execute("""
        UPDATE {table_name}
        SET record_id = 'default_value'
        WHERE record_id IS NULL;
        """.format(table_name='user_diagnoses'))

    # Make record_id non-nullable in user_diagnoses table
    op.alter_column('user_diagnoses', 'record_id',
                    existing_type=sa.String(),
                    nullable=False)
