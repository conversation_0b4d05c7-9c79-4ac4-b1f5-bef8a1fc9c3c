"""add_prompts_to_reports

Revision ID: 2195da1d4766
Revises: 2bbf869d43a2
Create Date: 2025-06-20 13:10:53.734749

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = '2195da1d4766'
down_revision: Union[str, None] = '2bbf869d43a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column('report_gen_requests', sa.Column('workflow_name', sa.String(), nullable=True))
    op.add_column('report_gen_requests', sa.Column('workflow_id', sa.String(), nullable=True))
    op.add_column('report_gen_requests', sa.Column('version', sa.String(), nullable=True))
    op.add_column('report_gen_requests', sa.Column('report_language', sa.String(), nullable=True))
    op.add_column('report_gen_requests', sa.Column('prompts', sa.JSON(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_column('report_gen_requests', 'prompts')
    op.drop_column('report_gen_requests', 'report_language')
    op.drop_column('report_gen_requests', 'version')
    op.drop_column('report_gen_requests', 'workflow_id')
    op.drop_column('report_gen_requests', 'workflow_name')
