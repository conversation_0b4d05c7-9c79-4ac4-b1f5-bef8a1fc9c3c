"""add_json_field_to_data_tables

Revision ID: 9ce397b0abd9
Revises: caed3d7e17b3
Create Date: 2025-05-28 12:18:33.228618

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '9ce397b0abd9'
down_revision: Union[str, None] = 'caed3d7e17b3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clinical_data', sa.Column('additional_data', sa.JSON().with_variant(postgresql.JSONB(astext_type=sa.Text()), 'postgresql'), nullable=True))
    op.create_index(op.f('ix_clinical_data_additional_data'), 'clinical_data', ['additional_data'], unique=False)
    op.add_column('user_diagnoses', sa.Column('additional_data', sa.JSON().with_variant(postgresql.JSONB(astext_type=sa.Text()), 'postgresql'), nullable=True))
    op.create_index(op.f('ix_user_diagnoses_additional_data'), 'user_diagnoses', ['additional_data'], unique=False)
    op.add_column('user_genetics', sa.Column('additional_data', sa.JSON().with_variant(postgresql.JSONB(astext_type=sa.Text()), 'postgresql'), nullable=True))
    op.create_index(op.f('ix_user_genetics_additional_data'), 'user_genetics', ['additional_data'], unique=False)
    op.add_column('user_medications', sa.Column('additional_data', sa.JSON().with_variant(postgresql.JSONB(astext_type=sa.Text()), 'postgresql'), nullable=True))
    op.create_index(op.f('ix_user_medications_additional_data'), 'user_medications', ['additional_data'], unique=False)
    op.add_column('user_procedures', sa.Column('additional_data', sa.JSON().with_variant(postgresql.JSONB(astext_type=sa.Text()), 'postgresql'), nullable=True))
    op.create_index(op.f('ix_user_procedures_additional_data'), 'user_procedures', ['additional_data'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_procedures_additional_data'), table_name='user_procedures')
    op.drop_column('user_procedures', 'additional_data')
    op.drop_index(op.f('ix_user_medications_additional_data'), table_name='user_medications')
    op.drop_column('user_medications', 'additional_data')
    op.drop_index(op.f('ix_user_genetics_additional_data'), table_name='user_genetics')
    op.drop_column('user_genetics', 'additional_data')
    op.drop_index(op.f('ix_user_diagnoses_additional_data'), table_name='user_diagnoses')
    op.drop_column('user_diagnoses', 'additional_data')
    op.drop_index(op.f('ix_clinical_data_additional_data'), table_name='clinical_data')
    op.drop_column('clinical_data', 'additional_data')
    # ### end Alembic commands ###
