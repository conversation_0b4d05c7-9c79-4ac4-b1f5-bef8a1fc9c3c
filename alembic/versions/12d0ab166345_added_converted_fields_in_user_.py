"""added converted fields in user_biomarkers

Revision ID: 12d0ab166345
Revises: d809d16ac357
Create Date: 2025-07-15 12:16:26.392322

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = '12d0ab166345'
down_revision: Union[str, None] = 'd809d16ac357'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_biomarkers', sa.Column('converted_result_numeric', sa.Float(), nullable=True))
    op.add_column('user_biomarkers', sa.Column('converted_reference_range_min', sa.Float(), nullable=True))
    op.add_column('user_biomarkers', sa.Column('converted_reference_range_max', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_biomarkers', 'converted_reference_range_max')
    op.drop_column('user_biomarkers', 'converted_reference_range_min')
    op.drop_column('user_biomarkers', 'converted_result_numeric')
    # ### end Alembic commands ###
