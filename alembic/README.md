# N1 API Database Migration Guide

This document provides instructions for managing database migrations using Alembic in the N1 API project.

## Overview

The N1 API uses Alembic for database schema migrations. Alembic tracks changes to the database schema over time, allowing for:
- Version-controlled database schema changes
- Automatic generation of migration scripts based on SQLAlchemy model changes
- Ability to upgrade or downgrade the database schema to specific versions

## Prerequisites

Before working with migrations, ensure:
1. The `DATABASE_URL` environment variable is set (see `env.sh.example`)
2. PostgreSQL is running and accessible
3. You have the necessary permissions to modify the database schema

## Common Commands

### Initialize Migration Environment (already done)
```bash
alembic init alembic
```

### Create a New Migration

To create a new migration manually:
```bash
alembic revision -m "description_of_changes"
```

To auto-generate a migration based on model changes:
```bash
alembic revision --autogenerate -m "description_of_changes"
```

### Apply Migrations

To upgrade to the latest version:
```bash
alembic upgrade head
```

To upgrade to a specific version:
```bash
alembic upgrade <revision_id>
```

To upgrade by a relative number of versions:
```bash
alembic upgrade +1
```

### Downgrade Migrations

To downgrade to the previous version:
```bash
alembic downgrade -1
```

To downgrade to a specific version:
```bash
alembic downgrade <revision_id>
```

To downgrade to the base (remove all migrations):
```bash
alembic downgrade base
```

### View Migration Information

To see current migration version:
```bash
alembic current
```

To see migration history:
```bash
alembic history
```

To see migration history with more details:
```bash
alembic history -v
```

## Project-Specific Configuration

- The database URL is configured from the `DATABASE_URL` environment variable in `alembic/env.py`
- SQLAlchemy models are imported from `database.py` and `models.py`
- Migration scripts are stored in `alembic/versions/`

## Best Practices

1. **Always test migrations**: Before applying to production, test migrations on a staging environment
2. **Keep migrations small and focused**: Each migration should make a specific, well-defined change
3. **Include both upgrade and downgrade paths**: Ensure each migration can be reversed
4. **Use meaningful migration names**: The migration description should clearly indicate what changes are being made
5. **Review auto-generated migrations**: Auto-generated migrations may need manual adjustments
6. **Commit migrations to version control**: Migration scripts should be tracked in git

## Troubleshooting

### Migration conflicts
If you encounter conflicts between migrations:
1. Ensure you're working with the latest codebase
2. Run `alembic heads` to identify multiple head revisions
3. Create a merge migration: `alembic merge -m "merge_heads" <revision1> <revision2>`

### Failed migrations
If a migration fails:
1. Check the error message for specific issues
2. Fix the migration script or database issue
3. If necessary, downgrade to a previous working version
4. Apply the fixed migration

## Database Models

The N1 API includes the following main models:
- `UserORM`: User accounts and authentication
- `UserRecordRequestORM`: Record processing requests
- `UserReportGenRequestORM`: Report generation requests
- `UserRequestStageORM`: Processing stages for requests
- `ClinicalDataORM`: Clinical data records
- `LoincRecord`: LOINC code reference data
- `UserQuestionResponseORM`: User responses to health questions
- `UserProfileORM`: User profile information

See `models.py` for complete model definitions.
